{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ب\", \"ك\"],\n  abbreviated: [\"ب\", \"ك\"],\n  wide: [\"مىيلادىدىن بۇرۇن\", \"مىيلادىدىن كىيىن\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1\", \"2\", \"3\", \"4\"],\n  wide: [\"بىرىنجى چارەك\", \"ئىككىنجى چارەك\", \"ئۈچىنجى چارەك\", \"تۆتىنجى چارەك\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ي\", \"ف\", \"م\", \"ا\", \"م\", \"ى\", \"ى\", \"ا\", \"س\", \"ۆ\", \"ن\", \"د\"],\n  abbreviated: [\"يانۋار\", \"فېۋىرال\", \"مارت\", \"ئاپرىل\", \"ماي\", \"ئىيۇن\", \"ئىيول\", \"ئاۋغۇست\", \"سىنتەبىر\", \"ئۆكتەبىر\", \"نويابىر\", \"دىكابىر\"],\n  wide: [\"يانۋار\", \"فېۋىرال\", \"مارت\", \"ئاپرىل\", \"ماي\", \"ئىيۇن\", \"ئىيول\", \"ئاۋغۇست\", \"سىنتەبىر\", \"ئۆكتەبىر\", \"نويابىر\", \"دىكابىر\"]\n};\nconst dayValues = {\n  narrow: [\"ي\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  short: [\"ي\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  abbreviated: [\"يەكشەنبە\", \"دۈشەنبە\", \"سەيشەنبە\", \"چارشەنبە\", \"پەيشەنبە\", \"جۈمە\", \"شەنبە\"],\n  wide: [\"يەكشەنبە\", \"دۈشەنبە\", \"سەيشەنبە\", \"چارشەنبە\", \"پەيشەنبە\", \"جۈمە\", \"شەنبە\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\"\n  },\n  abbreviated: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\"\n  },\n  wide: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\"\n  },\n  abbreviated: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\"\n  },\n  wide: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}