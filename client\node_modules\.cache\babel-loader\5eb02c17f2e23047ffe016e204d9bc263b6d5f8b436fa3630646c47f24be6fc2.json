{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    regular: {\n      one: \"1 секундтан аз\",\n      singularNominative: \"{{count}} секундтан аз\",\n      singularGenitive: \"{{count}} секундтан аз\",\n      pluralGenitive: \"{{count}} секундтан аз\"\n    },\n    future: {\n      one: \"бір секундтан кейін\",\n      singularNominative: \"{{count}} секундтан кейін\",\n      singularGenitive: \"{{count}} секундтан кейін\",\n      pluralGenitive: \"{{count}} секундтан кейін\"\n    }\n  },\n  xSeconds: {\n    regular: {\n      singularNominative: \"{{count}} секунд\",\n      singularGenitive: \"{{count}} секунд\",\n      pluralGenitive: \"{{count}} секунд\"\n    },\n    past: {\n      singularNominative: \"{{count}} секунд бұрын\",\n      singularGenitive: \"{{count}} секунд бұрын\",\n      pluralGenitive: \"{{count}} секунд бұрын\"\n    },\n    future: {\n      singularNominative: \"{{count}} секундтан кейін\",\n      singularGenitive: \"{{count}} секундтан кейін\",\n      pluralGenitive: \"{{count}} секундтан кейін\"\n    }\n  },\n  halfAMinute: options => {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return \"жарты минут ішінде\";\n      } else {\n        return \"жарты минут бұрын\";\n      }\n    }\n    return \"жарты минут\";\n  },\n  lessThanXMinutes: {\n    regular: {\n      one: \"1 минуттан аз\",\n      singularNominative: \"{{count}} минуттан аз\",\n      singularGenitive: \"{{count}} минуттан аз\",\n      pluralGenitive: \"{{count}} минуттан аз\"\n    },\n    future: {\n      one: \"минуттан кем \",\n      singularNominative: \"{{count}} минуттан кем\",\n      singularGenitive: \"{{count}} минуттан кем\",\n      pluralGenitive: \"{{count}} минуттан кем\"\n    }\n  },\n  xMinutes: {\n    regular: {\n      singularNominative: \"{{count}} минут\",\n      singularGenitive: \"{{count}} минут\",\n      pluralGenitive: \"{{count}} минут\"\n    },\n    past: {\n      singularNominative: \"{{count}} минут бұрын\",\n      singularGenitive: \"{{count}} минут бұрын\",\n      pluralGenitive: \"{{count}} минут бұрын\"\n    },\n    future: {\n      singularNominative: \"{{count}} минуттан кейін\",\n      singularGenitive: \"{{count}} минуттан кейін\",\n      pluralGenitive: \"{{count}} минуттан кейін\"\n    }\n  },\n  aboutXHours: {\n    regular: {\n      singularNominative: \"шамамен {{count}} сағат\",\n      singularGenitive: \"шамамен {{count}} сағат\",\n      pluralGenitive: \"шамамен {{count}} сағат\"\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} сағаттан кейін\",\n      singularGenitive: \"шамамен {{count}} сағаттан кейін\",\n      pluralGenitive: \"шамамен {{count}} сағаттан кейін\"\n    }\n  },\n  xHours: {\n    regular: {\n      singularNominative: \"{{count}} сағат\",\n      singularGenitive: \"{{count}} сағат\",\n      pluralGenitive: \"{{count}} сағат\"\n    }\n  },\n  xDays: {\n    regular: {\n      singularNominative: \"{{count}} күн\",\n      singularGenitive: \"{{count}} күн\",\n      pluralGenitive: \"{{count}} күн\"\n    },\n    future: {\n      singularNominative: \"{{count}} күннен кейін\",\n      singularGenitive: \"{{count}} күннен кейін\",\n      pluralGenitive: \"{{count}} күннен кейін\"\n    }\n  },\n  aboutXWeeks: {\n    type: \"weeks\",\n    one: \"шамамен 1 апта\",\n    other: \"шамамен {{count}} апта\"\n  },\n  xWeeks: {\n    type: \"weeks\",\n    one: \"1 апта\",\n    other: \"{{count}} апта\"\n  },\n  aboutXMonths: {\n    regular: {\n      singularNominative: \"шамамен {{count}} ай\",\n      singularGenitive: \"шамамен {{count}} ай\",\n      pluralGenitive: \"шамамен {{count}} ай\"\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} айдан кейін\",\n      singularGenitive: \"шамамен {{count}} айдан кейін\",\n      pluralGenitive: \"шамамен {{count}} айдан кейін\"\n    }\n  },\n  xMonths: {\n    regular: {\n      singularNominative: \"{{count}} ай\",\n      singularGenitive: \"{{count}} ай\",\n      pluralGenitive: \"{{count}} ай\"\n    }\n  },\n  aboutXYears: {\n    regular: {\n      singularNominative: \"шамамен {{count}} жыл\",\n      singularGenitive: \"шамамен {{count}} жыл\",\n      pluralGenitive: \"шамамен {{count}} жыл\"\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} жылдан кейін\",\n      singularGenitive: \"шамамен {{count}} жылдан кейін\",\n      pluralGenitive: \"шамамен {{count}} жылдан кейін\"\n    }\n  },\n  xYears: {\n    regular: {\n      singularNominative: \"{{count}} жыл\",\n      singularGenitive: \"{{count}} жыл\",\n      pluralGenitive: \"{{count}} жыл\"\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан кейін\",\n      singularGenitive: \"{{count}} жылдан кейін\",\n      pluralGenitive: \"{{count}} жылдан кейін\"\n    }\n  },\n  overXYears: {\n    regular: {\n      singularNominative: \"{{count}} жылдан астам\",\n      singularGenitive: \"{{count}} жылдан астам\",\n      pluralGenitive: \"{{count}} жылдан астам\"\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан астам\",\n      singularGenitive: \"{{count}} жылдан астам\",\n      pluralGenitive: \"{{count}} жылдан астам\"\n    }\n  },\n  almostXYears: {\n    regular: {\n      singularNominative: \"{{count}} жылға жақын\",\n      singularGenitive: \"{{count}} жылға жақын\",\n      pluralGenitive: \"{{count}} жылға жақын\"\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан кейін\",\n      singularGenitive: \"{{count}} жылдан кейін\",\n      pluralGenitive: \"{{count}} жылдан кейін\"\n    }\n  }\n};\nfunction declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one && count === 1) return scheme.one;\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\nexport const formatDistance = (token, count, options) => {\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"function\") return tokenValue(options);\n  if (tokenValue.type === \"weeks\") {\n    return count === 1 ? tokenValue.one : tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (tokenValue.future) {\n        return declension(tokenValue.future, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" кейін\";\n      }\n    } else {\n      if (tokenValue.past) {\n        return declension(tokenValue.past, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" бұрын\";\n      }\n    }\n  } else {\n    return declension(tokenValue.regular, count);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}