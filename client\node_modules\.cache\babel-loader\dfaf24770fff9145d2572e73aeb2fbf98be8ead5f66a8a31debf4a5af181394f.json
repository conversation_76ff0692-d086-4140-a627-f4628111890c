{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n// Source: https://dsal.uchicago.edu/dictionaries/brown/\n\n// CLDR #1605 - #1608\nconst eraValues = {\n  narrow: [\"క్రీ.పూ.\", \"క్రీ.శ.\"],\n  abbreviated: [\"క్రీ.పూ.\", \"క్రీ.శ.\"],\n  wide: [\"క్రీస్తు పూర్వం\", \"క్రీస్తుశకం\"]\n};\n\n// CLDR #1613 - #1628\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"త్రై1\", \"త్రై2\", \"త్రై3\", \"త్రై4\"],\n  wide: [\"1వ త్రైమాసికం\", \"2వ త్రైమాసికం\", \"3వ త్రైమాసికం\", \"4వ త్రైమాసికం\"]\n};\n\n// CLDR #1637 - #1708\nconst monthValues = {\n  narrow: [\"జ\", \"ఫి\", \"మా\", \"ఏ\", \"మే\", \"జూ\", \"జు\", \"ఆ\", \"సె\", \"అ\", \"న\", \"డి\"],\n  abbreviated: [\"జన\", \"ఫిబ్ర\", \"మార్చి\", \"ఏప్రి\", \"మే\", \"జూన్\", \"జులై\", \"ఆగ\", \"సెప్టెం\", \"అక్టో\", \"నవం\", \"డిసెం\"],\n  wide: [\"జనవరి\", \"ఫిబ్రవరి\", \"మార్చి\", \"ఏప్రిల్\", \"మే\", \"జూన్\", \"జులై\", \"ఆగస్టు\", \"సెప్టెంబర్\", \"అక్టోబర్\", \"నవంబర్\", \"డిసెంబర్\"]\n};\n\n// CLDR #1709 - #1764\nconst dayValues = {\n  narrow: [\"ఆ\", \"సో\", \"మ\", \"బు\", \"గు\", \"శు\", \"శ\"],\n  short: [\"ఆది\", \"సోమ\", \"మంగళ\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"],\n  abbreviated: [\"ఆది\", \"సోమ\", \"మంగళ\", \"బుధ\", \"గురు\", \"శుక్ర\", \"శని\"],\n  wide: [\"ఆదివారం\", \"సోమవారం\", \"మంగళవారం\", \"బుధవారం\", \"గురువారం\", \"శుక్రవారం\", \"శనివారం\"]\n};\n\n// CLDR #1767 - #1806\nconst dayPeriodValues = {\n  narrow: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  },\n  abbreviated: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  },\n  wide: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  },\n  abbreviated: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  },\n  wide: {\n    am: \"పూర్వాహ్నం\",\n    pm: \"అపరాహ్నం\",\n    midnight: \"అర్ధరాత్రి\",\n    noon: \"మిట్టమధ్యాహ్నం\",\n    morning: \"ఉదయం\",\n    afternoon: \"మధ్యాహ్నం\",\n    evening: \"సాయంత్రం\",\n    night: \"రాత్రి\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"వ\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}