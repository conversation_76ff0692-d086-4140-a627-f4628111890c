{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1초 미만\",\n    other: \"{{count}}초 미만\"\n  },\n  xSeconds: {\n    one: \"1초\",\n    other: \"{{count}}초\"\n  },\n  halfAMinute: \"30초\",\n  lessThanXMinutes: {\n    one: \"1분 미만\",\n    other: \"{{count}}분 미만\"\n  },\n  xMinutes: {\n    one: \"1분\",\n    other: \"{{count}}분\"\n  },\n  aboutXHours: {\n    one: \"약 1시간\",\n    other: \"약 {{count}}시간\"\n  },\n  xHours: {\n    one: \"1시간\",\n    other: \"{{count}}시간\"\n  },\n  xDays: {\n    one: \"1일\",\n    other: \"{{count}}일\"\n  },\n  aboutXWeeks: {\n    one: \"약 1주\",\n    other: \"약 {{count}}주\"\n  },\n  xWeeks: {\n    one: \"1주\",\n    other: \"{{count}}주\"\n  },\n  aboutXMonths: {\n    one: \"약 1개월\",\n    other: \"약 {{count}}개월\"\n  },\n  xMonths: {\n    one: \"1개월\",\n    other: \"{{count}}개월\"\n  },\n  aboutXYears: {\n    one: \"약 1년\",\n    other: \"약 {{count}}년\"\n  },\n  xYears: {\n    one: \"1년\",\n    other: \"{{count}}년\"\n  },\n  overXYears: {\n    one: \"1년 이상\",\n    other: \"{{count}}년 이상\"\n  },\n  almostXYears: {\n    one: \"거의 1년\",\n    other: \"거의 {{count}}년\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" 후\";\n    } else {\n      return result + \" 전\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}