{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)\\.?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(o\\.? ?Kr\\.?|m\\.? ?Kr\\.?)/i,\n  abbreviated: /^(o\\.? ?Kr\\.?|m\\.? ?Kr\\.?)/i,\n  wide: /^(ovdal <PERSON>|ovdal min áiggi|maŋ<PERSON><PERSON>|min áigi)/i\n};\nconst parseEraPatterns = {\n  any: [/^o/i, /^m/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](\\.)? kvartála/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^[ogncmsbčj]/i,\n  abbreviated: /^(ođđa|guov|njuk|cuo|mies|geas|suoi|borg|čakč|golg|skáb|juov)\\.?/i,\n  wide: /^(ođđajagemánnu|guovvamánnu|njukčamánnu|cuoŋománnu|miessemánnu|geassemánnu|suoidnemánnu|borgemánnu|čakčamánnu|golggotmánnu|skábmamánnu|juovlamánnu)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^o/i, /^g/i, /^n/i, /^c/i, /^m/i, /^g/i, /^s/i, /^b/i, /^č/i, /^g/i, /^s/i, /^j/i],\n  any: [/^o/i, /^gu/i, /^n/i, /^c/i, /^m/i, /^ge/i, /^su/i, /^b/i, /^č/i, /^go/i, /^sk/i, /^j/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[svmgdbl]/i,\n  short: /^(sotn|vuos|maŋ|gask|duor|bear|láv)/i,\n  abbreviated: /^(sotn|vuos|maŋ|gask|duor|bear|láv)/i,\n  wide: /^(sotnabeaivi|vuossárga|maŋŋebárga|gaskavahkku|duorastat|bearjadat|lávvardat)/i\n};\nconst parseDayPatterns = {\n  any: [/^s/i, /^v/i, /^m/i, /^g/i, /^d/i, /^b/i, /^l/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(gaskaidja|gaskabeaivvi|(på) (iđđes|maŋŋel gaskabeaivvi|eahkes|ihkku)|[ap])/i,\n  any: /^([ap]\\.?\\s?m\\.?|gaskaidja|gaskabeaivvi|(på) (iđđes|maŋŋel gaskabeaivvi|eahkes|ihkku))/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a(\\.?\\s?m\\.?)?$/i,\n    pm: /^p(\\.?\\s?m\\.?)?$/i,\n    midnight: /^gaskai/i,\n    noon: /^gaskab/i,\n    morning: /iđđes/i,\n    afternoon: /maŋŋel gaskabeaivvi/i,\n    evening: /eahkes/i,\n    night: /ihkku/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}