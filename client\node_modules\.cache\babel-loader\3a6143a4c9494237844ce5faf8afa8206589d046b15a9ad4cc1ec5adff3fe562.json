{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nexport class <PERSON><PERSON>ars<PERSON> extends Parser {\n  priority = 140;\n  parse(dateString, token, match) {\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return match.era(dateString, {\n          width: \"abbreviated\"\n        }) || match.era(dateString, {\n          width: \"narrow\"\n        });\n\n      // A, B\n      case \"GGGGG\":\n        return match.era(dateString, {\n          width: \"narrow\"\n        });\n      // <PERSON><PERSON>, Before Christ\n      case \"GGGG\":\n      default:\n        return match.era(dateString, {\n          width: \"wide\"\n        }) || match.era(dateString, {\n          width: \"abbreviated\"\n        }) || match.era(dateString, {\n          width: \"narrow\"\n        });\n    }\n  }\n  set(date, flags, value) {\n    flags.era = value;\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"R\", \"u\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}