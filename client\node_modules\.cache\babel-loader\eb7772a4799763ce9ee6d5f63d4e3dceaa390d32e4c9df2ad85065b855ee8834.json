{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1びょうみまん\",\n    other: \"{{count}}びょうみまん\",\n    oneWithSuffix: \"やく1びょう\",\n    otherWithSuffix: \"やく{{count}}びょう\"\n  },\n  xSeconds: {\n    one: \"1びょう\",\n    other: \"{{count}}びょう\"\n  },\n  halfAMinute: \"30びょう\",\n  lessThanXMinutes: {\n    one: \"1ぷんみまん\",\n    other: \"{{count}}ふんみまん\",\n    oneWithSuffix: \"やく1ぷん\",\n    otherWithSuffix: \"やく{{count}}ふん\"\n  },\n  xMinutes: {\n    one: \"1ぷん\",\n    other: \"{{count}}ふん\"\n  },\n  aboutXHours: {\n    one: \"やく1じかん\",\n    other: \"やく{{count}}じかん\"\n  },\n  xHours: {\n    one: \"1じかん\",\n    other: \"{{count}}じかん\"\n  },\n  xDays: {\n    one: \"1にち\",\n    other: \"{{count}}にち\"\n  },\n  aboutXWeeks: {\n    one: \"やく1しゅうかん\",\n    other: \"やく{{count}}しゅうかん\"\n  },\n  xWeeks: {\n    one: \"1しゅうかん\",\n    other: \"{{count}}しゅうかん\"\n  },\n  aboutXMonths: {\n    one: \"やく1かげつ\",\n    other: \"やく{{count}}かげつ\"\n  },\n  xMonths: {\n    one: \"1かげつ\",\n    other: \"{{count}}かげつ\"\n  },\n  aboutXYears: {\n    one: \"やく1ねん\",\n    other: \"やく{{count}}ねん\"\n  },\n  xYears: {\n    one: \"1ねん\",\n    other: \"{{count}}ねん\"\n  },\n  overXYears: {\n    one: \"1ねんいじょう\",\n    other: \"{{count}}ねんいじょう\"\n  },\n  almostXYears: {\n    one: \"1ねんちかく\",\n    other: \"{{count}}ねんちかく\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options.addSuffix && tokenValue.oneWithSuffix) {\n      result = tokenValue.oneWithSuffix;\n    } else {\n      result = tokenValue.one;\n    }\n  } else {\n    if (options.addSuffix && tokenValue.otherWithSuffix) {\n      result = tokenValue.otherWithSuffix.replace(\"{{count}}\", String(count));\n    } else {\n      result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"あと\";\n    } else {\n      return result + \"まえ\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}