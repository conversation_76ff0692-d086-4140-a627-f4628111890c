{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"f.Kr.\", \"e.Kr.\"],\n  abbreviated: [\"f.Kr.\", \"e.Kr.\"],\n  wide: [\"f<PERSON><PERSON>\", \"e<PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1:a kvartalet\", \"2:a kvartalet\", \"3:e kvartalet\", \"4:e kvartalet\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"jan.\", \"feb.\", \"mars\", \"apr.\", \"maj\", \"juni\", \"juli\", \"aug.\", \"sep.\", \"okt.\", \"nov.\", \"dec.\"],\n  wide: [\"januari\", \"februari\", \"mars\", \"april\", \"maj\", \"juni\", \"juli\", \"augusti\", \"september\", \"oktober\", \"november\", \"december\"]\n};\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"T\", \"O\", \"T\", \"F\", \"L\"],\n  short: [\"sö\", \"må\", \"ti\", \"on\", \"to\", \"fr\", \"lö\"],\n  abbreviated: [\"sön\", \"mån\", \"tis\", \"ons\", \"tors\", \"fre\", \"lör\"],\n  wide: [\"söndag\", \"måndag\", \"tisdag\", \"onsdag\", \"torsdag\", \"fredag\", \"lördag\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sv.html#1888\nconst dayPeriodValues = {\n  narrow: {\n    am: \"fm\",\n    pm: \"em\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"morg.\",\n    afternoon: \"efterm.\",\n    evening: \"kväll\",\n    night: \"natt\"\n  },\n  abbreviated: {\n    am: \"f.m.\",\n    pm: \"e.m.\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"morgon\",\n    afternoon: \"efterm.\",\n    evening: \"kväll\",\n    night: \"natt\"\n  },\n  wide: {\n    am: \"förmiddag\",\n    pm: \"eftermiddag\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"morgon\",\n    afternoon: \"eftermiddag\",\n    evening: \"kväll\",\n    night: \"natt\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"fm\",\n    pm: \"em\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"på morg.\",\n    afternoon: \"på efterm.\",\n    evening: \"på kvällen\",\n    night: \"på natten\"\n  },\n  abbreviated: {\n    am: \"fm\",\n    pm: \"em\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"på morg.\",\n    afternoon: \"på efterm.\",\n    evening: \"på kvällen\",\n    night: \"på natten\"\n  },\n  wide: {\n    am: \"fm\",\n    pm: \"em\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"på morgonen\",\n    afternoon: \"på eftermiddagen\",\n    evening: \"på kvällen\",\n    night: \"på natten\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n      case 2:\n        return number + \":a\";\n    }\n  }\n  return number + \":e\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}