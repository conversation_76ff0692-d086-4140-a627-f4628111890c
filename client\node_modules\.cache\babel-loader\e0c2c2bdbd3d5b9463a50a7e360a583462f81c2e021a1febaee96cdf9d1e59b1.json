{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"e.ə\", \"b.e\"],\n  abbreviated: [\"e.ə\", \"b.e\"],\n  wide: [\"eramızdan əvvəl\", \"bizim era\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1ci kvartal\", \"2ci kvartal\", \"3cü kvartal\", \"4cü kvartal\"]\n};\nconst monthValues = {\n  narrow: [\"Y\", \"F\", \"M\", \"A\", \"M\", \"İ\", \"İ\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"Yan\", \"Fev\", \"Mar\", \"Apr\", \"May\", \"<PERSON>yun\", \"<PERSON>yul\", \"Avq\", \"Sen\", \"Okt\", \"<PERSON>y\", \"Dek\"],\n  wide: [\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"May\", \"<PERSON>yun\", \"<PERSON>yu<PERSON>\", \"Avqust\", \"Sentyabr\", \"Oktyabr\", \"Noyabr\", \"Dekabr\"]\n};\nconst dayValues = {\n  narrow: [\"B.\", \"B.e\", \"Ç.a\", \"Ç.\", \"C.a\", \"C.\", \"Ş.\"],\n  short: [\"B.\", \"B.e\", \"Ç.a\", \"Ç.\", \"C.a\", \"C.\", \"Ş.\"],\n  abbreviated: [\"Baz\", \"Baz.e\", \"Çər.a\", \"Çər\", \"Cüm.a\", \"Cüm\", \"Şə\"],\n  wide: [\"Bazar\", \"Bazar ertəsi\", \"Çərşənbə axşamı\", \"Çərşənbə\", \"Cümə axşamı\", \"Cümə\", \"Şənbə\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gecəyarı\",\n    noon: \"gün\",\n    morning: \"səhər\",\n    afternoon: \"gündüz\",\n    evening: \"axşam\",\n    night: \"gecə\"\n  }\n};\nconst suffixes = {\n  1: \"-inci\",\n  5: \"-inci\",\n  8: \"-inci\",\n  70: \"-inci\",\n  80: \"-inci\",\n  2: \"-nci\",\n  7: \"-nci\",\n  20: \"-nci\",\n  50: \"-nci\",\n  3: \"-üncü\",\n  4: \"-üncü\",\n  100: \"-üncü\",\n  6: \"-ncı\",\n  9: \"-uncu\",\n  10: \"-uncu\",\n  30: \"-uncu\",\n  60: \"-ıncı\",\n  90: \"-ıncı\"\n};\nconst getSuffix = number => {\n  if (number === 0) {\n    // special case for zero\n    return number + \"-ıncı\";\n  }\n  const a = number % 10;\n  const b = number % 100 - a;\n  const c = number >= 100 ? 100 : null;\n  if (suffixes[a]) {\n    return suffixes[a];\n  } else if (suffixes[b]) {\n    return suffixes[b];\n  } else if (c !== null) {\n    return suffixes[c];\n  }\n  return \"\";\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const suffix = getSuffix(number);\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}