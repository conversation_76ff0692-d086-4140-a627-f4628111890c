{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ჩ.წ-მდე\", \"ჩ.წ\"],\n  abbreviated: [\"ჩვ.წ-მდე\", \"ჩვ.წ\"],\n  wide: [\"ჩვენს წელთაღრიცხვამდე\", \"ჩვენი წელთაღრიცხვით\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ლი კვ\", \"2-ე კვ\", \"3-ე კვ\", \"4-ე კვ\"],\n  wide: [\"1-ლი კვარტალი\", \"2-ე კვარტალი\", \"3-ე კვარტალი\", \"4-ე კვარტალი\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ია\", \"თე\", \"მა\", \"აპ\", \"მს\", \"ვნ\", \"ვლ\", \"აგ\", \"სე\", \"ოქ\", \"ნო\", \"დე\"],\n  abbreviated: [\"იან\", \"თებ\", \"მარ\", \"აპრ\", \"მაი\", \"ივნ\", \"ივლ\", \"აგვ\", \"სექ\", \"ოქტ\", \"ნოე\", \"დეკ\"],\n  wide: [\"იანვარი\", \"თებერვალი\", \"მარტი\", \"აპრილი\", \"მაისი\", \"ივნისი\", \"ივლისი\", \"აგვისტო\", \"სექტემბერი\", \"ოქტომბერი\", \"ნოემბერი\", \"დეკემბერი\"]\n};\nconst dayValues = {\n  narrow: [\"კვ\", \"ორ\", \"სა\", \"ოთ\", \"ხუ\", \"პა\", \"შა\"],\n  short: [\"კვი\", \"ორშ\", \"სამ\", \"ოთხ\", \"ხუთ\", \"პარ\", \"შაბ\"],\n  abbreviated: [\"კვი\", \"ორშ\", \"სამ\", \"ოთხ\", \"ხუთ\", \"პარ\", \"შაბ\"],\n  wide: [\"კვირა\", \"ორშაბათი\", \"სამშაბათი\", \"ოთხშაბათი\", \"ხუთშაბათი\", \"პარასკევი\", \"შაბათი\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"შუაღამე\",\n    noon: \"შუადღე\",\n    morning: \"დილა\",\n    afternoon: \"საღამო\",\n    evening: \"საღამო\",\n    night: \"ღამე\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"შუაღამით\",\n    noon: \"შუადღისას\",\n    morning: \"დილით\",\n    afternoon: \"ნაშუადღევს\",\n    evening: \"საღამოს\",\n    night: \"ღამით\"\n  }\n};\nconst ordinalNumber = dirtyNumber => {\n  const number = Number(dirtyNumber);\n  if (number === 1) {\n    return number + \"-ლი\";\n  }\n  return number + \"-ე\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}