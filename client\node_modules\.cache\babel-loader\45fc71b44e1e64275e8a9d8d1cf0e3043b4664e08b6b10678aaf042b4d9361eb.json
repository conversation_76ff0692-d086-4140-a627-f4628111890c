{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"секунд хүрэхгүй\",\n    other: \"{{count}} секунд хүрэхгүй\"\n  },\n  xSeconds: {\n    one: \"1 секунд\",\n    other: \"{{count}} секунд\"\n  },\n  halfAMinute: \"хагас минут\",\n  lessThanXMinutes: {\n    one: \"минут хүрэхгүй\",\n    other: \"{{count}} минут хүрэхгүй\"\n  },\n  xMinutes: {\n    one: \"1 минут\",\n    other: \"{{count}} минут\"\n  },\n  aboutXHours: {\n    one: \"ойролцоогоор 1 цаг\",\n    other: \"ойролцоогоор {{count}} цаг\"\n  },\n  xHours: {\n    one: \"1 цаг\",\n    other: \"{{count}} цаг\"\n  },\n  xDays: {\n    one: \"1 өдөр\",\n    other: \"{{count}} өдөр\"\n  },\n  aboutXWeeks: {\n    one: \"ойролцоогоор 1 долоо хоног\",\n    other: \"ойролцоогоор {{count}} долоо хоног\"\n  },\n  xWeeks: {\n    one: \"1 долоо хоног\",\n    other: \"{{count}} долоо хоног\"\n  },\n  aboutXMonths: {\n    one: \"ойролцоогоор 1 сар\",\n    other: \"ойролцоогоор {{count}} сар\"\n  },\n  xMonths: {\n    one: \"1 сар\",\n    other: \"{{count}} сар\"\n  },\n  aboutXYears: {\n    one: \"ойролцоогоор 1 жил\",\n    other: \"ойролцоогоор {{count}} жил\"\n  },\n  xYears: {\n    one: \"1 жил\",\n    other: \"{{count}} жил\"\n  },\n  overXYears: {\n    one: \"1 жил гаран\",\n    other: \"{{count}} жил гаран\"\n  },\n  almostXYears: {\n    one: \"бараг 1 жил\",\n    other: \"бараг {{count}} жил\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    /**\n     * Append genitive case\n     */\n    const words = result.split(\" \");\n    const lastword = words.pop();\n    result = words.join(\" \");\n    switch (lastword) {\n      case \"секунд\":\n        result += \" секундийн\";\n        break;\n      case \"минут\":\n        result += \" минутын\";\n        break;\n      case \"цаг\":\n        result += \" цагийн\";\n        break;\n      case \"өдөр\":\n        result += \" өдрийн\";\n        break;\n      case \"сар\":\n        result += \" сарын\";\n        break;\n      case \"жил\":\n        result += \" жилийн\";\n        break;\n      case \"хоног\":\n        result += \" хоногийн\";\n        break;\n      case \"гаран\":\n        result += \" гараны\";\n        break;\n      case \"хүрэхгүй\":\n        result += \" хүрэхгүй хугацааны\";\n        break;\n      default:\n        result += lastword + \"-н\";\n    }\n    if (options.comparison && options.comparison > 0) {\n      return result + \" дараа\";\n    } else {\n      return result + \" өмнө\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}