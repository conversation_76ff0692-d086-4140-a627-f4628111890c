{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"Ք\", \"Մ\"],\n  abbreviated: [\"ՔԱ\", \"ՄԹ\"],\n  wide: [\"Քրիստոսից առաջ\", \"Մեր թվարկության\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Ք1\", \"Ք2\", \"Ք3\", \"Ք4\"],\n  wide: [\"1֊ին քառորդ\", \"2֊րդ քառորդ\", \"3֊րդ քառորդ\", \"4֊րդ քառորդ\"]\n};\nconst monthValues = {\n  narrow: [\"Հ\", \"Փ\", \"Մ\", \"Ա\", \"Մ\", \"Հ\", \"Հ\", \"Օ\", \"Ս\", \"Հ\", \"Ն\", \"Դ\"],\n  abbreviated: [\"հուն\", \"փետ\", \"մար\", \"ապր\", \"մայ\", \"հուն\", \"հուլ\", \"օգս\", \"սեպ\", \"հոկ\", \"նոյ\", \"դեկ\"],\n  wide: [\"հունվար\", \"փետրվար\", \"մարտ\", \"ապրիլ\", \"մայիս\", \"հունիս\", \"հուլիս\", \"օգոստոս\", \"սեպտեմբեր\", \"հոկտեմբեր\", \"նոյեմբեր\", \"դեկտեմբեր\"]\n};\nconst dayValues = {\n  narrow: [\"Կ\", \"Ե\", \"Ե\", \"Չ\", \"Հ\", \"Ո\", \"Շ\"],\n  short: [\"կր\", \"եր\", \"եք\", \"չք\", \"հգ\", \"ուր\", \"շբ\"],\n  abbreviated: [\"կիր\", \"երկ\", \"երք\", \"չոր\", \"հնգ\", \"ուրբ\", \"շաբ\"],\n  wide: [\"կիրակի\", \"երկուշաբթի\", \"երեքշաբթի\", \"չորեքշաբթի\", \"հինգշաբթի\", \"ուրբաթ\", \"շաբաթ\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"կեսգշ\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"կեսգիշեր\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"կեսգիշեր\",\n    noon: \"կեսօր\",\n    morning: \"առավոտ\",\n    afternoon: \"ցերեկ\",\n    evening: \"երեկո\",\n    night: \"գիշեր\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"կեսգշ\",\n    noon: \"կեսօր\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"կեսգիշերին\",\n    noon: \"կեսօրին\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"կեսգիշերին\",\n    noon: \"կեսօրին\",\n    morning: \"առավոտը\",\n    afternoon: \"ցերեկը\",\n    evening: \"երեկոյան\",\n    night: \"գիշերը\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n\n  // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  const rem100 = number % 100;\n  if (rem100 < 10) {\n    if (rem100 % 10 === 1) {\n      return number + \"֊ին\";\n    }\n  }\n  return number + \"֊րդ\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}