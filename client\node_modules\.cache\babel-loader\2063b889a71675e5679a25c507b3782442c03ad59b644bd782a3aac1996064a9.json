{"ast": null, "code": "// Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"હમણાં\",\n    // CLDR #1461\n    other: \"​આશરે {{count}} સેકંડ\"\n  },\n  xSeconds: {\n    one: \"1 સેકંડ\",\n    other: \"{{count}} સેકંડ\"\n  },\n  halfAMinute: \"અડધી મિનિટ\",\n  lessThanXMinutes: {\n    one: \"આ મિનિટ\",\n    // CLDR #1448\n    other: \"​આશરે {{count}} મિનિટ\"\n  },\n  xMinutes: {\n    one: \"1 મિનિટ\",\n    other: \"{{count}} મિનિટ\"\n  },\n  aboutXHours: {\n    one: \"​આશરે 1 કલાક\",\n    other: \"​આશરે {{count}} કલાક\"\n  },\n  xHours: {\n    one: \"1 કલાક\",\n    other: \"{{count}} કલાક\"\n  },\n  xDays: {\n    one: \"1 દિવસ\",\n    other: \"{{count}} દિવસ\"\n  },\n  aboutXWeeks: {\n    one: \"આશરે 1 અઠવાડિયું\",\n    other: \"આશરે {{count}} અઠવાડિયા\"\n  },\n  xWeeks: {\n    one: \"1 અઠવાડિયું\",\n    other: \"{{count}} અઠવાડિયા\"\n  },\n  aboutXMonths: {\n    one: \"આશરે 1 મહિનો\",\n    other: \"આશરે {{count}} મહિના\"\n  },\n  xMonths: {\n    one: \"1 મહિનો\",\n    other: \"{{count}} મહિના\"\n  },\n  aboutXYears: {\n    one: \"આશરે 1 વર્ષ\",\n    other: \"આશરે {{count}} વર્ષ\"\n  },\n  xYears: {\n    one: \"1 વર્ષ\",\n    other: \"{{count}} વર્ષ\"\n  },\n  overXYears: {\n    one: \"1 વર્ષથી વધુ\",\n    other: \"{{count}} વર્ષથી વધુ\"\n  },\n  almostXYears: {\n    one: \"લગભગ 1 વર્ષ\",\n    other: \"લગભગ {{count}} વર્ષ\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"માં\";\n    } else {\n      return result + \" પહેલાં\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}