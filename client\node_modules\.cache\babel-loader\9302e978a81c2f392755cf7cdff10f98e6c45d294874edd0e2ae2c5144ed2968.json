{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"BC\", \"AC\"],\n  abbreviated: [\"きげんぜん\", \"せいれき\"],\n  wide: [\"きげんぜん\", \"せいれき\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"だい1しはんき\", \"だい2しはんき\", \"だい3しはんき\", \"だい4しはんき\"]\n};\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n  abbreviated: [\"1がつ\", \"2がつ\", \"3がつ\", \"4がつ\", \"5がつ\", \"6がつ\", \"7がつ\", \"8がつ\", \"9がつ\", \"10がつ\", \"11がつ\", \"12がつ\"],\n  wide: [\"1がつ\", \"2がつ\", \"3がつ\", \"4がつ\", \"5がつ\", \"6がつ\", \"7がつ\", \"8がつ\", \"9がつ\", \"10がつ\", \"11がつ\", \"12がつ\"]\n};\nconst dayValues = {\n  narrow: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  short: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  abbreviated: [\"にち\", \"げつ\", \"か\", \"すい\", \"もく\", \"きん\", \"ど\"],\n  wide: [\"にちようび\", \"げつようび\", \"かようび\", \"すいようび\", \"もくようび\", \"きんようび\", \"どようび\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  },\n  abbreviated: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  },\n  wide: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  },\n  abbreviated: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  },\n  wide: {\n    am: \"ごぜん\",\n    pm: \"ごご\",\n    midnight: \"しんや\",\n    noon: \"しょうご\",\n    morning: \"あさ\",\n    afternoon: \"ごご\",\n    evening: \"よる\",\n    night: \"しんや\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options?.unit);\n  switch (unit) {\n    case \"year\":\n      return `${number}ねん`;\n    case \"quarter\":\n      return `だい${number}しはんき`;\n    case \"month\":\n      return `${number}がつ`;\n    case \"week\":\n      return `だい${number}しゅう`;\n    case \"date\":\n      return `${number}にち`;\n    case \"hour\":\n      return `${number}じ`;\n    case \"minute\":\n      return `${number}ふん`;\n    case \"second\":\n      return `${number}びょう`;\n    default:\n      return `${number}`;\n  }\n};\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => Number(quarter) - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}