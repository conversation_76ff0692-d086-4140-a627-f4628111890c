{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1 сониядан кам\",\n    other: \"{{count}} сониядан кам\"\n  },\n  xSeconds: {\n    one: \"1 сония\",\n    other: \"{{count}} сония\"\n  },\n  halfAMinute: \"ярим дақиқа\",\n  lessThanXMinutes: {\n    one: \"1 дақиқадан кам\",\n    other: \"{{count}} дақиқадан кам\"\n  },\n  xMinutes: {\n    one: \"1 дақиқа\",\n    other: \"{{count}} дақиқа\"\n  },\n  aboutXHours: {\n    one: \"тахминан 1 соат\",\n    other: \"тахминан {{count}} соат\"\n  },\n  xHours: {\n    one: \"1 соат\",\n    other: \"{{count}} соат\"\n  },\n  xDays: {\n    one: \"1 кун\",\n    other: \"{{count}} кун\"\n  },\n  aboutXWeeks: {\n    one: \"тахминан 1 хафта\",\n    other: \"тахминан {{count}} хафта\"\n  },\n  xWeeks: {\n    one: \"1 хафта\",\n    other: \"{{count}} хафта\"\n  },\n  aboutXMonths: {\n    one: \"тахминан 1 ой\",\n    other: \"тахминан {{count}} ой\"\n  },\n  xMonths: {\n    one: \"1 ой\",\n    other: \"{{count}} ой\"\n  },\n  aboutXYears: {\n    one: \"тахминан 1 йил\",\n    other: \"тахминан {{count}} йил\"\n  },\n  xYears: {\n    one: \"1 йил\",\n    other: \"{{count}} йил\"\n  },\n  overXYears: {\n    one: \"1 йилдан кўп\",\n    other: \"{{count}} йилдан кўп\"\n  },\n  almostXYears: {\n    one: \"деярли 1 йил\",\n    other: \"деярли {{count}} йил\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"дан кейин\";\n    } else {\n      return result + \" олдин\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}