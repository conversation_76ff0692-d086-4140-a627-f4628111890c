{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { getDaysInMonth } from \"./getDaysInMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setMonth} function options.\n */\n\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param month - The month index to set (0-11)\n * @param options - The options\n *\n * @returns The new date with the month set\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\nexport function setMonth(date, month, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const day = _date.getDate();\n  const midMonth = constructFrom(options?.in || date, 0);\n  midMonth.setFullYear(year, month, 15);\n  midMonth.setHours(0, 0, 0, 0);\n  const daysInMonth = getDaysInMonth(midMonth);\n\n  // Set the earlier date, allows to wrap Jan 31 to Feb 28\n  _date.setMonth(month, Math.min(day, daysInMonth));\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMonth;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}