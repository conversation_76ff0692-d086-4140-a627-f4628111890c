{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"помалку од секунда\",\n    other: \"помалку од {{count}} секунди\"\n  },\n  xSeconds: {\n    one: \"1 секунда\",\n    other: \"{{count}} секунди\"\n  },\n  halfAMinute: \"половина минута\",\n  lessThanXMinutes: {\n    one: \"помалку од минута\",\n    other: \"помалку од {{count}} минути\"\n  },\n  xMinutes: {\n    one: \"1 минута\",\n    other: \"{{count}} минути\"\n  },\n  aboutXHours: {\n    one: \"околу 1 час\",\n    other: \"околу {{count}} часа\"\n  },\n  xHours: {\n    one: \"1 час\",\n    other: \"{{count}} часа\"\n  },\n  xDays: {\n    one: \"1 ден\",\n    other: \"{{count}} дена\"\n  },\n  aboutXWeeks: {\n    one: \"околу 1 недела\",\n    other: \"околу {{count}} месеци\"\n  },\n  xWeeks: {\n    one: \"1 недела\",\n    other: \"{{count}} недели\"\n  },\n  aboutXMonths: {\n    one: \"околу 1 месец\",\n    other: \"околу {{count}} недели\"\n  },\n  xMonths: {\n    one: \"1 месец\",\n    other: \"{{count}} месеци\"\n  },\n  aboutXYears: {\n    one: \"околу 1 година\",\n    other: \"околу {{count}} години\"\n  },\n  xYears: {\n    one: \"1 година\",\n    other: \"{{count}} години\"\n  },\n  overXYears: {\n    one: \"повеќе од 1 година\",\n    other: \"повеќе од {{count}} години\"\n  },\n  almostXYears: {\n    one: \"безмалку 1 година\",\n    other: \"безмалку {{count}} години\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"за \" + result;\n    } else {\n      return \"пред \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}