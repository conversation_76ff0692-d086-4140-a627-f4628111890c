{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"פחות משנייה\",\n    two: \"פחות משתי שניות\",\n    other: \"פחות מ־{{count}} שניות\"\n  },\n  xSeconds: {\n    one: \"שנייה\",\n    two: \"שתי שניות\",\n    other: \"{{count}} שניות\"\n  },\n  halfAMinute: \"חצי דקה\",\n  lessThanXMinutes: {\n    one: \"פחות מדקה\",\n    two: \"פחות משתי דקות\",\n    other: \"פחות מ־{{count}} דקות\"\n  },\n  xMinutes: {\n    one: \"דקה\",\n    two: \"שתי דקות\",\n    other: \"{{count}} דקות\"\n  },\n  aboutXHours: {\n    one: \"כשעה\",\n    two: \"כשעתיים\",\n    other: \"כ־{{count}} שעות\"\n  },\n  xHours: {\n    one: \"שעה\",\n    two: \"שעתיים\",\n    other: \"{{count}} שעות\"\n  },\n  xDays: {\n    one: \"יום\",\n    two: \"יומיים\",\n    other: \"{{count}} ימים\"\n  },\n  aboutXWeeks: {\n    one: \"כשבוע\",\n    two: \"כשבועיים\",\n    other: \"כ־{{count}} שבועות\"\n  },\n  xWeeks: {\n    one: \"שבוע\",\n    two: \"שבועיים\",\n    other: \"{{count}} שבועות\"\n  },\n  aboutXMonths: {\n    one: \"כחודש\",\n    two: \"כחודשיים\",\n    other: \"כ־{{count}} חודשים\"\n  },\n  xMonths: {\n    one: \"חודש\",\n    two: \"חודשיים\",\n    other: \"{{count}} חודשים\"\n  },\n  aboutXYears: {\n    one: \"כשנה\",\n    two: \"כשנתיים\",\n    other: \"כ־{{count}} שנים\"\n  },\n  xYears: {\n    one: \"שנה\",\n    two: \"שנתיים\",\n    other: \"{{count}} שנים\"\n  },\n  overXYears: {\n    one: \"יותר משנה\",\n    two: \"יותר משנתיים\",\n    other: \"יותר מ־{{count}} שנים\"\n  },\n  almostXYears: {\n    one: \"כמעט שנה\",\n    two: \"כמעט שנתיים\",\n    other: \"כמעט {{count}} שנים\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  // Return word instead of `in one day` or `one day ago`\n  if (token === \"xDays\" && options?.addSuffix && count <= 2) {\n    if (options.comparison && options.comparison > 0) {\n      return count === 1 ? \"מחר\" : \"מחרתיים\";\n    }\n    return count === 1 ? \"אתמול\" : \"שלשום\";\n  }\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"בעוד \" + result;\n    } else {\n      return \"לפני \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}