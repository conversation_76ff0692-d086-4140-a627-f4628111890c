{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'الماضي عند الساعة' p\",\n  yesterday: \"'الأمس عند الساعة' p\",\n  today: \"'اليوم عند الساعة' p\",\n  tomorrow: \"'غدا عند الساعة' p\",\n  nextWeek: \"eeee 'القادم عند الساعة' p\",\n  other: \"P\"\n};\nexport const formatRelative = token => formatRelativeLocale[token];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}