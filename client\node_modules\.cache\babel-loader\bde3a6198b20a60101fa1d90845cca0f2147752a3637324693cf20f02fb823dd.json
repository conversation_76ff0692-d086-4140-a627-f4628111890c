{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: \"manner wéi eng Sekonn\",\n      other: \"manner wéi {{count}} Sekonnen\"\n    },\n    withPreposition: {\n      one: \"manner wéi enger Sekonn\",\n      other: \"manner wéi {{count}} Sekonnen\"\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: \"eng Sekonn\",\n      other: \"{{count}} Sekonnen\"\n    },\n    withPreposition: {\n      one: \"enger Sekonn\",\n      other: \"{{count}} Sekonnen\"\n    }\n  },\n  halfAMinute: {\n    standalone: \"eng hallef Minutt\",\n    withPreposition: \"enger hallwer Minutt\"\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: \"manner wéi eng Minutt\",\n      other: \"manner wéi {{count}} Minutten\"\n    },\n    withPreposition: {\n      one: \"manner wéi enger Minutt\",\n      other: \"manner wéi {{count}} Minutten\"\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: \"eng Minutt\",\n      other: \"{{count}} Minutten\"\n    },\n    withPreposition: {\n      one: \"enger Minutt\",\n      other: \"{{count}} Minutten\"\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: \"ongeféier eng Stonn\",\n      other: \"ongeféier {{count}} Stonnen\"\n    },\n    withPreposition: {\n      one: \"ongeféier enger Stonn\",\n      other: \"ongeféier {{count}} Stonnen\"\n    }\n  },\n  xHours: {\n    standalone: {\n      one: \"eng Stonn\",\n      other: \"{{count}} Stonnen\"\n    },\n    withPreposition: {\n      one: \"enger Stonn\",\n      other: \"{{count}} Stonnen\"\n    }\n  },\n  xDays: {\n    standalone: {\n      one: \"een Dag\",\n      other: \"{{count}} Deeg\"\n    },\n    withPreposition: {\n      one: \"engem Dag\",\n      other: \"{{count}} Deeg\"\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: \"ongeféier eng Woch\",\n      other: \"ongeféier {{count}} Wochen\"\n    },\n    withPreposition: {\n      one: \"ongeféier enger Woche\",\n      other: \"ongeféier {{count}} Wochen\"\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: \"eng Woch\",\n      other: \"{{count}} Wochen\"\n    },\n    withPreposition: {\n      one: \"enger Woch\",\n      other: \"{{count}} Wochen\"\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: \"ongeféier ee Mount\",\n      other: \"ongeféier {{count}} Méint\"\n    },\n    withPreposition: {\n      one: \"ongeféier engem Mount\",\n      other: \"ongeféier {{count}} Méint\"\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: \"ee Mount\",\n      other: \"{{count}} Méint\"\n    },\n    withPreposition: {\n      one: \"engem Mount\",\n      other: \"{{count}} Méint\"\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: \"ongeféier ee Joer\",\n      other: \"ongeféier {{count}} Joer\"\n    },\n    withPreposition: {\n      one: \"ongeféier engem Joer\",\n      other: \"ongeféier {{count}} Joer\"\n    }\n  },\n  xYears: {\n    standalone: {\n      one: \"ee Joer\",\n      other: \"{{count}} Joer\"\n    },\n    withPreposition: {\n      one: \"engem Joer\",\n      other: \"{{count}} Joer\"\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: \"méi wéi ee Joer\",\n      other: \"méi wéi {{count}} Joer\"\n    },\n    withPreposition: {\n      one: \"méi wéi engem Joer\",\n      other: \"méi wéi {{count}} Joer\"\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: \"bal ee Joer\",\n      other: \"bal {{count}} Joer\"\n    },\n    withPreposition: {\n      one: \"bal engem Joer\",\n      other: \"bal {{count}} Joer\"\n    }\n  }\n};\nconst EXCEPTION_CONSONANTS = [\"d\", \"h\", \"n\", \"t\", \"z\"];\nconst VOWELS = [\"a,\", \"e\", \"i\", \"o\", \"u\"];\nconst DIGITS_SPOKEN_N_NEEDED = [0, 1, 2, 3, 8, 9];\nconst FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED = [40, 50, 60, 70];\n\n// Eifeler Regel\nfunction isFinalNNeeded(nextWords) {\n  const firstLetter = nextWords.charAt(0).toLowerCase();\n  if (VOWELS.indexOf(firstLetter) != -1 || EXCEPTION_CONSONANTS.indexOf(firstLetter) != -1) {\n    return true;\n  }\n\n  // Numbers would need to converted into words for checking.\n  // Therefore, I have listed the digits that require a preceeding n with a few exceptions.\n  const firstWord = nextWords.split(\" \")[0];\n  const number = parseInt(firstWord);\n  if (!isNaN(number) && DIGITS_SPOKEN_N_NEEDED.indexOf(number % 10) != -1 && FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED.indexOf(parseInt(firstWord.substring(0, 2))) == -1) {\n    return true;\n  }\n\n  // Omit other checks as they are not expected here.\n  return false;\n}\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  const usageGroup = options?.addSuffix ? tokenValue.withPreposition : tokenValue.standalone;\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"a\" + (isFinalNNeeded(result) ? \"n\" : \"\") + \" \" + result;\n    } else {\n      return \"viru\" + (isFinalNNeeded(result) ? \"n\" : \"\") + \" \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}