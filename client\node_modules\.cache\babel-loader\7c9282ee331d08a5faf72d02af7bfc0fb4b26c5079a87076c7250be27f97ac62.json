{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"aK\", \"pK\"],\n  abbreviated: [\"a.K.E.\", \"p.K.E.\"],\n  wide: [\"anta<PERSON>\", \"<PERSON><PERSON><PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1-a kvaronjaro\", \"2-a kvaronjaro\", \"3-a kvaronjaro\", \"4-a kvaronjaro\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"jan\", \"feb\", \"mar\", \"apr\", \"maj\", \"jun\", \"jul\", \"aŭg\", \"sep\", \"okt\", \"nov\", \"dec\"],\n  wide: [\"januaro\", \"februaro\", \"marto\", \"aprilo\", \"majo\", \"junio\", \"julio\", \"aŭgusto\", \"septembro\", \"oktobro\", \"novembro\", \"decembro\"]\n};\nconst dayValues = {\n  narrow: [\"D\", \"L\", \"M\", \"M\", \"Ĵ\", \"V\", \"S\"],\n  short: [\"di\", \"lu\", \"ma\", \"me\", \"ĵa\", \"ve\", \"sa\"],\n  abbreviated: [\"dim\", \"lun\", \"mar\", \"mer\", \"ĵaŭ\", \"ven\", \"sab\"],\n  wide: [\"dimanĉo\", \"lundo\", \"mardo\", \"merkredo\", \"ĵaŭdo\", \"vendredo\", \"sabato\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\"\n  },\n  abbreviated: {\n    am: \"a.t.m.\",\n    pm: \"p.t.m.\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\"\n  },\n  wide: {\n    am: \"antaŭtagmeze\",\n    pm: \"posttagmeze\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\"\n  }\n};\nconst ordinalNumber = dirtyNumber => {\n  const number = Number(dirtyNumber);\n  return number + \"-a\";\n};\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}