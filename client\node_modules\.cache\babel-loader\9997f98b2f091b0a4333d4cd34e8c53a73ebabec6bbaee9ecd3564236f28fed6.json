{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\n//Source: https://www.unicode.org/cldr/charts/32/summary/gu.html\nconst dateFormats = {\n  full: \"EEEE, d MMMM, y\",\n  // CLDR #1825\n  long: \"d MMMM, y\",\n  // CLDR #1826\n  medium: \"d MMM, y\",\n  // CLDR #1827\n  short: \"d/M/yy\" // CLDR #1828\n};\nconst timeFormats = {\n  full: \"hh:mm:ss a zzzz\",\n  // CLDR #1829\n  long: \"hh:mm:ss a z\",\n  // CLDR #1830\n  medium: \"hh:mm:ss a\",\n  // CLDR #1831\n  short: \"hh:mm a\" // CLDR #1832\n};\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  // CLDR #1833\n  long: \"{{date}} {{time}}\",\n  // CLDR #1834\n  medium: \"{{date}} {{time}}\",\n  // CLDR #1835\n  short: \"{{date}} {{time}}\" // CLDR #1836\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}