{"ast": null, "code": "import { daysInYear } from \"./constants.js\";\n\n/**\n * @name milliseconds\n * @category Millisecond Helpers\n * @summary\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * @description\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occurs every 4 years, except for years that are divisible by 100 and not divisible by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n *\n * One month is a year divided by 12.\n *\n * @param duration - The object with years, months, weeks, days, hours, minutes and seconds to be added.\n *\n * @returns The milliseconds\n *\n * @example\n * // 1 year in milliseconds\n * milliseconds({ years: 1 })\n * //=> 31556952000\n *\n * // 3 months in milliseconds\n * milliseconds({ months: 3 })\n * //=> 7889238000\n */\nexport function milliseconds({\n  years,\n  months,\n  weeks,\n  days,\n  hours,\n  minutes,\n  seconds\n}) {\n  let totalDays = 0;\n  if (years) totalDays += years * daysInYear;\n  if (months) totalDays += months * (daysInYear / 12);\n  if (weeks) totalDays += weeks * 7;\n  if (days) totalDays += days;\n  let totalSeconds = totalDays * 24 * 60 * 60;\n  if (hours) totalSeconds += hours * 60 * 60;\n  if (minutes) totalSeconds += minutes * 60;\n  if (seconds) totalSeconds += seconds;\n  return Math.trunc(totalSeconds * 1000);\n}\n\n// Fallback for modularized imports:\nexport default milliseconds;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}