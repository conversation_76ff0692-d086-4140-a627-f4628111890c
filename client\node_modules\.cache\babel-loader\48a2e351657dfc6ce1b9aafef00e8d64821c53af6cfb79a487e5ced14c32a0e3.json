{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'إلي فات مع' p\",\n  yesterday: \"'البارح مع' p\",\n  today: \"'اليوم مع' p\",\n  tomorrow: \"'غدوة مع' p\",\n  nextWeek: \"eeee 'الجمعة الجاية مع' p 'نهار'\",\n  other: \"P\"\n};\nexport const formatRelative = token => formatRelativeLocale[token];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}