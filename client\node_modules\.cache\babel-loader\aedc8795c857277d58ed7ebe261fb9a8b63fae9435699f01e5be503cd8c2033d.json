{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      regular: \"méně než 1 sekunda\",\n      past: \"před méně než 1 sekundou\",\n      future: \"za méně než 1 sekundu\"\n    },\n    few: {\n      regular: \"méně než {{count}} sekundy\",\n      past: \"před méně než {{count}} sekundami\",\n      future: \"za méně než {{count}} sekundy\"\n    },\n    many: {\n      regular: \"méně než {{count}} sekund\",\n      past: \"před méně než {{count}} sekundami\",\n      future: \"za méně než {{count}} sekund\"\n    }\n  },\n  xSeconds: {\n    one: {\n      regular: \"1 sekunda\",\n      past: \"před 1 sekundou\",\n      future: \"za 1 sekundu\"\n    },\n    few: {\n      regular: \"{{count}} sekundy\",\n      past: \"před {{count}} sekundami\",\n      future: \"za {{count}} sekundy\"\n    },\n    many: {\n      regular: \"{{count}} sekund\",\n      past: \"před {{count}} sekundami\",\n      future: \"za {{count}} sekund\"\n    }\n  },\n  halfAMinute: {\n    type: \"other\",\n    other: {\n      regular: \"půl minuty\",\n      past: \"před půl minutou\",\n      future: \"za půl minuty\"\n    }\n  },\n  lessThanXMinutes: {\n    one: {\n      regular: \"méně než 1 minuta\",\n      past: \"před méně než 1 minutou\",\n      future: \"za méně než 1 minutu\"\n    },\n    few: {\n      regular: \"méně než {{count}} minuty\",\n      past: \"před méně než {{count}} minutami\",\n      future: \"za méně než {{count}} minuty\"\n    },\n    many: {\n      regular: \"méně než {{count}} minut\",\n      past: \"před méně než {{count}} minutami\",\n      future: \"za méně než {{count}} minut\"\n    }\n  },\n  xMinutes: {\n    one: {\n      regular: \"1 minuta\",\n      past: \"před 1 minutou\",\n      future: \"za 1 minutu\"\n    },\n    few: {\n      regular: \"{{count}} minuty\",\n      past: \"před {{count}} minutami\",\n      future: \"za {{count}} minuty\"\n    },\n    many: {\n      regular: \"{{count}} minut\",\n      past: \"před {{count}} minutami\",\n      future: \"za {{count}} minut\"\n    }\n  },\n  aboutXHours: {\n    one: {\n      regular: \"přibližně 1 hodina\",\n      past: \"přibližně před 1 hodinou\",\n      future: \"přibližně za 1 hodinu\"\n    },\n    few: {\n      regular: \"přibližně {{count}} hodiny\",\n      past: \"přibližně před {{count}} hodinami\",\n      future: \"přibližně za {{count}} hodiny\"\n    },\n    many: {\n      regular: \"přibližně {{count}} hodin\",\n      past: \"přibližně před {{count}} hodinami\",\n      future: \"přibližně za {{count}} hodin\"\n    }\n  },\n  xHours: {\n    one: {\n      regular: \"1 hodina\",\n      past: \"před 1 hodinou\",\n      future: \"za 1 hodinu\"\n    },\n    few: {\n      regular: \"{{count}} hodiny\",\n      past: \"před {{count}} hodinami\",\n      future: \"za {{count}} hodiny\"\n    },\n    many: {\n      regular: \"{{count}} hodin\",\n      past: \"před {{count}} hodinami\",\n      future: \"za {{count}} hodin\"\n    }\n  },\n  xDays: {\n    one: {\n      regular: \"1 den\",\n      past: \"před 1 dnem\",\n      future: \"za 1 den\"\n    },\n    few: {\n      regular: \"{{count}} dny\",\n      past: \"před {{count}} dny\",\n      future: \"za {{count}} dny\"\n    },\n    many: {\n      regular: \"{{count}} dní\",\n      past: \"před {{count}} dny\",\n      future: \"za {{count}} dní\"\n    }\n  },\n  aboutXWeeks: {\n    one: {\n      regular: \"přibližně 1 týden\",\n      past: \"přibližně před 1 týdnem\",\n      future: \"přibližně za 1 týden\"\n    },\n    few: {\n      regular: \"přibližně {{count}} týdny\",\n      past: \"přibližně před {{count}} týdny\",\n      future: \"přibližně za {{count}} týdny\"\n    },\n    many: {\n      regular: \"přibližně {{count}} týdnů\",\n      past: \"přibližně před {{count}} týdny\",\n      future: \"přibližně za {{count}} týdnů\"\n    }\n  },\n  xWeeks: {\n    one: {\n      regular: \"1 týden\",\n      past: \"před 1 týdnem\",\n      future: \"za 1 týden\"\n    },\n    few: {\n      regular: \"{{count}} týdny\",\n      past: \"před {{count}} týdny\",\n      future: \"za {{count}} týdny\"\n    },\n    many: {\n      regular: \"{{count}} týdnů\",\n      past: \"před {{count}} týdny\",\n      future: \"za {{count}} týdnů\"\n    }\n  },\n  aboutXMonths: {\n    one: {\n      regular: \"přibližně 1 měsíc\",\n      past: \"přibližně před 1 měsícem\",\n      future: \"přibližně za 1 měsíc\"\n    },\n    few: {\n      regular: \"přibližně {{count}} měsíce\",\n      past: \"přibližně před {{count}} měsíci\",\n      future: \"přibližně za {{count}} měsíce\"\n    },\n    many: {\n      regular: \"přibližně {{count}} měsíců\",\n      past: \"přibližně před {{count}} měsíci\",\n      future: \"přibližně za {{count}} měsíců\"\n    }\n  },\n  xMonths: {\n    one: {\n      regular: \"1 měsíc\",\n      past: \"před 1 měsícem\",\n      future: \"za 1 měsíc\"\n    },\n    few: {\n      regular: \"{{count}} měsíce\",\n      past: \"před {{count}} měsíci\",\n      future: \"za {{count}} měsíce\"\n    },\n    many: {\n      regular: \"{{count}} měsíců\",\n      past: \"před {{count}} měsíci\",\n      future: \"za {{count}} měsíců\"\n    }\n  },\n  aboutXYears: {\n    one: {\n      regular: \"přibližně 1 rok\",\n      past: \"přibližně před 1 rokem\",\n      future: \"přibližně za 1 rok\"\n    },\n    few: {\n      regular: \"přibližně {{count}} roky\",\n      past: \"přibližně před {{count}} roky\",\n      future: \"přibližně za {{count}} roky\"\n    },\n    many: {\n      regular: \"přibližně {{count}} roků\",\n      past: \"přibližně před {{count}} roky\",\n      future: \"přibližně za {{count}} roků\"\n    }\n  },\n  xYears: {\n    one: {\n      regular: \"1 rok\",\n      past: \"před 1 rokem\",\n      future: \"za 1 rok\"\n    },\n    few: {\n      regular: \"{{count}} roky\",\n      past: \"před {{count}} roky\",\n      future: \"za {{count}} roky\"\n    },\n    many: {\n      regular: \"{{count}} roků\",\n      past: \"před {{count}} roky\",\n      future: \"za {{count}} roků\"\n    }\n  },\n  overXYears: {\n    one: {\n      regular: \"více než 1 rok\",\n      past: \"před více než 1 rokem\",\n      future: \"za více než 1 rok\"\n    },\n    few: {\n      regular: \"více než {{count}} roky\",\n      past: \"před více než {{count}} roky\",\n      future: \"za více než {{count}} roky\"\n    },\n    many: {\n      regular: \"více než {{count}} roků\",\n      past: \"před více než {{count}} roky\",\n      future: \"za více než {{count}} roků\"\n    }\n  },\n  almostXYears: {\n    one: {\n      regular: \"skoro 1 rok\",\n      past: \"skoro před 1 rokem\",\n      future: \"skoro za 1 rok\"\n    },\n    few: {\n      regular: \"skoro {{count}} roky\",\n      past: \"skoro před {{count}} roky\",\n      future: \"skoro za {{count}} roky\"\n    },\n    many: {\n      regular: \"skoro {{count}} roků\",\n      past: \"skoro před {{count}} roky\",\n      future: \"skoro za {{count}} roků\"\n    }\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let pluralResult;\n  const tokenValue = formatDistanceLocale[token];\n\n  // cs pluralization\n  if (tokenValue.type === \"other\") {\n    pluralResult = tokenValue.other;\n  } else if (count === 1) {\n    pluralResult = tokenValue.one;\n  } else if (count > 1 && count < 5) {\n    pluralResult = tokenValue.few;\n  } else {\n    pluralResult = tokenValue.many;\n  }\n\n  // times\n  const suffixExist = options?.addSuffix === true;\n  const comparison = options?.comparison;\n  let timeResult;\n  if (suffixExist && comparison === -1) {\n    timeResult = pluralResult.past;\n  } else if (suffixExist && comparison === 1) {\n    timeResult = pluralResult.future;\n  } else {\n    timeResult = pluralResult.regular;\n  }\n  return timeResult.replace(\"{{count}}\", String(count));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}