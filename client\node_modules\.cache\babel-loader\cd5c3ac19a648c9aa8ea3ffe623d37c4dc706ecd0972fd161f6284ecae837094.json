{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"少於 1 秒\",\n    other: \"少於 {{count}} 秒\"\n  },\n  xSeconds: {\n    one: \"1 秒\",\n    other: \"{{count}} 秒\"\n  },\n  halfAMinute: \"半分鐘\",\n  lessThanXMinutes: {\n    one: \"少於 1 分鐘\",\n    other: \"少於 {{count}} 分鐘\"\n  },\n  xMinutes: {\n    one: \"1 分鐘\",\n    other: \"{{count}} 分鐘\"\n  },\n  xHours: {\n    one: \"1 小時\",\n    other: \"{{count}} 小時\"\n  },\n  aboutXHours: {\n    one: \"大約 1 小時\",\n    other: \"大約 {{count}} 小時\"\n  },\n  xDays: {\n    one: \"1 天\",\n    other: \"{{count}} 天\"\n  },\n  aboutXWeeks: {\n    one: \"大約 1 個星期\",\n    other: \"大約 {{count}} 個星期\"\n  },\n  xWeeks: {\n    one: \"1 個星期\",\n    other: \"{{count}} 個星期\"\n  },\n  aboutXMonths: {\n    one: \"大約 1 個月\",\n    other: \"大約 {{count}} 個月\"\n  },\n  xMonths: {\n    one: \"1 個月\",\n    other: \"{{count}} 個月\"\n  },\n  aboutXYears: {\n    one: \"大約 1 年\",\n    other: \"大約 {{count}} 年\"\n  },\n  xYears: {\n    one: \"1 年\",\n    other: \"{{count}} 年\"\n  },\n  overXYears: {\n    one: \"超過 1 年\",\n    other: \"超過 {{count}} 年\"\n  },\n  almostXYears: {\n    one: \"將近 1 年\",\n    other: \"將近 {{count}} 年\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"內\";\n    } else {\n      return result + \"前\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}