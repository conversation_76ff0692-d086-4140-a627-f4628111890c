{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"bir saniyədən az\",\n    other: \"{{count}} bir saniyədən az\"\n  },\n  xSeconds: {\n    one: \"1 saniyə\",\n    other: \"{{count}} saniyə\"\n  },\n  halfAMinute: \"yarım dəqiqə\",\n  lessThanXMinutes: {\n    one: \"bir dəqiqədən az\",\n    other: \"{{count}} bir dəqiqədən az\"\n  },\n  xMinutes: {\n    one: \"bir dəqiqə\",\n    other: \"{{count}} dəqiqə\"\n  },\n  aboutXHours: {\n    one: \"təxminən 1 saat\",\n    other: \"təxminən {{count}} saat\"\n  },\n  xHours: {\n    one: \"1 saat\",\n    other: \"{{count}} saat\"\n  },\n  xDays: {\n    one: \"1 gün\",\n    other: \"{{count}} gün\"\n  },\n  aboutXWeeks: {\n    one: \"təxminən 1 həftə\",\n    other: \"təxminən {{count}} həftə\"\n  },\n  xWeeks: {\n    one: \"1 həftə\",\n    other: \"{{count}} həftə\"\n  },\n  aboutXMonths: {\n    one: \"təxminən 1 ay\",\n    other: \"təxminən {{count}} ay\"\n  },\n  xMonths: {\n    one: \"1 ay\",\n    other: \"{{count}} ay\"\n  },\n  aboutXYears: {\n    one: \"təxminən 1 il\",\n    other: \"təxminən {{count}} il\"\n  },\n  xYears: {\n    one: \"1 il\",\n    other: \"{{count}} il\"\n  },\n  overXYears: {\n    one: \"1 ildən çox\",\n    other: \"{{count}} ildən çox\"\n  },\n  almostXYears: {\n    one: \"demək olar ki 1 il\",\n    other: \"demək olar ki {{count}} il\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" sonra\";\n    } else {\n      return result + \" əvvəl\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}