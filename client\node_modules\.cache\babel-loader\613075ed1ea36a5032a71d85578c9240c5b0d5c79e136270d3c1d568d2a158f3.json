{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"pr. n. št.\", \"po n. št.\"],\n  abbreviated: [\"pr. n. št.\", \"po n. št.\"],\n  wide: [\"pred našim <PERSON>\", \"po našem štetju\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1. čet.\", \"2. čet.\", \"3. čet.\", \"4. čet.\"],\n  wide: [\"1. četrtletje\", \"2. četrtletje\", \"3. četrtletje\", \"4. četrtletje\"]\n};\nconst monthValues = {\n  narrow: [\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\"jan.\", \"feb.\", \"mar.\", \"apr.\", \"maj\", \"jun.\", \"jul.\", \"avg.\", \"sep.\", \"okt.\", \"nov.\", \"dec.\"],\n  wide: [\"januar\", \"februar\", \"marec\", \"april\", \"maj\", \"junij\", \"julij\", \"avgust\", \"september\", \"oktober\", \"november\", \"december\"]\n};\nconst dayValues = {\n  narrow: [\"n\", \"p\", \"t\", \"s\", \"č\", \"p\", \"s\"],\n  short: [\"ned.\", \"pon.\", \"tor.\", \"sre.\", \"čet.\", \"pet.\", \"sob.\"],\n  abbreviated: [\"ned.\", \"pon.\", \"tor.\", \"sre.\", \"čet.\", \"pet.\", \"sob.\"],\n  wide: [\"nedelja\", \"ponedeljek\", \"torek\", \"sreda\", \"četrtek\", \"petek\", \"sobota\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"d\",\n    pm: \"p\",\n    midnight: \"24.00\",\n    noon: \"12.00\",\n    morning: \"j\",\n    afternoon: \"p\",\n    evening: \"v\",\n    night: \"n\"\n  },\n  abbreviated: {\n    am: \"dop.\",\n    pm: \"pop.\",\n    midnight: \"poln.\",\n    noon: \"pold.\",\n    morning: \"jut.\",\n    afternoon: \"pop.\",\n    evening: \"več.\",\n    night: \"noč\"\n  },\n  wide: {\n    am: \"dop.\",\n    pm: \"pop.\",\n    midnight: \"polnoč\",\n    noon: \"poldne\",\n    morning: \"jutro\",\n    afternoon: \"popoldne\",\n    evening: \"večer\",\n    night: \"noč\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"d\",\n    pm: \"p\",\n    midnight: \"24.00\",\n    noon: \"12.00\",\n    morning: \"zj\",\n    afternoon: \"p\",\n    evening: \"zv\",\n    night: \"po\"\n  },\n  abbreviated: {\n    am: \"dop.\",\n    pm: \"pop.\",\n    midnight: \"opoln.\",\n    noon: \"opold.\",\n    morning: \"zjut.\",\n    afternoon: \"pop.\",\n    evening: \"zveč.\",\n    night: \"ponoči\"\n  },\n  wide: {\n    am: \"dop.\",\n    pm: \"pop.\",\n    midnight: \"opolnoči\",\n    noon: \"opoldne\",\n    morning: \"zjutraj\",\n    afternoon: \"popoldan\",\n    evening: \"zvečer\",\n    night: \"ponoči\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}