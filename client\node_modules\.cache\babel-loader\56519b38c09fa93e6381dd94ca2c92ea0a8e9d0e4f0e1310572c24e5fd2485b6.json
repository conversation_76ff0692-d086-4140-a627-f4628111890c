{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      regular: \"mniej niż sekunda\",\n      past: \"mniej niż sekundę\",\n      future: \"mniej niż sekundę\"\n    },\n    twoFour: \"mniej niż {{count}} sekundy\",\n    other: \"mniej niż {{count}} sekund\"\n  },\n  xSeconds: {\n    one: {\n      regular: \"sekunda\",\n      past: \"sekundę\",\n      future: \"sekundę\"\n    },\n    twoFour: \"{{count}} sekundy\",\n    other: \"{{count}} sekund\"\n  },\n  halfAMinute: {\n    one: \"pół minuty\",\n    twoFour: \"pół minuty\",\n    other: \"pół minuty\"\n  },\n  lessThanXMinutes: {\n    one: {\n      regular: \"mniej niż minuta\",\n      past: \"mniej niż minutę\",\n      future: \"mniej niż minutę\"\n    },\n    twoFour: \"mniej niż {{count}} minuty\",\n    other: \"mniej niż {{count}} minut\"\n  },\n  xMinutes: {\n    one: {\n      regular: \"minuta\",\n      past: \"minutę\",\n      future: \"minutę\"\n    },\n    twoFour: \"{{count}} minuty\",\n    other: \"{{count}} minut\"\n  },\n  aboutXHours: {\n    one: {\n      regular: \"około godziny\",\n      past: \"około godziny\",\n      future: \"około godzinę\"\n    },\n    twoFour: \"około {{count}} godziny\",\n    other: \"około {{count}} godzin\"\n  },\n  xHours: {\n    one: {\n      regular: \"godzina\",\n      past: \"godzinę\",\n      future: \"godzinę\"\n    },\n    twoFour: \"{{count}} godziny\",\n    other: \"{{count}} godzin\"\n  },\n  xDays: {\n    one: {\n      regular: \"dzień\",\n      past: \"dzień\",\n      future: \"1 dzień\"\n    },\n    twoFour: \"{{count}} dni\",\n    other: \"{{count}} dni\"\n  },\n  aboutXWeeks: {\n    one: \"około tygodnia\",\n    twoFour: \"około {{count}} tygodni\",\n    other: \"około {{count}} tygodni\"\n  },\n  xWeeks: {\n    one: \"tydzień\",\n    twoFour: \"{{count}} tygodnie\",\n    other: \"{{count}} tygodni\"\n  },\n  aboutXMonths: {\n    one: \"około miesiąc\",\n    twoFour: \"około {{count}} miesiące\",\n    other: \"około {{count}} miesięcy\"\n  },\n  xMonths: {\n    one: \"miesiąc\",\n    twoFour: \"{{count}} miesiące\",\n    other: \"{{count}} miesięcy\"\n  },\n  aboutXYears: {\n    one: \"około rok\",\n    twoFour: \"około {{count}} lata\",\n    other: \"około {{count}} lat\"\n  },\n  xYears: {\n    one: \"rok\",\n    twoFour: \"{{count}} lata\",\n    other: \"{{count}} lat\"\n  },\n  overXYears: {\n    one: \"ponad rok\",\n    twoFour: \"ponad {{count}} lata\",\n    other: \"ponad {{count}} lat\"\n  },\n  almostXYears: {\n    one: \"prawie rok\",\n    twoFour: \"prawie {{count}} lata\",\n    other: \"prawie {{count}} lat\"\n  }\n};\nfunction declensionGroup(scheme, count) {\n  if (count === 1) {\n    return scheme.one;\n  }\n  const rem100 = count % 100;\n\n  // ends with 11-20\n  if (rem100 <= 20 && rem100 > 10) {\n    return scheme.other;\n  }\n  const rem10 = rem100 % 10;\n\n  // ends with 2, 3, 4\n  if (rem10 >= 2 && rem10 <= 4) {\n    return scheme.twoFour;\n  }\n  return scheme.other;\n}\nfunction declension(scheme, count, time) {\n  const group = declensionGroup(scheme, count);\n  const finalText = typeof group === \"string\" ? group : group[time];\n  return finalText.replace(\"{{count}}\", String(count));\n}\nexport const formatDistance = (token, count, options) => {\n  const scheme = formatDistanceLocale[token];\n  if (!options?.addSuffix) {\n    return declension(scheme, count, \"regular\");\n  }\n  if (options.comparison && options.comparison > 0) {\n    return \"za \" + declension(scheme, count, \"future\");\n  } else {\n    return declension(scheme, count, \"past\") + \" temu\";\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}