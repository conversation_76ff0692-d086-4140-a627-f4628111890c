{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst numberValues = {\n  locale: {\n    1: \"১\",\n    2: \"২\",\n    3: \"৩\",\n    4: \"৪\",\n    5: \"৫\",\n    6: \"৬\",\n    7: \"৭\",\n    8: \"৮\",\n    9: \"৯\",\n    0: \"০\"\n  },\n  number: {\n    \"১\": \"1\",\n    \"২\": \"2\",\n    \"৩\": \"3\",\n    \"৪\": \"4\",\n    \"৫\": \"5\",\n    \"৬\": \"6\",\n    \"৭\": \"7\",\n    \"৮\": \"8\",\n    \"৯\": \"9\",\n    \"০\": \"0\"\n  }\n};\nconst eraValues = {\n  narrow: [\"খ্রিঃপূঃ\", \"খ্রিঃ\"],\n  abbreviated: [\"খ্রিঃপূর্ব\", \"খ্রিঃ\"],\n  wide: [\"খ্রিস্টপূর্ব\", \"খ্রিস্টাব্দ\"]\n};\nconst quarterValues = {\n  narrow: [\"১\", \"২\", \"৩\", \"৪\"],\n  abbreviated: [\"১ত্রৈ\", \"২ত্রৈ\", \"৩ত্রৈ\", \"৪ত্রৈ\"],\n  wide: [\"১ম ত্রৈমাসিক\", \"২য় ত্রৈমাসিক\", \"৩য় ত্রৈমাসিক\", \"৪র্থ ত্রৈমাসিক\"]\n};\nconst monthValues = {\n  narrow: [\"জানু\", \"ফেব্রু\", \"মার্চ\", \"এপ্রিল\", \"মে\", \"জুন\", \"জুলাই\", \"আগস্ট\", \"সেপ্ট\", \"অক্টো\", \"নভে\", \"ডিসে\"],\n  abbreviated: [\"জানু\", \"ফেব্রু\", \"মার্চ\", \"এপ্রিল\", \"মে\", \"জুন\", \"জুলাই\", \"আগস্ট\", \"সেপ্ট\", \"অক্টো\", \"নভে\", \"ডিসে\"],\n  wide: [\"জানুয়ারি\", \"ফেব্রুয়ারি\", \"মার্চ\", \"এপ্রিল\", \"মে\", \"জুন\", \"জুলাই\", \"আগস্ট\", \"সেপ্টেম্বর\", \"অক্টোবর\", \"নভেম্বর\", \"ডিসেম্বর\"]\n};\nconst dayValues = {\n  narrow: [\"র\", \"সো\", \"ম\", \"বু\", \"বৃ\", \"শু\", \"শ\"],\n  short: [\"রবি\", \"সোম\", \"মঙ্গল\", \"বুধ\", \"বৃহ\", \"শুক্র\", \"শনি\"],\n  abbreviated: [\"রবি\", \"সোম\", \"মঙ্গল\", \"বুধ\", \"বৃহ\", \"শুক্র\", \"শনি\"],\n  wide: [\"রবিবার\", \"সোমবার\", \"মঙ্গলবার\", \"বুধবার\", \"বৃহস্পতিবার \", \"শুক্রবার\", \"শনিবার\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"পূ\",\n    pm: \"অপ\",\n    midnight: \"মধ্যরাত\",\n    noon: \"মধ্যাহ্ন\",\n    morning: \"সকাল\",\n    afternoon: \"বিকাল\",\n    evening: \"সন্ধ্যা\",\n    night: \"রাত\"\n  },\n  abbreviated: {\n    am: \"পূর্বাহ্ন\",\n    pm: \"অপরাহ্ন\",\n    midnight: \"মধ্যরাত\",\n    noon: \"মধ্যাহ্ন\",\n    morning: \"সকাল\",\n    afternoon: \"বিকাল\",\n    evening: \"সন্ধ্যা\",\n    night: \"রাত\"\n  },\n  wide: {\n    am: \"পূর্বাহ্ন\",\n    pm: \"অপরাহ্ন\",\n    midnight: \"মধ্যরাত\",\n    noon: \"মধ্যাহ্ন\",\n    morning: \"সকাল\",\n    afternoon: \"বিকাল\",\n    evening: \"সন্ধ্যা\",\n    night: \"রাত\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"পূ\",\n    pm: \"অপ\",\n    midnight: \"মধ্যরাত\",\n    noon: \"মধ্যাহ্ন\",\n    morning: \"সকাল\",\n    afternoon: \"বিকাল\",\n    evening: \"সন্ধ্যা\",\n    night: \"রাত\"\n  },\n  abbreviated: {\n    am: \"পূর্বাহ্ন\",\n    pm: \"অপরাহ্ন\",\n    midnight: \"মধ্যরাত\",\n    noon: \"মধ্যাহ্ন\",\n    morning: \"সকাল\",\n    afternoon: \"বিকাল\",\n    evening: \"সন্ধ্যা\",\n    night: \"রাত\"\n  },\n  wide: {\n    am: \"পূর্বাহ্ন\",\n    pm: \"অপরাহ্ন\",\n    midnight: \"মধ্যরাত\",\n    noon: \"মধ্যাহ্ন\",\n    morning: \"সকাল\",\n    afternoon: \"বিকাল\",\n    evening: \"সন্ধ্যা\",\n    night: \"রাত\"\n  }\n};\nfunction dateOrdinalNumber(number, localeNumber) {\n  if (number > 18 && number <= 31) {\n    return localeNumber + \"শে\";\n  } else {\n    switch (number) {\n      case 1:\n        return localeNumber + \"লা\";\n      case 2:\n      case 3:\n        return localeNumber + \"রা\";\n      case 4:\n        return localeNumber + \"ঠা\";\n      default:\n        return localeNumber + \"ই\";\n    }\n  }\n}\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const localeNumber = numberToLocale(number);\n  const unit = options?.unit;\n  if (unit === \"date\") {\n    return dateOrdinalNumber(number, localeNumber);\n  }\n  if (number > 10 || number === 0) return localeNumber + \"তম\";\n  const rem10 = number % 10;\n  switch (rem10) {\n    case 2:\n    case 3:\n      return localeNumber + \"য়\";\n    case 4:\n      return localeNumber + \"র্থ\";\n    case 6:\n      return localeNumber + \"ষ্ঠ\";\n    default:\n      return localeNumber + \"ম\";\n  }\n};\n\n// function localeToNumber(locale: string): number {\n//   const enNumber = locale.toString().replace(/[১২৩৪৫৬৭৮৯০]/g, function (match) {\n//     return numberValues.number[match as keyof typeof numberValues.number]\n//   })\n//   return Number(enNumber)\n// }\n\nexport function numberToLocale(enNumber) {\n  return enNumber.toString().replace(/\\d/g, function (match) {\n    return numberValues.locale[match];\n  });\n}\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}