{"ast": null, "code": "const accusativeWeekdays = [\"ned<PERSON><PERSON>\", \"ponděl<PERSON>\", \"úter<PERSON>\", \"středu\", \"čtvrtek\", \"pátek\", \"sobotu\"];\nconst formatRelativeLocale = {\n  lastWeek: \"'posledn<PERSON>' eeee 've' p\",\n  yesterday: \"'včera v' p\",\n  today: \"'dnes v' p\",\n  tomorrow: \"'zítra v' p\",\n  nextWeek: date => {\n    const day = date.getDay();\n    return \"'v \" + accusativeWeekdays[day] + \" o' p\";\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}