{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"v.Chr.\", \"n.Chr.\"],\n  abbreviated: [\"v.Chr.\", \"n.Chr.\"],\n  wide: [\"viru Christus\", \"no Christ<PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. Quartal\", \"2. Quartal\", \"3. Quartal\", \"4. Quartal\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"Jan\", \"Feb\", \"Mäe\", \"Abr\", \"Mee\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Okt\", \"Nov\", \"De<PERSON>\"],\n  wide: [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"August\", \"September\", \"<PERSON><PERSON><PERSON>\", \"November\", \"Dezember\"]\n};\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"D\", \"M\", \"D\", \"F\", \"S\"],\n  short: [\"So\", \"Mé\", \"Dë\", \"Më\", \"Do\", \"Fr\", \"Sa\"],\n  abbreviated: [\"So.\", \"Mé.\", \"Dë.\", \"Më.\", \"Do.\", \"Fr.\", \"Sa.\"],\n  wide: [\"Sonndeg\", \"Méindeg\", \"Dënschdeg\", \"Mëttwoch\", \"Donneschdeg\", \"Freideg\", \"Samschdeg\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"mo.\",\n    pm: \"nomë.\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\"\n  },\n  abbreviated: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\"\n  },\n  wide: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"Mëtteg\",\n    morning: \"Moien\",\n    afternoon: \"Nomëtteg\",\n    evening: \"Owend\",\n    night: \"Nuecht\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"mo.\",\n    pm: \"nom.\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\"\n  },\n  abbreviated: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\"\n  },\n  wide: {\n    am: \"moies\",\n    pm: \"nomëttes\",\n    midnight: \"Mëtternuecht\",\n    noon: \"mëttes\",\n    morning: \"moies\",\n    afternoon: \"nomëttes\",\n    evening: \"owes\",\n    night: \"nuets\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}