{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"НТӨ\", \"НТ\"],\n  abbreviated: [\"НТӨ\", \"НТ\"],\n  wide: [\"нийтийн тооллын өмнөх\", \"нийтийн тооллын\"]\n};\nconst quarterValues = {\n  narrow: [\"I\", \"II\", \"III\", \"IV\"],\n  abbreviated: [\"I улирал\", \"II улирал\", \"III улирал\", \"IV улирал\"],\n  wide: [\"1-р улирал\", \"2-р улирал\", \"3-р улирал\", \"4-р улирал\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\", \"VII\", \"VIII\", \"IX\", \"X\", \"XI\", \"XII\"],\n  abbreviated: [\"1-р сар\", \"2-р сар\", \"3-р сар\", \"4-р сар\", \"5-р сар\", \"6-р сар\", \"7-р сар\", \"8-р сар\", \"9-р сар\", \"10-р сар\", \"11-р сар\", \"12-р сар\"],\n  wide: [\"Нэгдүгээр сар\", \"Хоёрдугаар сар\", \"Гуравдугаар сар\", \"Дөрөвдүгээр сар\", \"Тавдугаар сар\", \"Зургаадугаар сар\", \"Долоодугаар сар\", \"Наймдугаар сар\", \"Есдүгээр сар\", \"Аравдугаар сар\", \"Арваннэгдүгээр сар\", \"Арван хоёрдугаар сар\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\", \"VII\", \"VIII\", \"IX\", \"X\", \"XI\", \"XII\"],\n  abbreviated: [\"1-р сар\", \"2-р сар\", \"3-р сар\", \"4-р сар\", \"5-р сар\", \"6-р сар\", \"7-р сар\", \"8-р сар\", \"9-р сар\", \"10-р сар\", \"11-р сар\", \"12-р сар\"],\n  wide: [\"нэгдүгээр сар\", \"хоёрдугаар сар\", \"гуравдугаар сар\", \"дөрөвдүгээр сар\", \"тавдугаар сар\", \"зургаадугаар сар\", \"долоодугаар сар\", \"наймдугаар сар\", \"есдүгээр сар\", \"аравдугаар сар\", \"арваннэгдүгээр сар\", \"арван хоёрдугаар сар\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"Д\", \"М\", \"Л\", \"П\", \"Б\", \"Б\"],\n  short: [\"Ня\", \"Да\", \"Мя\", \"Лх\", \"Пү\", \"Ба\", \"Бя\"],\n  abbreviated: [\"Ням\", \"Дав\", \"Мяг\", \"Лха\", \"Пүр\", \"Баа\", \"Бям\"],\n  wide: [\"Ням\", \"Даваа\", \"Мягмар\", \"Лхагва\", \"Пүрэв\", \"Баасан\", \"Бямба\"]\n};\nconst formattingDayValues = {\n  narrow: [\"Н\", \"Д\", \"М\", \"Л\", \"П\", \"Б\", \"Б\"],\n  short: [\"Ня\", \"Да\", \"Мя\", \"Лх\", \"Пү\", \"Ба\", \"Бя\"],\n  abbreviated: [\"Ням\", \"Дав\", \"Мяг\", \"Лха\", \"Пүр\", \"Баа\", \"Бям\"],\n  wide: [\"ням\", \"даваа\", \"мягмар\", \"лхагва\", \"пүрэв\", \"баасан\", \"бямба\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\"\n  },\n  abbreviated: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\"\n  },\n  wide: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}