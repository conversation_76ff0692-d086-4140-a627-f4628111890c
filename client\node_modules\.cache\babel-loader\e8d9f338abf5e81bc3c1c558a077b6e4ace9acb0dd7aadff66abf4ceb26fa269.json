{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// Ref: https://www.unicode.org/cldr/charts/32/summary/ta.html\n\nconst eraValues = {\n  narrow: [\"கி.மு.\", \"கி.பி.\"],\n  abbreviated: [\"கி.மு.\", \"கி.பி.\"],\n  // CLDR #1624, #1626\n  wide: [\"கிறிஸ்துவுக்கு முன்\", \"அன்னோ டோமினி\"] // CLDR #1620, #1622\n};\nconst quarterValues = {\n  // CLDR #1644 - #1647\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  // CLDR #1636 - #1639\n  abbreviated: [\"காலா.1\", \"காலா.2\", \"காலா.3\", \"காலா.4\"],\n  // CLDR #1628 - #1631\n  wide: [\"ஒன்றாம் காலாண்டு\", \"இரண்டாம் காலாண்டு\", \"மூன்றாம் காலாண்டு\", \"நான்காம் காலாண்டு\"]\n};\nconst monthValues = {\n  // CLDR #700 - #711\n  narrow: [\"ஜ\", \"பி\", \"மா\", \"ஏ\", \"மே\", \"ஜூ\", \"ஜூ\", \"ஆ\", \"செ\", \"அ\", \"ந\", \"டி\"],\n  // CLDR #1676 - #1687\n  abbreviated: [\"ஜன.\", \"பிப்.\", \"மார்.\", \"ஏப்.\", \"மே\", \"ஜூன்\", \"ஜூலை\", \"ஆக.\", \"செப்.\", \"அக்.\", \"நவ.\", \"டிச.\"],\n  // CLDR #1652 - #1663\n  wide: [\"ஜனவரி\",\n  // January\n  \"பிப்ரவரி\",\n  // February\n  \"மார்ச்\",\n  // March\n  \"ஏப்ரல்\",\n  // April\n  \"மே\",\n  // May\n  \"ஜூன்\",\n  // June\n  \"ஜூலை\",\n  // July\n  \"ஆகஸ்ட்\",\n  // August\n  \"செப்டம்பர்\",\n  // September\n  \"அக்டோபர்\",\n  // October\n  \"நவம்பர்\",\n  // November\n  \"டிசம்பர்\" // December\n  ]\n};\nconst dayValues = {\n  // CLDR #1766 - #1772\n  narrow: [\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"],\n  // CLDR #1752 - #1758\n  short: [\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"],\n  // CLDR #1738 - #1744\n  abbreviated: [\"ஞாயி.\", \"திங்.\", \"செவ்.\", \"புத.\", \"வியா.\", \"வெள்.\", \"சனி\"],\n  // CLDR #1724 - #1730\n  wide: [\"ஞாயிறு\",\n  // Sunday\n  \"திங்கள்\",\n  // Monday\n  \"செவ்வாய்\",\n  // Tuesday\n  \"புதன்\",\n  // Wednesday\n  \"வியாழன்\",\n  // Thursday\n  \"வெள்ளி\",\n  // Friday\n  \"சனி\" // Saturday\n  ]\n};\n\n// CLDR #1780 - #1845\nconst dayPeriodValues = {\n  narrow: {\n    am: \"மு.ப\",\n    pm: \"பி.ப\",\n    midnight: \"நள்.\",\n    noon: \"நண்.\",\n    morning: \"கா.\",\n    afternoon: \"மதி.\",\n    evening: \"மா.\",\n    night: \"இர.\"\n  },\n  abbreviated: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\"\n  },\n  wide: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\"\n  }\n};\n\n// CLDR #1780 - #1845\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"மு.ப\",\n    pm: \"பி.ப\",\n    midnight: \"நள்.\",\n    noon: \"நண்.\",\n    morning: \"கா.\",\n    afternoon: \"மதி.\",\n    evening: \"மா.\",\n    night: \"இர.\"\n  },\n  abbreviated: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\"\n  },\n  wide: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}