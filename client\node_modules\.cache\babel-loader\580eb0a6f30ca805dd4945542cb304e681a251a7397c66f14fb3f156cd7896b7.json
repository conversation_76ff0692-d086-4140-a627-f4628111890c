{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"dưới 1 giây\",\n    other: \"dưới {{count}} giây\"\n  },\n  xSeconds: {\n    one: \"1 giây\",\n    other: \"{{count}} giây\"\n  },\n  halfAMinute: \"nửa phút\",\n  lessThanXMinutes: {\n    one: \"dưới 1 phút\",\n    other: \"dưới {{count}} phút\"\n  },\n  xMinutes: {\n    one: \"1 phút\",\n    other: \"{{count}} phút\"\n  },\n  aboutXHours: {\n    one: \"khoảng 1 giờ\",\n    other: \"khoảng {{count}} giờ\"\n  },\n  xHours: {\n    one: \"1 giờ\",\n    other: \"{{count}} giờ\"\n  },\n  xDays: {\n    one: \"1 ngày\",\n    other: \"{{count}} ngày\"\n  },\n  aboutXWeeks: {\n    one: \"khoảng 1 tuần\",\n    other: \"khoảng {{count}} tuần\"\n  },\n  xWeeks: {\n    one: \"1 tuần\",\n    other: \"{{count}} tuần\"\n  },\n  aboutXMonths: {\n    one: \"khoảng 1 tháng\",\n    other: \"khoảng {{count}} tháng\"\n  },\n  xMonths: {\n    one: \"1 tháng\",\n    other: \"{{count}} tháng\"\n  },\n  aboutXYears: {\n    one: \"khoảng 1 năm\",\n    other: \"khoảng {{count}} năm\"\n  },\n  xYears: {\n    one: \"1 năm\",\n    other: \"{{count}} năm\"\n  },\n  overXYears: {\n    one: \"hơn 1 năm\",\n    other: \"hơn {{count}} năm\"\n  },\n  almostXYears: {\n    one: \"gần 1 năm\",\n    other: \"gần {{count}} năm\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" nữa\";\n    } else {\n      return result + \" trước\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}