{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// Vietnamese locale reference: http://www.localeplanet.com/icu/vi-VN/index.html\n// Capitalization reference: http://hcmup.edu.vn/index.php?option=com_content&view=article&id=4106%3Avit-hoa-trong-vn-bn-hanh-chinh&catid=2345%3Atham-kho&Itemid=4103&lang=vi&site=134\n\nconst eraValues = {\n  narrow: [\"TCN\", \"SCN\"],\n  abbreviated: [\"trước CN\", \"sau CN\"],\n  wide: [\"trước Công Nguyên\", \"sau Công Nguyên\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"Quý 1\", \"Quý 2\", \"Quý 3\", \"Quý 4\"]\n};\nconst formattingQuarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  // I notice many news outlet use this \"quý II/2018\"\n  wide: [\"quý I\", \"quý II\", \"quý III\", \"quý IV\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n  abbreviated: [\"Thg 1\", \"Thg 2\", \"Thg 3\", \"Thg 4\", \"Thg 5\", \"Thg 6\", \"Thg 7\", \"Thg 8\", \"Thg 9\", \"Thg 10\", \"Thg 11\", \"Thg 12\"],\n  wide: [\"Tháng Một\", \"Tháng Hai\", \"Tháng Ba\", \"Tháng Tư\", \"Tháng Năm\", \"Tháng Sáu\", \"Tháng Bảy\", \"Tháng Tám\", \"Tháng Chín\", \"Tháng Mười\", \"Tháng Mười Một\", \"Tháng Mười Hai\"]\n};\n// In Vietnamese date formatting, month number less than 10 expected to have leading zero\nconst formattingMonthValues = {\n  narrow: [\"01\", \"02\", \"03\", \"04\", \"05\", \"06\", \"07\", \"08\", \"09\", \"10\", \"11\", \"12\"],\n  abbreviated: [\"thg 1\", \"thg 2\", \"thg 3\", \"thg 4\", \"thg 5\", \"thg 6\", \"thg 7\", \"thg 8\", \"thg 9\", \"thg 10\", \"thg 11\", \"thg 12\"],\n  wide: [\"tháng 01\", \"tháng 02\", \"tháng 03\", \"tháng 04\", \"tháng 05\", \"tháng 06\", \"tháng 07\", \"tháng 08\", \"tháng 09\", \"tháng 10\", \"tháng 11\", \"tháng 12\"]\n};\nconst dayValues = {\n  narrow: [\"CN\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\", \"T7\"],\n  short: [\"CN\", \"Th 2\", \"Th 3\", \"Th 4\", \"Th 5\", \"Th 6\", \"Th 7\"],\n  abbreviated: [\"CN\", \"Thứ 2\", \"Thứ 3\", \"Thứ 4\", \"Thứ 5\", \"Thứ 6\", \"Thứ 7\"],\n  wide: [\"Chủ Nhật\", \"Thứ Hai\", \"Thứ Ba\", \"Thứ Tư\", \"Thứ Năm\", \"Thứ Sáu\", \"Thứ Bảy\"]\n};\n\n// Vietnamese are used to AM/PM borrowing from English, hence `narrow` and\n// `abbreviated` are just like English but I'm leaving the `wide`\n// format being localized with abbreviations found in some systems (SÁng / CHiều);\n// however, personally, I don't think `Chiều` sounds appropriate for `PM`\nconst dayPeriodValues = {\n  // narrow date period is extremely rare in Vietnamese\n  // I used abbreviated form for noon, morning and afternoon\n  // which are regconizable by Vietnamese, others cannot be any shorter\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"nửa đêm\",\n    noon: \"tr\",\n    morning: \"sg\",\n    afternoon: \"ch\",\n    evening: \"tối\",\n    night: \"đêm\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"nửa đêm\",\n    noon: \"trưa\",\n    morning: \"sáng\",\n    afternoon: \"chiều\",\n    evening: \"tối\",\n    night: \"đêm\"\n  },\n  wide: {\n    am: \"SA\",\n    pm: \"CH\",\n    midnight: \"nửa đêm\",\n    noon: \"trưa\",\n    morning: \"sáng\",\n    afternoon: \"chiều\",\n    evening: \"tối\",\n    night: \"đêm\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"nửa đêm\",\n    noon: \"tr\",\n    morning: \"sg\",\n    afternoon: \"ch\",\n    evening: \"tối\",\n    night: \"đêm\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"nửa đêm\",\n    noon: \"trưa\",\n    morning: \"sáng\",\n    afternoon: \"chiều\",\n    evening: \"tối\",\n    night: \"đêm\"\n  },\n  wide: {\n    am: \"SA\",\n    pm: \"CH\",\n    midnight: \"nửa đêm\",\n    noon: \"giữa trưa\",\n    morning: \"vào buổi sáng\",\n    afternoon: \"vào buổi chiều\",\n    evening: \"vào buổi tối\",\n    night: \"vào ban đêm\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  if (unit === \"quarter\") {\n    // many news outlets use \"quý I\"...\n    switch (number) {\n      case 1:\n        return \"I\";\n      case 2:\n        return \"II\";\n      case 3:\n        return \"III\";\n      case 4:\n        return \"IV\";\n    }\n  } else if (unit === \"day\") {\n    // day of week in Vietnamese has ordinal number meaning,\n    // so we should use them, else it'll sound weird\n    switch (number) {\n      case 1:\n        return \"thứ 2\";\n      // meaning 2nd day but it's the first day of the week :D\n      case 2:\n        return \"thứ 3\";\n      // meaning 3rd day\n      case 3:\n        return \"thứ 4\";\n      // meaning 4th day and so on\n      case 4:\n        return \"thứ 5\";\n      case 5:\n        return \"thứ 6\";\n      case 6:\n        return \"thứ 7\";\n      case 7:\n        return \"chủ nhật\";\n      // meaning Sunday, there's no 8th day :D\n    }\n  } else if (unit === \"week\") {\n    if (number === 1) {\n      return \"thứ nhất\";\n    } else {\n      return \"thứ \" + number;\n    }\n  } else if (unit === \"dayOfYear\") {\n    if (number === 1) {\n      return \"đầu tiên\";\n    } else {\n      return \"thứ \" + number;\n    }\n  }\n\n  // there are no different forms of ordinal numbers in Vietnamese\n  return String(number);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}