{"ast": null, "code": "function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"праз \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" таму\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nconst halfAMinute = (_, options) => {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"праз паўхвіліны\";\n    } else {\n      return \"паўхвіліны таму\";\n    }\n  }\n  return \"паўхвіліны\";\n};\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"менш за секунду\",\n      singularNominative: \"менш за {{count}} секунду\",\n      singularGenitive: \"менш за {{count}} секунды\",\n      pluralGenitive: \"менш за {{count}} секунд\"\n    },\n    future: {\n      one: \"менш, чым праз секунду\",\n      singularNominative: \"менш, чым праз {{count}} секунду\",\n      singularGenitive: \"менш, чым праз {{count}} секунды\",\n      pluralGenitive: \"менш, чым праз {{count}} секунд\"\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунды\",\n      pluralGenitive: \"{{count}} секунд\"\n    },\n    past: {\n      singularNominative: \"{{count}} секунду таму\",\n      singularGenitive: \"{{count}} секунды таму\",\n      pluralGenitive: \"{{count}} секунд таму\"\n    },\n    future: {\n      singularNominative: \"праз {{count}} секунду\",\n      singularGenitive: \"праз {{count}} секунды\",\n      pluralGenitive: \"праз {{count}} секунд\"\n    }\n  }),\n  halfAMinute: halfAMinute,\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"менш за хвіліну\",\n      singularNominative: \"менш за {{count}} хвіліну\",\n      singularGenitive: \"менш за {{count}} хвіліны\",\n      pluralGenitive: \"менш за {{count}} хвілін\"\n    },\n    future: {\n      one: \"менш, чым праз хвіліну\",\n      singularNominative: \"менш, чым праз {{count}} хвіліну\",\n      singularGenitive: \"менш, чым праз {{count}} хвіліны\",\n      pluralGenitive: \"менш, чым праз {{count}} хвілін\"\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} хвіліна\",\n      singularGenitive: \"{{count}} хвіліны\",\n      pluralGenitive: \"{{count}} хвілін\"\n    },\n    past: {\n      singularNominative: \"{{count}} хвіліну таму\",\n      singularGenitive: \"{{count}} хвіліны таму\",\n      pluralGenitive: \"{{count}} хвілін таму\"\n    },\n    future: {\n      singularNominative: \"праз {{count}} хвіліну\",\n      singularGenitive: \"праз {{count}} хвіліны\",\n      pluralGenitive: \"праз {{count}} хвілін\"\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} гадзіны\",\n      singularGenitive: \"каля {{count}} гадзін\",\n      pluralGenitive: \"каля {{count}} гадзін\"\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} гадзіну\",\n      singularGenitive: \"прыблізна праз {{count}} гадзіны\",\n      pluralGenitive: \"прыблізна праз {{count}} гадзін\"\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} гадзіна\",\n      singularGenitive: \"{{count}} гадзіны\",\n      pluralGenitive: \"{{count}} гадзін\"\n    },\n    past: {\n      singularNominative: \"{{count}} гадзіну таму\",\n      singularGenitive: \"{{count}} гадзіны таму\",\n      pluralGenitive: \"{{count}} гадзін таму\"\n    },\n    future: {\n      singularNominative: \"праз {{count}} гадзіну\",\n      singularGenitive: \"праз {{count}} гадзіны\",\n      pluralGenitive: \"праз {{count}} гадзін\"\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} дзень\",\n      singularGenitive: \"{{count}} дні\",\n      pluralGenitive: \"{{count}} дзён\"\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} тыдні\",\n      singularGenitive: \"каля {{count}} тыдняў\",\n      pluralGenitive: \"каля {{count}} тыдняў\"\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} тыдзень\",\n      singularGenitive: \"прыблізна праз {{count}} тыдні\",\n      pluralGenitive: \"прыблізна праз {{count}} тыдняў\"\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} тыдзень\",\n      singularGenitive: \"{{count}} тыдні\",\n      pluralGenitive: \"{{count}} тыдняў\"\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} месяца\",\n      singularGenitive: \"каля {{count}} месяцаў\",\n      pluralGenitive: \"каля {{count}} месяцаў\"\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} месяц\",\n      singularGenitive: \"прыблізна праз {{count}} месяцы\",\n      pluralGenitive: \"прыблізна праз {{count}} месяцаў\"\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} месяц\",\n      singularGenitive: \"{{count}} месяцы\",\n      pluralGenitive: \"{{count}} месяцаў\"\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"каля {{count}} года\",\n      singularGenitive: \"каля {{count}} гадоў\",\n      pluralGenitive: \"каля {{count}} гадоў\"\n    },\n    future: {\n      singularNominative: \"прыблізна праз {{count}} год\",\n      singularGenitive: \"прыблізна праз {{count}} гады\",\n      pluralGenitive: \"прыблізна праз {{count}} гадоў\"\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} год\",\n      singularGenitive: \"{{count}} гады\",\n      pluralGenitive: \"{{count}} гадоў\"\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"больш за {{count}} год\",\n      singularGenitive: \"больш за {{count}} гады\",\n      pluralGenitive: \"больш за {{count}} гадоў\"\n    },\n    future: {\n      singularNominative: \"больш, чым праз {{count}} год\",\n      singularGenitive: \"больш, чым праз {{count}} гады\",\n      pluralGenitive: \"больш, чым праз {{count}} гадоў\"\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"амаль {{count}} год\",\n      singularGenitive: \"амаль {{count}} гады\",\n      pluralGenitive: \"амаль {{count}} гадоў\"\n    },\n    future: {\n      singularNominative: \"амаль праз {{count}} год\",\n      singularGenitive: \"амаль праз {{count}} гады\",\n      pluralGenitive: \"амаль праз {{count}} гадоў\"\n    }\n  })\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  return formatDistanceLocale[token](count, options);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}