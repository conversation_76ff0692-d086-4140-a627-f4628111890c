{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1621 - #1630\nconst eraValues = {\n  narrow: [\"ઈસપૂ\", \"ઈસ\"],\n  abbreviated: [\"ઈ.સ.પૂર્વે\", \"ઈ.સ.\"],\n  wide: [\"ઈસવીસન પૂર્વે\", \"ઈસવીસન\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1631 - #1654\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1લો ત્રિમાસ\", \"2જો ત્રિમાસ\", \"3જો ત્રિમાસ\", \"4થો ત્રિમાસ\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1655 - #1726\nconst monthValues = {\n  narrow: [\"જા\", \"ફે\", \"મા\", \"એ\", \"મે\", \"જૂ\", \"જુ\", \"ઓ\", \"સ\", \"ઓ\", \"ન\", \"ડિ\"],\n  abbreviated: [\"જાન્યુ\", \"ફેબ્રુ\", \"માર્ચ\", \"એપ્રિલ\", \"મે\", \"જૂન\", \"જુલાઈ\", \"ઑગસ્ટ\", \"સપ્ટે\", \"ઓક્ટો\", \"નવે\", \"ડિસે\"],\n  wide: [\"જાન્યુઆરી\", \"ફેબ્રુઆરી\", \"માર્ચ\", \"એપ્રિલ\", \"મે\", \"જૂન\", \"જુલાઇ\", \"ઓગસ્ટ\", \"સપ્ટેમ્બર\", \"ઓક્ટોબર\", \"નવેમ્બર\", \"ડિસેમ્બર\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1727 - #1768\nconst dayValues = {\n  narrow: [\"ર\", \"સો\", \"મં\", \"બુ\", \"ગુ\", \"શુ\", \"શ\"],\n  short: [\"ર\", \"સો\", \"મં\", \"બુ\", \"ગુ\", \"શુ\", \"શ\"],\n  abbreviated: [\"રવિ\", \"સોમ\", \"મંગળ\", \"બુધ\", \"ગુરુ\", \"શુક્ર\", \"શનિ\"],\n  wide: [\"રવિવાર\" /* Sunday */, \"સોમવાર\" /* Monday */, \"મંગળવાર\" /* Tuesday */, \"બુધવાર\" /* Wednesday */, \"ગુરુવાર\" /* Thursday */, \"શુક્રવાર\" /* Friday */, \"શનિવાર\" /* Saturday */]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1783 - #1824\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મ.રાત્રિ\",\n    noon: \"બ.\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મ.રાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}