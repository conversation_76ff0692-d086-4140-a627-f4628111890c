{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mai puțin de o secundă\",\n    other: \"mai puțin de {{count}} secunde\"\n  },\n  xSeconds: {\n    one: \"1 secundă\",\n    other: \"{{count}} secunde\"\n  },\n  halfAMinute: \"jumătate de minut\",\n  lessThanXMinutes: {\n    one: \"mai puțin de un minut\",\n    other: \"mai puțin de {{count}} minute\"\n  },\n  xMinutes: {\n    one: \"1 minut\",\n    other: \"{{count}} minute\"\n  },\n  aboutXHours: {\n    one: \"circa 1 oră\",\n    other: \"circa {{count}} ore\"\n  },\n  xHours: {\n    one: \"1 oră\",\n    other: \"{{count}} ore\"\n  },\n  xDays: {\n    one: \"1 zi\",\n    other: \"{{count}} zile\"\n  },\n  aboutXWeeks: {\n    one: \"circa o săptămână\",\n    other: \"circa {{count}} săptămâni\"\n  },\n  xWeeks: {\n    one: \"1 săptămână\",\n    other: \"{{count}} săptămâni\"\n  },\n  aboutXMonths: {\n    one: \"circa 1 lună\",\n    other: \"circa {{count}} luni\"\n  },\n  xMonths: {\n    one: \"1 lună\",\n    other: \"{{count}} luni\"\n  },\n  aboutXYears: {\n    one: \"circa 1 an\",\n    other: \"circa {{count}} ani\"\n  },\n  xYears: {\n    one: \"1 an\",\n    other: \"{{count}} ani\"\n  },\n  overXYears: {\n    one: \"peste 1 an\",\n    other: \"peste {{count}} ani\"\n  },\n  almostXYears: {\n    one: \"aproape 1 an\",\n    other: \"aproape {{count}} ani\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"în \" + result;\n    } else {\n      return result + \" în urmă\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}