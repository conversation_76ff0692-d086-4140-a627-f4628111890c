{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ab. J.C.\", \"apr. J.C.\"],\n  abbreviated: [\"ab. J.C.\", \"apr. J.C.\"],\n  wide: [\"abans <PERSON>\", \"apr<PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  abbreviated: [\"1èr trim.\", \"2nd trim.\", \"3en trim.\", \"4en trim.\"],\n  wide: [\"1èr trimèstre\", \"2nd trimèstre\", \"3en trimèstre\", \"4en trimèstre\"]\n};\nconst monthValues = {\n  narrow: [\"GN\", \"FB\", \"MÇ\", \"AB\", \"MA\", \"JN\", \"JL\", \"AG\", \"ST\", \"OC\", \"NV\", \"DC\"],\n  abbreviated: [\"gen.\", \"febr.\", \"març\", \"abr.\", \"mai\", \"junh\", \"jul.\", \"ag.\", \"set.\", \"oct.\", \"nov.\", \"dec.\"],\n  wide: [\"genièr\", \"febrièr\", \"març\", \"abril\", \"mai\", \"junh\", \"julhet\", \"agost\", \"setembre\", \"octòbre\", \"novembre\", \"decembre\"]\n};\nconst dayValues = {\n  narrow: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  short: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  abbreviated: [\"dg.\", \"dl.\", \"dm.\", \"dc.\", \"dj.\", \"dv.\", \"ds.\"],\n  wide: [\"dimenge\", \"diluns\", \"dimars\", \"dimècres\", \"dijòus\", \"divendres\", \"dissabte\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\"\n  },\n  abbreviated: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"matin\",\n    afternoon: \"aprèp-miègjorn\",\n    evening: \"vèspre\",\n    night: \"nuèch\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\"\n  },\n  wide: {\n    am: \"ante meridiem\",\n    pm: \"post meridiem\",\n    midnight: \"mièjanuèch\",\n    noon: \"miègjorn\",\n    morning: \"del matin\",\n    afternoon: \"de l’aprèp-miègjorn\",\n    evening: \"del ser\",\n    night: \"de la nuèch\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  let ordinal;\n  switch (number) {\n    case 1:\n      ordinal = \"èr\";\n      break;\n    case 2:\n      ordinal = \"nd\";\n      break;\n    default:\n      ordinal = \"en\";\n  }\n\n  // feminine for year, week, hour, minute, second\n  if (unit === \"year\" || unit === \"week\" || unit === \"hour\" || unit === \"minute\" || unit === \"second\") {\n    ordinal += \"a\";\n  }\n  return number + ordinal;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}