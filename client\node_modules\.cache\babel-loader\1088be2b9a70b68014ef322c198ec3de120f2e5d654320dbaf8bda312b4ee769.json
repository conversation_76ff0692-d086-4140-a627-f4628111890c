{"ast": null, "code": "import { addHours } from \"./addHours.js\";\n\n/**\n * The {@link subHours} function options.\n */\n\n/**\n * @name subHours\n * @category Hour Helpers\n * @summary Subtract the specified number of hours from the given date.\n *\n * @description\n * Subtract the specified number of hours from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of hours to be subtracted.\n * @param options - The options\n *\n * @returns The new date with the hours subtracted\n *\n * @example\n * // Subtract 2 hours from 11 July 2014 01:00:00:\n * const result = subHours(new Date(2014, 6, 11, 1, 0), 2)\n * //=> Thu Jul 10 2014 23:00:00\n */\nexport function subHours(date, amount, options) {\n  return addHours(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subHours;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}