{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'trecută la' p\",\n  yesterday: \"'ieri la' p\",\n  today: \"'astăzi la' p\",\n  tomorrow: \"'mâine la' p\",\n  nextWeek: \"eeee 'viitoare la' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}