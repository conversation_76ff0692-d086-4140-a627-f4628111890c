{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)(\\.)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n  abbreviated: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n  wide: /^(viru <PERSON>us|virun eiser Zäitrechnung|no Christus|eiser <PERSON>nung)/i\n};\nconst parseEraPatterns = {\n  any: [/^v/i, /^n/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](\\.)? Quartal/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|feb|mäe|abr|mee|jun|jul|aug|sep|okt|nov|dez)/i,\n  wide: /^(januar|februar|mäerz|abrëll|mee|juni|juli|august|september|oktober|november|dezember)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mä/i, /^ab/i, /^me/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[smdf]/i,\n  short: /^(so|mé|dë|më|do|fr|sa)/i,\n  abbreviated: /^(son?|méi?|dën?|mët?|don?|fre?|sam?)\\.?/i,\n  wide: /^(sonndeg|méindeg|dënschdeg|mëttwoch|donneschdeg|freideg|samschdeg)/i\n};\nconst parseDayPatterns = {\n  any: [/^so/i, /^mé/i, /^dë/i, /^më/i, /^do/i, /^f/i, /^sa/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(mo\\.?|nomë\\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,\n  abbreviated: /^(moi\\.?|nomët\\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,\n  wide: /^(moies|nomëttes|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^m/i,\n    pm: /^n/i,\n    midnight: /^Mëtter/i,\n    noon: /^mëttes/i,\n    morning: /moies/i,\n    afternoon: /nomëttes/i,\n    // will never be matched. Afternoon is matched by `pm`\n    evening: /owes/i,\n    night: /nuets/i // will never be matched. Night is matched by `pm`\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}