{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link startOfTomorrow} function options.\n */\n\n/**\n * @name startOfTomorrow\n * @category Day Helpers\n * @summary Return the start of tomorrow.\n * @pure false\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @returns The start of tomorrow\n *\n * @description\n * Return the start of tomorrow.\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfTomorrow()\n * //=> Tue Oct 7 2014 00:00:00\n */\nexport function startOfTomorrow(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n  const date = constructFrom(options?.in, 0);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfTomorrow;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}