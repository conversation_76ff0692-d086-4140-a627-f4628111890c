{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"پ\", \"د\"],\n  abbreviated: [\"پ-ز\", \"د-ز\"],\n  wide: [\"پێش زاین\", \"دوای زاین\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"چ1م\", \"چ2م\", \"چ3م\", \"چ4م\"],\n  wide: [\"چارەگی یەکەم\", \"چارەگی دووەم\", \"چارەگی سێیەم\", \"چارەگی چوارەم\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ک-د\", \"ش\", \"ئا\", \"ن\", \"م\", \"ح\", \"ت\", \"ئا\", \"ئە\", \"تش-ی\", \"تش-د\", \"ک-ی\"],\n  abbreviated: [\"کان-دوو\", \"شوب\", \"ئاد\", \"نیس\", \"مایس\", \"حوز\", \"تەم\", \"ئاب\", \"ئەل\", \"تش-یەک\", \"تش-دوو\", \"کان-یەک\"],\n  wide: [\"کانوونی دووەم\", \"شوبات\", \"ئادار\", \"نیسان\", \"مایس\", \"حوزەیران\", \"تەمموز\", \"ئاب\", \"ئەیلول\", \"تشرینی یەکەم\", \"تشرینی دووەم\", \"کانوونی یەکەم\"]\n};\nconst dayValues = {\n  narrow: [\"ی-ش\", \"د-ش\", \"س-ش\", \"چ-ش\", \"پ-ش\", \"هە\", \"ش\"],\n  short: [\"یە-شە\", \"دوو-شە\", \"سێ-شە\", \"چو-شە\", \"پێ-شە\", \"هەی\", \"شە\"],\n  abbreviated: [\"یەک-شەم\", \"دوو-شەم\", \"سێ-شەم\", \"چوار-شەم\", \"پێنج-شەم\", \"هەینی\", \"شەمە\"],\n  wide: [\"یەک شەمە\", \"دوو شەمە\", \"سێ شەمە\", \"چوار شەمە\", \"پێنج شەمە\", \"هەینی\", \"شەمە\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"پ\",\n    pm: \"د\",\n    midnight: \"ن-ش\",\n    noon: \"ن\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\"\n  },\n  abbreviated: {\n    am: \"پ-ن\",\n    pm: \"د-ن\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\"\n  },\n  wide: {\n    am: \"پێش نیوەڕۆ\",\n    pm: \"دوای نیوەڕۆ\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"بەیانی\",\n    afternoon: \"دوای نیوەڕۆ\",\n    evening: \"ئێوارە\",\n    night: \"شەو\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"پ\",\n    pm: \"د\",\n    midnight: \"ن-ش\",\n    noon: \"ن\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\"\n  },\n  abbreviated: {\n    am: \"پ-ن\",\n    pm: \"د-ن\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\"\n  },\n  wide: {\n    am: \"پێش نیوەڕۆ\",\n    pm: \"دوای نیوەڕۆ\",\n    midnight: \"نیوە شەو\",\n    noon: \"نیوەڕۆ\",\n    morning: \"لە بەیانیدا\",\n    afternoon: \"لە دوای نیوەڕۆدا\",\n    evening: \"لە ئێوارەدا\",\n    night: \"لە شەودا\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}