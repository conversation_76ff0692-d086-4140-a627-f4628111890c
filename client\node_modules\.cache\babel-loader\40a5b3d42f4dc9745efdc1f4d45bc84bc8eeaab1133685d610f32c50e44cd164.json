{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\nconst dateFormats = {\n  full: \"EEEE, d 'de' MMMM 'de' y\",\n  long: \"d 'de' MMMM 'de' y\",\n  medium: \"d MMM y\",\n  short: \"dd/MM/y\"\n};\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n  full: \"{{date}} 'a las' {{time}}\",\n  long: \"{{date}} 'a las' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\"\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}