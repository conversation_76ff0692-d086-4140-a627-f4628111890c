{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'dernier à' p\",\n  yesterday: \"'hier à' p\",\n  today: \"'aujourd’hui à' p\",\n  tomorrow: \"'demain à' p'\",\n  nextWeek: \"eeee 'prochain à' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}