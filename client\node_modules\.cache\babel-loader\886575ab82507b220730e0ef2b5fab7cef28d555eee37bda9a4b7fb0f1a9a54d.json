{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\nconst dateFormats = {\n  full: \"EEEE, do MMMM, y\",\n  // CLDR #1787\n  long: \"do MMMM, y\",\n  // CLDR #1788\n  medium: \"d MMM, y\",\n  // CLDR #1789\n  short: \"dd/MM/yyyy\" // CLDR #1790\n};\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\",\n  // CLDR #1791\n  long: \"h:mm:ss a z\",\n  // CLDR #1792\n  medium: \"h:mm:ss a\",\n  // CLDR #1793\n  short: \"h:mm a\" // CLDR #1794\n};\nconst dateTimeFormats = {\n  full: \"{{date}} 'को' {{time}}\",\n  // CLDR #1795\n  long: \"{{date}} 'को' {{time}}\",\n  // CLDR #1796\n  medium: \"{{date}}, {{time}}\",\n  // CLDR #1797\n  short: \"{{date}}, {{time}}\" // CLDR #1798\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}