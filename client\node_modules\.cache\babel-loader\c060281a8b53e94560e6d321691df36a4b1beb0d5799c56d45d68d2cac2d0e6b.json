{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"av. J.-C\", \"ap. J.-C\"],\n  abbreviated: [\"av. J.-C\", \"ap. J.-C\"],\n  wide: [\"avant Jésus-Christ\", \"après J<PERSON>us-Christ\"]\n};\nconst quarterValues = {\n  narrow: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  abbreviated: [\"1er trim.\", \"2ème trim.\", \"3ème trim.\", \"4ème trim.\"],\n  wide: [\"1er trimestre\", \"2ème trimestre\", \"3ème trimestre\", \"4ème trimestre\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"janv.\", \"févr.\", \"mars\", \"avr.\", \"mai\", \"juin\", \"juil.\", \"août\", \"sept.\", \"oct.\", \"nov.\", \"déc.\"],\n  wide: [\"janvier\", \"février\", \"mars\", \"avril\", \"mai\", \"juin\", \"juillet\", \"août\", \"septembre\", \"octobre\", \"novembre\", \"décembre\"]\n};\nconst dayValues = {\n  narrow: [\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"],\n  short: [\"di\", \"lu\", \"ma\", \"me\", \"je\", \"ve\", \"sa\"],\n  abbreviated: [\"dim.\", \"lun.\", \"mar.\", \"mer.\", \"jeu.\", \"ven.\", \"sam.\"],\n  wide: [\"dimanche\", \"lundi\", \"mardi\", \"mercredi\", \"jeudi\", \"vendredi\", \"samedi\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"mat.\",\n    afternoon: \"ap.m.\",\n    evening: \"soir\",\n    night: \"mat.\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"matin\",\n    afternoon: \"après-midi\",\n    evening: \"soir\",\n    night: \"matin\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"du matin\",\n    afternoon: \"de l’après-midi\",\n    evening: \"du soir\",\n    night: \"du matin\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  if (number === 0) return \"0\";\n  const feminineUnits = [\"year\", \"week\", \"hour\", \"minute\", \"second\"];\n  let suffix;\n  if (number === 1) {\n    suffix = unit && feminineUnits.includes(unit) ? \"ère\" : \"er\";\n  } else {\n    suffix = \"ème\";\n  }\n  return number + suffix;\n};\nconst LONG_MONTHS_TOKENS = [\"MMM\", \"MMMM\"];\nexport const localize = {\n  preprocessor: (date, parts) => {\n    // Replaces the `do` tokens with `d` when used with long month tokens and the day of the month is greater than one.\n    // Use case \"do MMMM\" => 1er août, 29 août\n    // see https://github.com/date-fns/date-fns/issues/1391\n\n    if (date.getDate() === 1) return parts;\n    const hasLongMonthToken = parts.some(part => part.isToken && LONG_MONTHS_TOKENS.includes(part.value));\n    if (!hasLongMonthToken) return parts;\n    return parts.map(part => part.isToken && part.value === \"do\" ? {\n      isToken: true,\n      value: \"d\"\n    } : part);\n  },\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}