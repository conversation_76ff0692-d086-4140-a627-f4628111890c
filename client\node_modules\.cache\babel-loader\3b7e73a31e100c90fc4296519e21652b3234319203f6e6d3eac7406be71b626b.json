{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'li għadda' 'fil-'p\",\n  yesterday: \"'Il-bieraħ fil-'p\",\n  today: \"'<PERSON>lum fil-'p\",\n  tomorrow: \"'G<PERSON><PERSON> fil-'p\",\n  nextWeek: \"eeee 'fil-'p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}