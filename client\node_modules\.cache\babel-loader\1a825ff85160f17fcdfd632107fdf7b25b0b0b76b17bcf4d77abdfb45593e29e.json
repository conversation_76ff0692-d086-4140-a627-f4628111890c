{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"کمتر از یک ثانیه\",\n    other: \"کمتر از {{count}} ثانیه\"\n  },\n  xSeconds: {\n    one: \"1 ثانیه\",\n    other: \"{{count}} ثانیه\"\n  },\n  halfAMinute: \"نیم دقیقه\",\n  lessThanXMinutes: {\n    one: \"کمتر از یک دقیقه\",\n    other: \"کمتر از {{count}} دقیقه\"\n  },\n  xMinutes: {\n    one: \"1 دقیقه\",\n    other: \"{{count}} دقیقه\"\n  },\n  aboutXHours: {\n    one: \"حدود 1 ساعت\",\n    other: \"حدود {{count}} ساعت\"\n  },\n  xHours: {\n    one: \"1 ساعت\",\n    other: \"{{count}} ساعت\"\n  },\n  xDays: {\n    one: \"1 روز\",\n    other: \"{{count}} روز\"\n  },\n  aboutXWeeks: {\n    one: \"حدود 1 هفته\",\n    other: \"حدود {{count}} هفته\"\n  },\n  xWeeks: {\n    one: \"1 هفته\",\n    other: \"{{count}} هفته\"\n  },\n  aboutXMonths: {\n    one: \"حدود 1 ماه\",\n    other: \"حدود {{count}} ماه\"\n  },\n  xMonths: {\n    one: \"1 ماه\",\n    other: \"{{count}} ماه\"\n  },\n  aboutXYears: {\n    one: \"حدود 1 سال\",\n    other: \"حدود {{count}} سال\"\n  },\n  xYears: {\n    one: \"1 سال\",\n    other: \"{{count}} سال\"\n  },\n  overXYears: {\n    one: \"بیشتر از 1 سال\",\n    other: \"بیشتر از {{count}} سال\"\n  },\n  almostXYears: {\n    one: \"نزدیک 1 سال\",\n    other: \"نزدیک {{count}} سال\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"در \" + result;\n    } else {\n      return result + \" قبل\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}