{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"av. J.-K\", \"ap. J.-K\"],\n  abbreviated: [\"av. J.-K\", \"ap. J.-K\"],\n  wide: [\"<PERSON><PERSON>\", \"ap<PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  abbreviated: [\"1ye trim.\", \"2yèm trim.\", \"3yèm trim.\", \"4yèm trim.\"],\n  wide: [\"1ye trimès\", \"2yèm trimès\", \"3yèm trimès\", \"4yèm trimès\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"O\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"janv.\", \"fevr.\", \"mas\", \"avr.\", \"me\", \"jen\", \"jiy<PERSON>\", \"out\", \"sept.\", \"okt.\", \"nov.\", \"des.\"],\n  wide: [\"janvye\", \"fevrye\", \"mas\", \"avril\", \"me\", \"jen\", \"jiyè\", \"out\", \"septanm\", \"oktòb\", \"novanm\", \"desanm\"]\n};\nconst dayValues = {\n  narrow: [\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"],\n  short: [\"di\", \"le\", \"ma\", \"mè\", \"je\", \"va\", \"sa\"],\n  abbreviated: [\"dim.\", \"len.\", \"mad.\", \"mèk.\", \"jed.\", \"van.\", \"sam.\"],\n  wide: [\"dimanch\", \"lendi\", \"madi\", \"mèkredi\", \"jedi\", \"vandredi\", \"samdi\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minwit\",\n    noon: \"midi\",\n    morning: \"mat.\",\n    afternoon: \"ap.m.\",\n    evening: \"swa\",\n    night: \"mat.\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minwit\",\n    noon: \"midi\",\n    morning: \"maten\",\n    afternoon: \"aprèmidi\",\n    evening: \"swa\",\n    night: \"maten\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minwit\",\n    noon: \"midi\",\n    morning: \"nan maten\",\n    afternoon: \"nan aprèmidi\",\n    evening: \"nan aswè\",\n    night: \"nan maten\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  if (number === 0) return String(number);\n  const suffix = number === 1 ? \"ye\" : \"yèm\";\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}