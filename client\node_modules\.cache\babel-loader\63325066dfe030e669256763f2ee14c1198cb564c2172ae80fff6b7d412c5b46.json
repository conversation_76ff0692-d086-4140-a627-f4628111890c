{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"کەمتر لە یەک چرکە\",\n    other: \"کەمتر لە {{count}} چرکە\"\n  },\n  xSeconds: {\n    one: \"1 چرکە\",\n    other: \"{{count}} چرکە\"\n  },\n  halfAMinute: \"نیو کاتژمێر\",\n  lessThanXMinutes: {\n    one: \"کەمتر لە یەک خولەک\",\n    other: \"کەمتر لە {{count}} خولەک\"\n  },\n  xMinutes: {\n    one: \"1 خولەک\",\n    other: \"{{count}} خولەک\"\n  },\n  aboutXHours: {\n    one: \"دەوروبەری 1 کاتژمێر\",\n    other: \"دەوروبەری {{count}} کاتژمێر\"\n  },\n  xHours: {\n    one: \"1 کاتژمێر\",\n    other: \"{{count}} کاتژمێر\"\n  },\n  xDays: {\n    one: \"1 ڕۆژ\",\n    other: \"{{count}} ژۆژ\"\n  },\n  aboutXWeeks: {\n    one: \"دەوروبەری 1 هەفتە\",\n    other: \"دوروبەری {{count}} هەفتە\"\n  },\n  xWeeks: {\n    one: \"1 هەفتە\",\n    other: \"{{count}} هەفتە\"\n  },\n  aboutXMonths: {\n    one: \"داوروبەری 1 مانگ\",\n    other: \"دەوروبەری {{count}} مانگ\"\n  },\n  xMonths: {\n    one: \"1 مانگ\",\n    other: \"{{count}} مانگ\"\n  },\n  aboutXYears: {\n    one: \"دەوروبەری  1 ساڵ\",\n    other: \"دەوروبەری {{count}} ساڵ\"\n  },\n  xYears: {\n    one: \"1 ساڵ\",\n    other: \"{{count}} ساڵ\"\n  },\n  overXYears: {\n    one: \"زیاتر لە ساڵێک\",\n    other: \"زیاتر لە {{count}} ساڵ\"\n  },\n  almostXYears: {\n    one: \"بەنزیکەیی ساڵێک  \",\n    other: \"بەنزیکەیی {{count}} ساڵ\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"لە ماوەی \" + result + \"دا\";\n    } else {\n      return result + \"پێش ئێستا\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}