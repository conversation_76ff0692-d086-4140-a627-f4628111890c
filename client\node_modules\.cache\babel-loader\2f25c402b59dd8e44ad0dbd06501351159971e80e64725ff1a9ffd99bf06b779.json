{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"më pak se një sekondë\",\n    other: \"më pak se {{count}} sekonda\"\n  },\n  xSeconds: {\n    one: \"1 sekondë\",\n    other: \"{{count}} sekonda\"\n  },\n  halfAMinute: \"gjysëm minuti\",\n  lessThanXMinutes: {\n    one: \"më pak se një minute\",\n    other: \"më pak se {{count}} minuta\"\n  },\n  xMinutes: {\n    one: \"1 minutë\",\n    other: \"{{count}} minuta\"\n  },\n  aboutXHours: {\n    one: \"rreth 1 orë\",\n    other: \"rreth {{count}} orë\"\n  },\n  xHours: {\n    one: \"1 orë\",\n    other: \"{{count}} orë\"\n  },\n  xDays: {\n    one: \"1 ditë\",\n    other: \"{{count}} ditë\"\n  },\n  aboutXWeeks: {\n    one: \"rreth 1 javë\",\n    other: \"rreth {{count}} javë\"\n  },\n  xWeeks: {\n    one: \"1 javë\",\n    other: \"{{count}} javë\"\n  },\n  aboutXMonths: {\n    one: \"rreth 1 muaj\",\n    other: \"rreth {{count}} muaj\"\n  },\n  xMonths: {\n    one: \"1 muaj\",\n    other: \"{{count}} muaj\"\n  },\n  aboutXYears: {\n    one: \"rreth 1 vit\",\n    other: \"rreth {{count}} vite\"\n  },\n  xYears: {\n    one: \"1 vit\",\n    other: \"{{count}} vite\"\n  },\n  overXYears: {\n    one: \"mbi 1 vit\",\n    other: \"mbi {{count}} vite\"\n  },\n  almostXYears: {\n    one: \"pothuajse 1 vit\",\n    other: \"pothuajse {{count}} vite\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"në \" + result;\n    } else {\n      return result + \" më parë\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}