{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل الميلاد\", \"بعد الميلاد\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ر1\", \"ر2\", \"ر3\", \"ر4\"],\n  wide: [\"الربع الأول\", \"الربع الثاني\", \"الربع الثالث\", \"الربع الرابع\"]\n};\nconst monthValues = {\n  narrow: [\"د\", \"ن\", \"أ\", \"س\", \"أ\", \"ج\", \"ج\", \"م\", \"أ\", \"م\", \"ف\", \"ج\"],\n  abbreviated: [\"جانفي\", \"فيفري\", \"مارس\", \"أفريل\", \"ماي\", \"جوان\", \"جويلية\", \"أوت\", \"سب<PERSON>م<PERSON><PERSON>\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"],\n  wide: [\"جانفي\", \"فيفري\", \"مارس\", \"أفريل\", \"ماي\", \"جوان\", \"جويلية\", \"أوت\", \"سبتمبر\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"]\n};\nconst dayValues = {\n  narrow: [\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"],\n  short: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n  abbreviated: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n  wide: [\"الأحد\", \"الاثنين\", \"الثلاثاء\", \"الأربعاء\", \"الخميس\", \"الجمعة\", \"السبت\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\"\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\"\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"الصباح\",\n    noon: \"القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"العشية\",\n    night: \"الليل\",\n    midnight: \"نص الليل\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\"\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\"\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"ع\",\n    morning: \"في الصباح\",\n    noon: \"في القايلة\",\n    afternoon: \"بعد القايلة\",\n    evening: \"في العشية\",\n    night: \"في الليل\",\n    midnight: \"نص الليل\"\n  }\n};\nconst ordinalNumber = num => String(num);\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}