{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"pr.n.e.\", \"AD\"],\n  abbreviated: [\"pr. Kr.\", \"po. Kr.\"],\n  wide: [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1.\", \"2.\", \"3.\", \"4.\"],\n  abbreviated: [\"1. kv.\", \"2. kv.\", \"3. kv.\", \"4. kv.\"],\n  wide: [\"1. kvartal\", \"2. kvartal\", \"3. kvartal\", \"4. kvartal\"]\n};\nconst monthValues = {\n  narrow: [\"1.\", \"2.\", \"3.\", \"4.\", \"5.\", \"6.\", \"7.\", \"8.\", \"9.\", \"10.\", \"11.\", \"12.\"],\n  abbreviated: [\"sij\", \"velj\", \"ožu\", \"tra\", \"svi\", \"lip\", \"srp\", \"kol\", \"ruj\", \"lis\", \"stu\", \"pro\"],\n  wide: [\"sije<PERSON>anj\", \"velja<PERSON><PERSON>\", \"o<PERSON>ujak\", \"travanj\", \"svibanj\", \"lipanj\", \"srpanj\", \"kolovoz\", \"rujan\", \"listopad\", \"studeni\", \"prosinac\"]\n};\nconst formattingMonth<PERSON>alues = {\n  narrow: [\"1.\", \"2.\", \"3.\", \"4.\", \"5.\", \"6.\", \"7.\", \"8.\", \"9.\", \"10.\", \"11.\", \"12.\"],\n  abbreviated: [\"sij\", \"velj\", \"ožu\", \"tra\", \"svi\", \"lip\", \"srp\", \"kol\", \"ruj\", \"lis\", \"stu\", \"pro\"],\n  wide: [\"siječnja\", \"veljače\", \"ožujka\", \"travnja\", \"svibnja\", \"lipnja\", \"srpnja\", \"kolovoza\", \"rujna\", \"listopada\", \"studenog\", \"prosinca\"]\n};\nconst dayValues = {\n  narrow: [\"N\", \"P\", \"U\", \"S\", \"Č\", \"P\", \"S\"],\n  short: [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"],\n  abbreviated: [\"ned\", \"pon\", \"uto\", \"sri\", \"čet\", \"pet\", \"sub\"],\n  wide: [\"nedjelja\", \"ponedjeljak\", \"utorak\", \"srijeda\", \"četvrtak\", \"petak\", \"subota\"]\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"ponoć\",\n    noon: \"podne\",\n    morning: \"ujutro\",\n    afternoon: \"popodne\",\n    evening: \"navečer\",\n    night: \"noću\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"ponoć\",\n    noon: \"podne\",\n    morning: \"ujutro\",\n    afternoon: \"popodne\",\n    evening: \"navečer\",\n    night: \"noću\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"ponoć\",\n    noon: \"podne\",\n    morning: \"ujutro\",\n    afternoon: \"poslije podne\",\n    evening: \"navečer\",\n    night: \"noću\"\n  }\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"ponoć\",\n    noon: \"podne\",\n    morning: \"ujutro\",\n    afternoon: \"popodne\",\n    evening: \"navečer\",\n    night: \"noću\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"ponoć\",\n    noon: \"podne\",\n    morning: \"ujutro\",\n    afternoon: \"popodne\",\n    evening: \"navečer\",\n    night: \"noću\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"ponoć\",\n    noon: \"podne\",\n    morning: \"ujutro\",\n    afternoon: \"poslije podne\",\n    evening: \"navečer\",\n    night: \"noću\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}