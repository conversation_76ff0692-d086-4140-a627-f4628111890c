{"ast": null, "code": "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousSunday} function options.\n */\n\n/**\n * @name previousSunday\n * @category Weekday Helpers\n * @summary When is the previous Sunday?\n *\n * @description\n * When is the previous Sunday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - The options\n *\n * @returns The previous Sunday\n *\n * @example\n * // When is the previous Sunday before Jun, 21, 2021?\n * const result = previousSunday(new Date(2021, 5, 21))\n * //=> Sun June 20 2021 00:00:00\n */\nexport function previousSunday(date, options) {\n  return previousDay(date, 0, options);\n}\n\n// Fallback for modularized imports:\nexport default previousSunday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}