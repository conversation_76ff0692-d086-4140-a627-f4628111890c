{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\n// Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\n\nconst dateFormats = {\n  full: \"EEEE, MMMM d, y\",\n  // CLDR 1816\n  long: \"MMMM d, y\",\n  // CLDR 1817\n  medium: \"MMM d, y\",\n  // CLDR 1818\n  short: \"d/M/yy\" // CLDR 1819\n};\nconst timeFormats = {\n  full: \"hh:mm:ss a zzzz\",\n  // CLDR 1820\n  long: \"hh:mm:ss a z\",\n  // CLDR 1821\n  medium: \"hh:mm:ss a\",\n  // CLDR 1822\n  short: \"hh:mm a\" // CLDR 1823\n};\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  // CLDR 1824\n  long: \"{{date}} {{time}}\",\n  // CLDR 1825\n  medium: \"{{date}} {{time}}\",\n  // CLDR 1826\n  short: \"{{date}} {{time}}\" // CLDR 1827\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}