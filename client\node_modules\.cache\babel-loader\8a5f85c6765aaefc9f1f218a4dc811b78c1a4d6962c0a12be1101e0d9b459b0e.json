{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"segundo bat baino gutxiago\",\n    other: \"{{count}} segundo baino gutxiago\"\n  },\n  xSeconds: {\n    one: \"1 segundo\",\n    other: \"{{count}} segundo\"\n  },\n  halfAMinute: \"minutu erdi\",\n  lessThanXMinutes: {\n    one: \"minutu bat baino gutxiago\",\n    other: \"{{count}} minutu baino gutxiago\"\n  },\n  xMinutes: {\n    one: \"1 minutu\",\n    other: \"{{count}} minutu\"\n  },\n  aboutXHours: {\n    one: \"1 ordu gutxi gorabehera\",\n    other: \"{{count}} ordu gutxi gorabehera\"\n  },\n  xHours: {\n    one: \"1 ordu\",\n    other: \"{{count}} ordu\"\n  },\n  xDays: {\n    one: \"1 egun\",\n    other: \"{{count}} egun\"\n  },\n  aboutXWeeks: {\n    one: \"aste 1 inguru\",\n    other: \"{{count}} aste inguru\"\n  },\n  xWeeks: {\n    one: \"1 aste\",\n    other: \"{{count}} astean\"\n  },\n  aboutXMonths: {\n    one: \"1 hilabete gutxi gorabehera\",\n    other: \"{{count}} hilabete gutxi gorabehera\"\n  },\n  xMonths: {\n    one: \"1 hilabete\",\n    other: \"{{count}} hilabete\"\n  },\n  aboutXYears: {\n    one: \"1 urte gutxi gorabehera\",\n    other: \"{{count}} urte gutxi gorabehera\"\n  },\n  xYears: {\n    one: \"1 urte\",\n    other: \"{{count}} urte\"\n  },\n  overXYears: {\n    one: \"1 urte baino gehiago\",\n    other: \"{{count}} urte baino gehiago\"\n  },\n  almostXYears: {\n    one: \"ia 1 urte\",\n    other: \"ia {{count}} urte\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"en \" + result;\n    } else {\n      return \"duela \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}