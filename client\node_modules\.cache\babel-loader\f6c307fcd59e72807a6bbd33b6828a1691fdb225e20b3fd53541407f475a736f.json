{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: \"តិចជាង {{count}} វិនាទី\",\n  xSeconds: \"{{count}} វិនាទី\",\n  halfAMinute: \"កន្លះនាទី\",\n  lessThanXMinutes: \"តិចជាង {{count}} នាទី\",\n  xMinutes: \"{{count}} នាទី\",\n  aboutXHours: \"ប្រហែល {{count}} ម៉ោង\",\n  xHours: \"{{count}} ម៉ោង\",\n  xDays: \"{{count}} ថ្ងៃ\",\n  aboutXWeeks: \"ប្រហែល {{count}} សប្តាហ៍\",\n  xWeeks: \"{{count}} សប្តាហ៍\",\n  aboutXMonths: \"ប្រហែល {{count}} ខែ\",\n  xMonths: \"{{count}} ខែ\",\n  aboutXYears: \"ប្រហែល {{count}} ឆ្នាំ\",\n  xYears: \"{{count}} ឆ្នាំ\",\n  overXYears: \"ជាង {{count}} ឆ្នាំ\",\n  almostXYears: \"ជិត {{count}} ឆ្នាំ\"\n};\nexport const formatDistance = (token, count, options) => {\n  const tokenValue = formatDistanceLocale[token];\n  let result = tokenValue;\n  if (typeof count === \"number\") {\n    result = result.replace(\"{{count}}\", count.toString());\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"ក្នុងរយៈពេល \" + result;\n    } else {\n      return result + \"មុន\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}