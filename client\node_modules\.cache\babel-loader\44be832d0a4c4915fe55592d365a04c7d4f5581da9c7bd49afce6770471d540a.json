{"ast": null, "code": "// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: \"సెకను కన్నా తక్కువ\",\n      other: \"{{count}} సెకన్ల కన్నా తక్కువ\"\n    },\n    withPreposition: {\n      one: \"సెకను\",\n      other: \"{{count}} సెకన్ల\"\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: \"ఒక సెకను\",\n      // CLDR #1314\n      other: \"{{count}} సెకన్ల\"\n    },\n    withPreposition: {\n      one: \"ఒక సెకను\",\n      other: \"{{count}} సెకన్ల\"\n    }\n  },\n  halfAMinute: {\n    standalone: \"అర నిమిషం\",\n    withPreposition: \"అర నిమిషం\"\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: \"ఒక నిమిషం కన్నా తక్కువ\",\n      other: \"{{count}} నిమిషాల కన్నా తక్కువ\"\n    },\n    withPreposition: {\n      one: \"ఒక నిమిషం\",\n      other: \"{{count}} నిమిషాల\"\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: \"ఒక నిమిషం\",\n      // CLDR #1311\n      other: \"{{count}} నిమిషాలు\"\n    },\n    withPreposition: {\n      one: \"ఒక నిమిషం\",\n      // CLDR #1311\n      other: \"{{count}} నిమిషాల\"\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: \"సుమారు ఒక గంట\",\n      other: \"సుమారు {{count}} గంటలు\"\n    },\n    withPreposition: {\n      one: \"సుమారు ఒక గంట\",\n      other: \"సుమారు {{count}} గంటల\"\n    }\n  },\n  xHours: {\n    standalone: {\n      one: \"ఒక గంట\",\n      // CLDR #1308\n      other: \"{{count}} గంటలు\"\n    },\n    withPreposition: {\n      one: \"ఒక గంట\",\n      other: \"{{count}} గంటల\"\n    }\n  },\n  xDays: {\n    standalone: {\n      one: \"ఒక రోజు\",\n      // CLDR #1292\n      other: \"{{count}} రోజులు\"\n    },\n    withPreposition: {\n      one: \"ఒక రోజు\",\n      other: \"{{count}} రోజుల\"\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: \"సుమారు ఒక వారం\",\n      other: \"సుమారు {{count}} వారాలు\"\n    },\n    withPreposition: {\n      one: \"సుమారు ఒక వారం\",\n      other: \"సుమారు {{count}} వారాలల\"\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: \"ఒక వారం\",\n      other: \"{{count}} వారాలు\"\n    },\n    withPreposition: {\n      one: \"ఒక వారం\",\n      other: \"{{count}} వారాలల\"\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: \"సుమారు ఒక నెల\",\n      other: \"సుమారు {{count}} నెలలు\"\n    },\n    withPreposition: {\n      one: \"సుమారు ఒక నెల\",\n      other: \"సుమారు {{count}} నెలల\"\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: \"ఒక నెల\",\n      // CLDR #1281\n      other: \"{{count}} నెలలు\"\n    },\n    withPreposition: {\n      one: \"ఒక నెల\",\n      other: \"{{count}} నెలల\"\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: \"సుమారు ఒక సంవత్సరం\",\n      other: \"సుమారు {{count}} సంవత్సరాలు\"\n    },\n    withPreposition: {\n      one: \"సుమారు ఒక సంవత్సరం\",\n      other: \"సుమారు {{count}} సంవత్సరాల\"\n    }\n  },\n  xYears: {\n    standalone: {\n      one: \"ఒక సంవత్సరం\",\n      // CLDR #1275\n      other: \"{{count}} సంవత్సరాలు\"\n    },\n    withPreposition: {\n      one: \"ఒక సంవత్సరం\",\n      other: \"{{count}} సంవత్సరాల\"\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: \"ఒక సంవత్సరం పైగా\",\n      other: \"{{count}} సంవత్సరాలకు పైగా\"\n    },\n    withPreposition: {\n      one: \"ఒక సంవత్సరం\",\n      other: \"{{count}} సంవత్సరాల\"\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: \"దాదాపు ఒక సంవత్సరం\",\n      other: \"దాదాపు {{count}} సంవత్సరాలు\"\n    },\n    withPreposition: {\n      one: \"దాదాపు ఒక సంవత్సరం\",\n      other: \"దాదాపు {{count}} సంవత్సరాల\"\n    }\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = options?.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"లో\";\n    } else {\n      return result + \" క్రితం\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}