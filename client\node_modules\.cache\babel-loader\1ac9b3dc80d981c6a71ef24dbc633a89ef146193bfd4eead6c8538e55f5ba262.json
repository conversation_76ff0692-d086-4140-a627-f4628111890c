{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: \"vähem kui üks sekund\",\n      other: \"vähem kui {{count}} sekundit\"\n    },\n    withPreposition: {\n      one: \"vähem kui ühe sekundi\",\n      other: \"vähem kui {{count}} sekundi\"\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: \"üks sekund\",\n      other: \"{{count}} sekundit\"\n    },\n    withPreposition: {\n      one: \"ühe sekundi\",\n      other: \"{{count}} sekundi\"\n    }\n  },\n  halfAMinute: {\n    standalone: \"pool minutit\",\n    withPreposition: \"poole minuti\"\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: \"vähem kui üks minut\",\n      other: \"vähem kui {{count}} minutit\"\n    },\n    withPreposition: {\n      one: \"vähem kui ühe minuti\",\n      other: \"vähem kui {{count}} minuti\"\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: \"üks minut\",\n      other: \"{{count}} minutit\"\n    },\n    withPreposition: {\n      one: \"ühe minuti\",\n      other: \"{{count}} minuti\"\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: \"umbes üks tund\",\n      other: \"umbes {{count}} tundi\"\n    },\n    withPreposition: {\n      one: \"umbes ühe tunni\",\n      other: \"umbes {{count}} tunni\"\n    }\n  },\n  xHours: {\n    standalone: {\n      one: \"üks tund\",\n      other: \"{{count}} tundi\"\n    },\n    withPreposition: {\n      one: \"ühe tunni\",\n      other: \"{{count}} tunni\"\n    }\n  },\n  xDays: {\n    standalone: {\n      one: \"üks päev\",\n      other: \"{{count}} päeva\"\n    },\n    withPreposition: {\n      one: \"ühe päeva\",\n      other: \"{{count}} päeva\"\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: \"umbes üks nädal\",\n      other: \"umbes {{count}} nädalat\"\n    },\n    withPreposition: {\n      one: \"umbes ühe nädala\",\n      other: \"umbes {{count}} nädala\"\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: \"üks nädal\",\n      other: \"{{count}} nädalat\"\n    },\n    withPreposition: {\n      one: \"ühe nädala\",\n      other: \"{{count}} nädala\"\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: \"umbes üks kuu\",\n      other: \"umbes {{count}} kuud\"\n    },\n    withPreposition: {\n      one: \"umbes ühe kuu\",\n      other: \"umbes {{count}} kuu\"\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: \"üks kuu\",\n      other: \"{{count}} kuud\"\n    },\n    withPreposition: {\n      one: \"ühe kuu\",\n      other: \"{{count}} kuu\"\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: \"umbes üks aasta\",\n      other: \"umbes {{count}} aastat\"\n    },\n    withPreposition: {\n      one: \"umbes ühe aasta\",\n      other: \"umbes {{count}} aasta\"\n    }\n  },\n  xYears: {\n    standalone: {\n      one: \"üks aasta\",\n      other: \"{{count}} aastat\"\n    },\n    withPreposition: {\n      one: \"ühe aasta\",\n      other: \"{{count}} aasta\"\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: \"rohkem kui üks aasta\",\n      other: \"rohkem kui {{count}} aastat\"\n    },\n    withPreposition: {\n      one: \"rohkem kui ühe aasta\",\n      other: \"rohkem kui {{count}} aasta\"\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: \"peaaegu üks aasta\",\n      other: \"peaaegu {{count}} aastat\"\n    },\n    withPreposition: {\n      one: \"peaaegu ühe aasta\",\n      other: \"peaaegu {{count}} aasta\"\n    }\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const usageGroup = options?.addSuffix ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n  let result;\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" pärast\";\n    } else {\n      return result + \" eest\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}