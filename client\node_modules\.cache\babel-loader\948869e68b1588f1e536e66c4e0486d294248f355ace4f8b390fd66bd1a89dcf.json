{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"بىر سىكۇنت ئىچىدە\",\n    other: \"سىكۇنت ئىچىدە {{count}}\"\n  },\n  xSeconds: {\n    one: \"بىر سىكۇنت\",\n    other: \"سىكۇنت {{count}}\"\n  },\n  halfAMinute: \"يىرىم مىنۇت\",\n  lessThanXMinutes: {\n    one: \"بىر مىنۇت ئىچىدە\",\n    other: \"مىنۇت ئىچىدە {{count}}\"\n  },\n  xMinutes: {\n    one: \"بىر مىنۇت\",\n    other: \"مىنۇت {{count}}\"\n  },\n  aboutXHours: {\n    one: \"تەخمىنەن بىر سائەت\",\n    other: \"سائەت {{count}} تەخمىنەن\"\n  },\n  xHours: {\n    one: \"بىر سائەت\",\n    other: \"سائەت {{count}}\"\n  },\n  xDays: {\n    one: \"بىر كۈن\",\n    other: \"كۈن {{count}}\"\n  },\n  aboutXWeeks: {\n    one: \"تەخمىنەن بىرھەپتە\",\n    other: \"ھەپتە {{count}} تەخمىنەن\"\n  },\n  xWeeks: {\n    one: \"بىرھەپتە\",\n    other: \"ھەپتە {{count}}\"\n  },\n  aboutXMonths: {\n    one: \"تەخمىنەن بىر ئاي\",\n    other: \"ئاي {{count}} تەخمىنەن\"\n  },\n  xMonths: {\n    one: \"بىر ئاي\",\n    other: \"ئاي {{count}}\"\n  },\n  aboutXYears: {\n    one: \"تەخمىنەن بىر يىل\",\n    other: \"يىل {{count}} تەخمىنەن\"\n  },\n  xYears: {\n    one: \"بىر يىل\",\n    other: \"يىل {{count}}\"\n  },\n  overXYears: {\n    one: \"بىر يىلدىن ئارتۇق\",\n    other: \"يىلدىن ئارتۇق {{count}}\"\n  },\n  almostXYears: {\n    one: \"ئاساسەن بىر يىل\",\n    other: \"يىل {{count}} ئاساسەن\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result;\n    } else {\n      return result + \" بولدى\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}