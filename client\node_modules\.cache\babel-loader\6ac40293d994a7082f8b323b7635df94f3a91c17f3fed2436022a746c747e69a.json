{"ast": null, "code": "const dateLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"P\":\n      return formatLong.date({\n        width: \"short\"\n      });\n    case \"PP\":\n      return formatLong.date({\n        width: \"medium\"\n      });\n    case \"PPP\":\n      return formatLong.date({\n        width: \"long\"\n      });\n    case \"PPPP\":\n    default:\n      return formatLong.date({\n        width: \"full\"\n      });\n  }\n};\nconst timeLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"p\":\n      return formatLong.time({\n        width: \"short\"\n      });\n    case \"pp\":\n      return formatLong.time({\n        width: \"medium\"\n      });\n    case \"ppp\":\n      return formatLong.time({\n        width: \"long\"\n      });\n    case \"pppp\":\n    default:\n      return formatLong.time({\n        width: \"full\"\n      });\n  }\n};\nconst dateTimeLongFormatter = (pattern, formatLong) => {\n  const matchResult = pattern.match(/(P+)(p+)?/) || [];\n  const datePattern = matchResult[1];\n  const timePattern = matchResult[2];\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n  let dateTimeFormat;\n  switch (datePattern) {\n    case \"P\":\n      dateTimeFormat = formatLong.dateTime({\n        width: \"short\"\n      });\n      break;\n    case \"PP\":\n      dateTimeFormat = formatLong.dateTime({\n        width: \"medium\"\n      });\n      break;\n    case \"PPP\":\n      dateTimeFormat = formatLong.dateTime({\n        width: \"long\"\n      });\n      break;\n    case \"PPPP\":\n    default:\n      dateTimeFormat = formatLong.dateTime({\n        width: \"full\"\n      });\n      break;\n  }\n  return dateTimeFormat.replace(\"{{date}}\", dateLongFormatter(datePattern, formatLong)).replace(\"{{time}}\", timeLongFormatter(timePattern, formatLong));\n};\nexport const longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}