{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"MÖ\", \"MS\"],\n  abbreviated: [\"M<PERSON>\", \"<PERSON>\"],\n  wide: [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON> Sonra\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1Ç\", \"2Ç\", \"3Ç\", \"4Ç\"],\n  wide: [\"İlk çeyrek\", \"<PERSON><PERSON><PERSON> Çeyrek\", \"Üçünc<PERSON> çeyrek\", \"Son çeyrek\"]\n};\nconst monthValues = {\n  narrow: [\"O\", \"Ş\", \"M\", \"N\", \"M\", \"H\", \"T\", \"A\", \"E\", \"E\", \"K\", \"A\"],\n  abbreviated: [\"Oca\", \"Şub\", \"Mar\", \"Nis\", \"May\", \"Haz\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\"],\n  wide: [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\"]\n};\nconst dayValues = {\n  narrow: [\"P\", \"P\", \"S\", \"Ç\", \"P\", \"C\", \"C\"],\n  short: [\"Pz\", \"Pt\", \"Sa\", \"Ça\", \"Pe\", \"<PERSON>u\", \"Ct\"],\n  abbreviated: [\"Paz\", \"Pzt\", \"Sal\", \"Çar\", \"Per\", \"Cum\", \"Cts\"],\n  wide: [\"Pazar\", \"Pazartesi\", \"Salı\", \"Çarşamba\", \"Perşembe\", \"Cuma\", \"Cumartesi\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"öö\",\n    pm: \"ös\",\n    midnight: \"gy\",\n    noon: \"ö\",\n    morning: \"sa\",\n    afternoon: \"ös\",\n    evening: \"ak\",\n    night: \"ge\"\n  },\n  abbreviated: {\n    am: \"ÖÖ\",\n    pm: \"ÖS\",\n    midnight: \"gece yarısı\",\n    noon: \"öğle\",\n    morning: \"sabah\",\n    afternoon: \"öğleden sonra\",\n    evening: \"akşam\",\n    night: \"gece\"\n  },\n  wide: {\n    am: \"Ö.Ö.\",\n    pm: \"Ö.S.\",\n    midnight: \"gece yarısı\",\n    noon: \"öğle\",\n    morning: \"sabah\",\n    afternoon: \"öğleden sonra\",\n    evening: \"akşam\",\n    night: \"gece\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"öö\",\n    pm: \"ös\",\n    midnight: \"gy\",\n    noon: \"ö\",\n    morning: \"sa\",\n    afternoon: \"ös\",\n    evening: \"ak\",\n    night: \"ge\"\n  },\n  abbreviated: {\n    am: \"ÖÖ\",\n    pm: \"ÖS\",\n    midnight: \"gece yarısı\",\n    noon: \"öğlen\",\n    morning: \"sabahleyin\",\n    afternoon: \"öğleden sonra\",\n    evening: \"akşamleyin\",\n    night: \"geceleyin\"\n  },\n  wide: {\n    am: \"ö.ö.\",\n    pm: \"ö.s.\",\n    midnight: \"gece yarısı\",\n    noon: \"öğlen\",\n    morning: \"sabahleyin\",\n    afternoon: \"öğleden sonra\",\n    evening: \"akşamleyin\",\n    night: \"geceleyin\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => Number(quarter) - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}