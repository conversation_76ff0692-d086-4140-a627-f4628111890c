{"ast": null, "code": "import { setISODay } from \"../../../setISODay.js\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// ISO day of week\nexport class ISODayParser extends Parser {\n  priority = 90;\n  parse(dateString, token, match) {\n    const valueCallback = value => {\n      if (value === 0) {\n        return 7;\n      }\n      return value;\n    };\n    switch (token) {\n      // 2\n      case \"i\":\n      case \"ii\":\n        // 02\n        return parseNDigits(token.length, dateString);\n      // 2nd\n      case \"io\":\n        return match.ordinalNumber(dateString, {\n          unit: \"day\"\n        });\n      // Tue\n      case \"iii\":\n        return mapValue(match.day(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.day(dateString, {\n          width: \"short\",\n          context: \"formatting\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        }), valueCallback);\n      // T\n      case \"iiiii\":\n        return mapValue(match.day(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        }), valueCallback);\n      // Tu\n      case \"iiiiii\":\n        return mapValue(match.day(dateString, {\n          width: \"short\",\n          context: \"formatting\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        }), valueCallback);\n      // Tuesday\n      case \"iiii\":\n      default:\n        return mapValue(match.day(dateString, {\n          width: \"wide\",\n          context: \"formatting\"\n        }) || match.day(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.day(dateString, {\n          width: \"short\",\n          context: \"formatting\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        }), valueCallback);\n    }\n  }\n  validate(_date, value) {\n    return value >= 1 && value <= 7;\n  }\n  set(date, _flags, value) {\n    date = setISODay(date, value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"y\", \"Y\", \"u\", \"q\", \"Q\", \"M\", \"L\", \"w\", \"d\", \"D\", \"E\", \"e\", \"c\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}