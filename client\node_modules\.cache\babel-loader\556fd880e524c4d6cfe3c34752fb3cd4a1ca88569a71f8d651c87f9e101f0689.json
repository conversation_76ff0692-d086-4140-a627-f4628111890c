{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"أقل من ثانية واحدة\",\n    two: \"أقل من ثانتين\",\n    threeToTen: \"أقل من {{count}} ثواني\",\n    other: \"أقل من {{count}} ثانية\"\n  },\n  xSeconds: {\n    one: \"ثانية واحدة\",\n    two: \"ثانتين\",\n    threeToTen: \"{{count}} ثواني\",\n    other: \"{{count}} ثانية\"\n  },\n  halfAMinute: \"نصف دقيقة\",\n  lessThanXMinutes: {\n    one: \"أقل من دقيقة\",\n    two: \"أقل من دقيقتين\",\n    threeToTen: \"أقل من {{count}} دقائق\",\n    other: \"أقل من {{count}} دقيقة\"\n  },\n  xMinutes: {\n    one: \"دقيقة واحدة\",\n    two: \"دقيقتين\",\n    threeToTen: \"{{count}} دقائق\",\n    other: \"{{count}} دقيقة\"\n  },\n  aboutXHours: {\n    one: \"ساعة واحدة تقريباً\",\n    two: \"ساعتين تقريباً\",\n    threeToTen: \"{{count}} ساعات تقريباً\",\n    other: \"{{count}} ساعة تقريباً\"\n  },\n  xHours: {\n    one: \"ساعة واحدة\",\n    two: \"ساعتين\",\n    threeToTen: \"{{count}} ساعات\",\n    other: \"{{count}} ساعة\"\n  },\n  xDays: {\n    one: \"يوم واحد\",\n    two: \"يومين\",\n    threeToTen: \"{{count}} أيام\",\n    other: \"{{count}} يوم\"\n  },\n  aboutXWeeks: {\n    one: \"أسبوع واحد تقريباً\",\n    two: \"أسبوعين تقريباً\",\n    threeToTen: \"{{count}} أسابيع تقريباً\",\n    other: \"{{count}} أسبوع تقريباً\"\n  },\n  xWeeks: {\n    one: \"أسبوع واحد\",\n    two: \"أسبوعين\",\n    threeToTen: \"{{count}} أسابيع\",\n    other: \"{{count}} أسبوع\"\n  },\n  aboutXMonths: {\n    one: \"شهر واحد تقريباً\",\n    two: \"شهرين تقريباً\",\n    threeToTen: \"{{count}} أشهر تقريباً\",\n    other: \"{{count}} شهر تقريباً\"\n  },\n  xMonths: {\n    one: \"شهر واحد\",\n    two: \"شهرين\",\n    threeToTen: \"{{count}} أشهر\",\n    other: \"{{count}} شهر\"\n  },\n  aboutXYears: {\n    one: \"عام واحد تقريباً\",\n    two: \"عامين تقريباً\",\n    threeToTen: \"{{count}} أعوام تقريباً\",\n    other: \"{{count}} عام تقريباً\"\n  },\n  xYears: {\n    one: \"عام واحد\",\n    two: \"عامين\",\n    threeToTen: \"{{count}} أعوام\",\n    other: \"{{count}} عام\"\n  },\n  overXYears: {\n    one: \"أكثر من عام\",\n    two: \"أكثر من عامين\",\n    threeToTen: \"أكثر من {{count}} أعوام\",\n    other: \"أكثر من {{count}} عام\"\n  },\n  almostXYears: {\n    one: \"عام واحد تقريباً\",\n    two: \"عامين تقريباً\",\n    threeToTen: \"{{count}} أعوام تقريباً\",\n    other: \"{{count}} عام تقريباً\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  const usageGroup = formatDistanceLocale[token];\n  let result;\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace(\"{{count}}\", String(count));\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"في خلال \" + result;\n    } else {\n      return \"منذ \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}