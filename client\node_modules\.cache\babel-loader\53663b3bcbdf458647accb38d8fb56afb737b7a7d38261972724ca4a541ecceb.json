{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: date => {\n    switch (date.getDay()) {\n      case 0:\n        return \"'prošlu nedjelju u' p\";\n      case 3:\n        return \"'prošlu srijedu u' p\";\n      case 6:\n        return \"'prošlu subotu u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  yesterday: \"'jučer u' p\",\n  today: \"'danas u' p\",\n  tomorrow: \"'sutra u' p\",\n  nextWeek: date => {\n    switch (date.getDay()) {\n      case 0:\n        return \"'iduću nedjelju u' p\";\n      case 3:\n        return \"'iduću srijedu u' p\";\n      case 6:\n        return \"'iduću subotu u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}