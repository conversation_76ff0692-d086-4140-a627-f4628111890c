{"ast": null, "code": "function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"через \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" назад\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"меньше секунды\",\n      singularNominative: \"меньше {{count}} секунды\",\n      singularGenitive: \"меньше {{count}} секунд\",\n      pluralGenitive: \"меньше {{count}} секунд\"\n    },\n    future: {\n      one: \"меньше, чем через секунду\",\n      singularNominative: \"меньше, чем через {{count}} секунду\",\n      singularGenitive: \"меньше, чем через {{count}} секунды\",\n      pluralGenitive: \"меньше, чем через {{count}} секунд\"\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунды\",\n      pluralGenitive: \"{{count}} секунд\"\n    },\n    past: {\n      singularNominative: \"{{count}} секунду назад\",\n      singularGenitive: \"{{count}} секунды назад\",\n      pluralGenitive: \"{{count}} секунд назад\"\n    },\n    future: {\n      singularNominative: \"через {{count}} секунду\",\n      singularGenitive: \"через {{count}} секунды\",\n      pluralGenitive: \"через {{count}} секунд\"\n    }\n  }),\n  halfAMinute: (_count, options) => {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return \"через полминуты\";\n      } else {\n        return \"полминуты назад\";\n      }\n    }\n    return \"полминуты\";\n  },\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"меньше минуты\",\n      singularNominative: \"меньше {{count}} минуты\",\n      singularGenitive: \"меньше {{count}} минут\",\n      pluralGenitive: \"меньше {{count}} минут\"\n    },\n    future: {\n      one: \"меньше, чем через минуту\",\n      singularNominative: \"меньше, чем через {{count}} минуту\",\n      singularGenitive: \"меньше, чем через {{count}} минуты\",\n      pluralGenitive: \"меньше, чем через {{count}} минут\"\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} минута\",\n      singularGenitive: \"{{count}} минуты\",\n      pluralGenitive: \"{{count}} минут\"\n    },\n    past: {\n      singularNominative: \"{{count}} минуту назад\",\n      singularGenitive: \"{{count}} минуты назад\",\n      pluralGenitive: \"{{count}} минут назад\"\n    },\n    future: {\n      singularNominative: \"через {{count}} минуту\",\n      singularGenitive: \"через {{count}} минуты\",\n      pluralGenitive: \"через {{count}} минут\"\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"около {{count}} часа\",\n      singularGenitive: \"около {{count}} часов\",\n      pluralGenitive: \"около {{count}} часов\"\n    },\n    future: {\n      singularNominative: \"приблизительно через {{count}} час\",\n      singularGenitive: \"приблизительно через {{count}} часа\",\n      pluralGenitive: \"приблизительно через {{count}} часов\"\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} час\",\n      singularGenitive: \"{{count}} часа\",\n      pluralGenitive: \"{{count}} часов\"\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} день\",\n      singularGenitive: \"{{count}} дня\",\n      pluralGenitive: \"{{count}} дней\"\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"около {{count}} недели\",\n      singularGenitive: \"около {{count}} недель\",\n      pluralGenitive: \"около {{count}} недель\"\n    },\n    future: {\n      singularNominative: \"приблизительно через {{count}} неделю\",\n      singularGenitive: \"приблизительно через {{count}} недели\",\n      pluralGenitive: \"приблизительно через {{count}} недель\"\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} неделя\",\n      singularGenitive: \"{{count}} недели\",\n      pluralGenitive: \"{{count}} недель\"\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"около {{count}} месяца\",\n      singularGenitive: \"около {{count}} месяцев\",\n      pluralGenitive: \"около {{count}} месяцев\"\n    },\n    future: {\n      singularNominative: \"приблизительно через {{count}} месяц\",\n      singularGenitive: \"приблизительно через {{count}} месяца\",\n      pluralGenitive: \"приблизительно через {{count}} месяцев\"\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} месяц\",\n      singularGenitive: \"{{count}} месяца\",\n      pluralGenitive: \"{{count}} месяцев\"\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"около {{count}} года\",\n      singularGenitive: \"около {{count}} лет\",\n      pluralGenitive: \"около {{count}} лет\"\n    },\n    future: {\n      singularNominative: \"приблизительно через {{count}} год\",\n      singularGenitive: \"приблизительно через {{count}} года\",\n      pluralGenitive: \"приблизительно через {{count}} лет\"\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} год\",\n      singularGenitive: \"{{count}} года\",\n      pluralGenitive: \"{{count}} лет\"\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"больше {{count}} года\",\n      singularGenitive: \"больше {{count}} лет\",\n      pluralGenitive: \"больше {{count}} лет\"\n    },\n    future: {\n      singularNominative: \"больше, чем через {{count}} год\",\n      singularGenitive: \"больше, чем через {{count}} года\",\n      pluralGenitive: \"больше, чем через {{count}} лет\"\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"почти {{count}} год\",\n      singularGenitive: \"почти {{count}} года\",\n      pluralGenitive: \"почти {{count}} лет\"\n    },\n    future: {\n      singularNominative: \"почти через {{count}} год\",\n      singularGenitive: \"почти через {{count}} года\",\n      pluralGenitive: \"почти через {{count}} лет\"\n    }\n  })\n};\nexport const formatDistance = (token, count, options) => {\n  return formatDistanceLocale[token](count, options);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}