{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"不到 1 秒\",\n    other: \"不到 {{count}} 秒\"\n  },\n  xSeconds: {\n    one: \"1 秒\",\n    other: \"{{count}} 秒\"\n  },\n  halfAMinute: \"半分钟\",\n  lessThanXMinutes: {\n    one: \"不到 1 分钟\",\n    other: \"不到 {{count}} 分钟\"\n  },\n  xMinutes: {\n    one: \"1 分钟\",\n    other: \"{{count}} 分钟\"\n  },\n  xHours: {\n    one: \"1 小时\",\n    other: \"{{count}} 小时\"\n  },\n  aboutXHours: {\n    one: \"大约 1 小时\",\n    other: \"大约 {{count}} 小时\"\n  },\n  xDays: {\n    one: \"1 天\",\n    other: \"{{count}} 天\"\n  },\n  aboutXWeeks: {\n    one: \"大约 1 个星期\",\n    other: \"大约 {{count}} 个星期\"\n  },\n  xWeeks: {\n    one: \"1 个星期\",\n    other: \"{{count}} 个星期\"\n  },\n  aboutXMonths: {\n    one: \"大约 1 个月\",\n    other: \"大约 {{count}} 个月\"\n  },\n  xMonths: {\n    one: \"1 个月\",\n    other: \"{{count}} 个月\"\n  },\n  aboutXYears: {\n    one: \"大约 1 年\",\n    other: \"大约 {{count}} 年\"\n  },\n  xYears: {\n    one: \"1 年\",\n    other: \"{{count}} 年\"\n  },\n  overXYears: {\n    one: \"超过 1 年\",\n    other: \"超过 {{count}} 年\"\n  },\n  almostXYears: {\n    one: \"将近 1 年\",\n    other: \"将近 {{count}} 年\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"内\";\n    } else {\n      return result + \"前\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}