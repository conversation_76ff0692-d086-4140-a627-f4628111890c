{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\n// DIN 5008: https://de.wikipedia.org/wiki/Datumsformat#DIN_5008\n\nconst dateFormats = {\n  full: \"EEEE, do MMMM y\",\n  // Méindeg, 7. Januar 2018\n  long: \"do MMMM y\",\n  // 7. Januar 2018\n  medium: \"do MMM y\",\n  // 7. Jan 2018\n  short: \"dd.MM.yy\" // 07.01.18\n};\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n  full: \"{{date}} 'um' {{time}}\",\n  long: \"{{date}} 'um' {{time}}\",\n  medium: \"{{date}} {{time}}\",\n  short: \"{{date}} {{time}}\"\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}