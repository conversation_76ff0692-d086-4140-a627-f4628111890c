{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1772\nconst eraValues = {\n  narrow: [\"pred Kr.\", \"po Kr.\"],\n  abbreviated: [\"pred Kr.\", \"po Kr.\"],\n  wide: [\"pred <PERSON><PERSON>\", \"po <PERSON><PERSON><PERSON>\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1780\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. štvrťrok\", \"2. štvrťrok\", \"3. štvrťrok\", \"4. štvrťrok\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1804\nconst monthValues = {\n  narrow: [\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\"jan\", \"feb\", \"mar\", \"apr\", \"máj\", \"jún\", \"júl\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"],\n  wide: [\"január\", \"február\", \"marec\", \"apríl\", \"máj\", \"jún\", \"júl\", \"august\", \"september\", \"október\", \"november\", \"december\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\"jan\", \"feb\", \"mar\", \"apr\", \"máj\", \"jún\", \"júl\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"],\n  wide: [\"januára\", \"februára\", \"marca\", \"apríla\", \"mája\", \"júna\", \"júla\", \"augusta\", \"septembra\", \"októbra\", \"novembra\", \"decembra\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1876\nconst dayValues = {\n  narrow: [\"n\", \"p\", \"u\", \"s\", \"š\", \"p\", \"s\"],\n  short: [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"],\n  abbreviated: [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"],\n  wide: [\"nedeľa\", \"pondelok\", \"utorok\", \"streda\", \"štvrtok\", \"piatok\", \"sobota\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1932\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"poln.\",\n    noon: \"pol.\",\n    morning: \"ráno\",\n    afternoon: \"pop.\",\n    evening: \"več.\",\n    night: \"noc\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"poln.\",\n    noon: \"pol.\",\n    morning: \"ráno\",\n    afternoon: \"popol.\",\n    evening: \"večer\",\n    night: \"noc\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"polnoc\",\n    noon: \"poludnie\",\n    morning: \"ráno\",\n    afternoon: \"popoludnie\",\n    evening: \"večer\",\n    night: \"noc\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o poln.\",\n    noon: \"nap.\",\n    morning: \"ráno\",\n    afternoon: \"pop.\",\n    evening: \"več.\",\n    night: \"v n.\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o poln.\",\n    noon: \"napol.\",\n    morning: \"ráno\",\n    afternoon: \"popol.\",\n    evening: \"večer\",\n    night: \"v noci\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o polnoci\",\n    noon: \"napoludnie\",\n    morning: \"ráno\",\n    afternoon: \"popoludní\",\n    evening: \"večer\",\n    night: \"v noci\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}