{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"น้อยกว่า 1 วินาที\",\n    other: \"น้อยกว่า {{count}} วินาที\"\n  },\n  xSeconds: {\n    one: \"1 วินาที\",\n    other: \"{{count}} วินาที\"\n  },\n  halfAMinute: \"ครึ่งนาที\",\n  lessThanXMinutes: {\n    one: \"น้อยกว่า 1 นาที\",\n    other: \"น้อยกว่า {{count}} นาที\"\n  },\n  xMinutes: {\n    one: \"1 นาที\",\n    other: \"{{count}} นาที\"\n  },\n  aboutXHours: {\n    one: \"ประมาณ 1 ชั่วโมง\",\n    other: \"ประมาณ {{count}} ชั่วโมง\"\n  },\n  xHours: {\n    one: \"1 ชั่วโมง\",\n    other: \"{{count}} ชั่วโมง\"\n  },\n  xDays: {\n    one: \"1 วัน\",\n    other: \"{{count}} วัน\"\n  },\n  aboutXWeeks: {\n    one: \"ประมาณ 1 สัปดาห์\",\n    other: \"ประมาณ {{count}} สัปดาห์\"\n  },\n  xWeeks: {\n    one: \"1 สัปดาห์\",\n    other: \"{{count}} สัปดาห์\"\n  },\n  aboutXMonths: {\n    one: \"ประมาณ 1 เดือน\",\n    other: \"ประมาณ {{count}} เดือน\"\n  },\n  xMonths: {\n    one: \"1 เดือน\",\n    other: \"{{count}} เดือน\"\n  },\n  aboutXYears: {\n    one: \"ประมาณ 1 ปี\",\n    other: \"ประมาณ {{count}} ปี\"\n  },\n  xYears: {\n    one: \"1 ปี\",\n    other: \"{{count}} ปี\"\n  },\n  overXYears: {\n    one: \"มากกว่า 1 ปี\",\n    other: \"มากกว่า {{count}} ปี\"\n  },\n  almostXYears: {\n    one: \"เกือบ 1 ปี\",\n    other: \"เกือบ {{count}} ปี\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (token === \"halfAMinute\") {\n        return \"ใน\" + result;\n      } else {\n        return \"ใน \" + result;\n      }\n    } else {\n      return result + \"ที่ผ่านมา\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}