{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nimport { toDate } from \"../../../toDate.js\";\nconst accusativeWeekdays = [\"нядзелю\", \"панядзелак\", \"аўторак\", \"сераду\", \"чацвер\", \"пятніцу\", \"суботу\"];\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у мінулую \" + weekday + \" а' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у мінулы \" + weekday + \" а' p\";\n  }\n}\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'у \" + weekday + \" а' p\";\n}\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступную \" + weekday + \" а' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступны \" + weekday + \" а' p\";\n  }\n}\nconst lastWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nconst nextWeekFormat = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'учора а' p\",\n  today: \"'сёння а' p\",\n  tomorrow: \"'заўтра а' p\",\n  nextWeek: nextWeekFormat,\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}