{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mindre enn eitt sekund\",\n    other: \"mindre enn {{count}} sekund\"\n  },\n  xSeconds: {\n    one: \"eitt sekund\",\n    other: \"{{count}} sekund\"\n  },\n  halfAMinute: \"eit halvt minutt\",\n  lessThanXMinutes: {\n    one: \"mindre enn eitt minutt\",\n    other: \"mindre enn {{count}} minutt\"\n  },\n  xMinutes: {\n    one: \"eitt minutt\",\n    other: \"{{count}} minutt\"\n  },\n  aboutXHours: {\n    one: \"omtrent ein time\",\n    other: \"omtrent {{count}} timar\"\n  },\n  xHours: {\n    one: \"ein time\",\n    other: \"{{count}} timar\"\n  },\n  xDays: {\n    one: \"ein dag\",\n    other: \"{{count}} dagar\"\n  },\n  aboutXWeeks: {\n    one: \"omtrent ei veke\",\n    other: \"omtrent {{count}} veker\"\n  },\n  xWeeks: {\n    one: \"ei veke\",\n    other: \"{{count}} veker\"\n  },\n  aboutXMonths: {\n    one: \"omtrent ein månad\",\n    other: \"omtrent {{count}} månader\"\n  },\n  xMonths: {\n    one: \"ein månad\",\n    other: \"{{count}} månader\"\n  },\n  aboutXYears: {\n    one: \"omtrent eitt år\",\n    other: \"omtrent {{count}} år\"\n  },\n  xYears: {\n    one: \"eitt år\",\n    other: \"{{count}} år\"\n  },\n  overXYears: {\n    one: \"over eitt år\",\n    other: \"over {{count}} år\"\n  },\n  almostXYears: {\n    one: \"nesten eitt år\",\n    other: \"nesten {{count}} år\"\n  }\n};\nconst wordMapping = [\"null\", \"ein\", \"to\", \"tre\", \"fire\", \"fem\", \"seks\", \"sju\", \"åtte\", \"ni\", \"ti\", \"elleve\", \"tolv\"];\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count < 13 ? wordMapping[count] : String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"om \" + result;\n    } else {\n      return result + \" sidan\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}