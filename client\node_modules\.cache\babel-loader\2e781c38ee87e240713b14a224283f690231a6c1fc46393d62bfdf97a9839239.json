{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\client\\\\messages\\\\ClientMessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport socketService from '../../../services/socketService';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, UserIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, UserCircleIcon, EllipsisHorizontalIcon, PhoneIcon, VideoCameraIcon, InformationCircleIcon, ClockIcon, CheckCircleIcon, XMarkIcon, TrashIcon, ArchiveBoxIcon, StarIcon, UserGroupIcon, CalendarIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON><PERSON><PERSON> sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ClientMessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user !== null && user !== void 0 && user.id) {\n      console.log('🔌 CLIENT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ CLIENT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 CLIENT: Socket connected:', socketConnection.connected);\n        console.log('🏠 CLIENT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n        // Online kullanıcı listesini al\n        socketConnection.emit('get_online_users');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ CLIENT: Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 CLIENT: Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 CLIENT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 CLIENT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', response => {\n            if (response === 'pong') {\n              console.log('💓 CLIENT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ CLIENT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!(user !== null && user !== void 0 && user.id)\n      });\n    }\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user !== null && user !== void 0 && user.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Sayfa yüklendiğinde online users listesini al\n      if (socket.connected) {\n        console.log('🔄 CLIENT: Requesting online users list on page load');\n        socket.emit('get_online_users');\n      }\n\n      // Socket event listeners\n      const handleNewMessage = message => {\n        console.log('📨 CLIENT: Yeni mesaj alındı:', message);\n        console.log('👤 CLIENT: Current user ID:', user.id);\n        console.log('💬 CLIENT: Message sender ID:', message.senderId);\n        console.log('🏠 CLIENT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 CLIENT: Updating conversations list');\n          return prev.map(conv => conv.id === message.conversationId ? {\n            ...conv,\n            lastMessage: message.content,\n            timestamp: message.createdAt,\n            unread: message.senderId !== user.id\n          } : conv);\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 CLIENT: Current selected conversation:', currentSelected === null || currentSelected === void 0 ? void 0 : currentSelected.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ CLIENT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ CLIENT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle\n            if (message.senderId !== user.id) {\n              console.log('📖 CLIENT: Auto-marking message as read since conversation is open');\n              setTimeout(() => {\n                markConversationAsRead(message.conversationId);\n              }, 500); // Kısa bir gecikme ile\n            }\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          } else {\n            console.log('❌ CLIENT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n      const handleMessageSent = message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n      const handleUserStatusChange = data => {\n        console.log('🔄 CLIENT: User status change:', data);\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          console.log('🟢 CLIENT: Online users updated:', Array.from(newSet));\n          return newSet;\n        });\n      };\n      const handleOnlineUsersList = userIds => {\n        console.log('📋 CLIENT: Online users list received:', userIds);\n        setOnlineUsers(new Set(userIds));\n      };\n      const handleUserTyping = data => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n      const handleUserStoppedTyping = data => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n      const handleMessagesRead = data => {\n        console.log('👁️ CLIENT: Messages read event:', data);\n        console.log('👁️ CLIENT: Read by user:', data.readBy, 'Current user:', user.id);\n        console.log('👁️ CLIENT: Message IDs marked as read:', data.messageIds);\n\n        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle\n        if (data.readBy !== user.id) {\n          setMessages(prev => prev.map(msg => {\n            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle\n            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {\n              console.log('👁️ CLIENT: Marking my message as read:', msg.id);\n              return {\n                ...msg,\n                read: true\n              };\n            }\n            // Ayrıca messageIds listesindeki mesajları da güncelle\n            if (data.messageIds.includes(msg.id)) {\n              console.log('👁️ CLIENT: Marking specific message as read:', msg.id);\n              return {\n                ...msg,\n                read: true\n              };\n            }\n            return msg;\n          }));\n\n          // Conversation listesindeki unread durumunu da güncelle\n          setConversations(prev => prev.map(conv => conv.id === data.conversationId ? {\n            ...conv,\n            unread: false\n          } : conv));\n        }\n      };\n\n      // Event listener'ları ekle\n      console.log('🎧 CLIENT: Adding socket event listeners...');\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      socket.on('online_users_list', handleOnlineUsersList);\n      console.log('✅ CLIENT: Socket event listeners added');\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n        socket.off('online_users_list', handleOnlineUsersList);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 CLIENT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 CLIENT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          expertId: conversation.otherUser.id,\n          expertName: conversation.otherUser.name,\n          expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n\n      // Conversation'ı seçildiğinde unread durumunu false yap\n      setConversations(prev => prev.map(conv => conv.id === selectedConversation.id ? {\n        ...conv,\n        unread: false\n      } : conv));\n    }\n  }, [selectedConversation]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async conversationId => {\n    try {\n      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)\n      const response = await api.put(`/messages/conversations/${conversationId}/read`);\n      if (response.data.updatedCount > 0) {\n        console.log('📖 CLIENT: Marked', response.data.updatedCount, 'messages as read');\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg => msg.senderId !== user.id ? {\n          ...msg,\n          read: true\n        } : msg));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Conversations yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true,\n        // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.expertId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.expertId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-50 min-h-screen pb-12\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-white\",\n              children: \"Mesajlar\\u0131m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-pink-100\",\n              children: \"Uzmanlar\\u0131n\\u0131zla g\\xFCvenli bir \\u015Fekilde ileti\\u015Fim kurun\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 sm:mt-0 flex space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/client/experts\",\n              className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\",\n              children: [/*#__PURE__*/_jsxDEV(UserGroupIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this), \"Uzmanlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/client/appointments\",\n              className: \"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\",\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this), \"Randevular\\u0131m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-12 h-[75vh]\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-semibold text-gray-800 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                  className: \"h-6 w-6 text-teal-600 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 19\n                }, this), \"Mesajlar\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Konu\\u015Fmalarda ara...\",\n                  className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('all'),\n                  children: \"T\\xFCm\\xFC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('unread'),\n                  children: \"Okunmam\\u0131\\u015F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('starred'),\n                  children: \"Y\\u0131ld\\u0131zl\\u0131\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                  onClick: () => setFilter('archived'),\n                  children: \"Ar\\u015Fiv\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: conversationsRef,\n              style: {\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 text-center text-gray-500\",\n                children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 19\n              }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-teal-50' : ''} ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`,\n                onClick: () => handleSelectConversation(conversation),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative flex-shrink-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: conversation.avatar,\n                      alt: conversation.expertName,\n                      className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-teal-600' : ''}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 624,\n                      columnNumber: 27\n                    }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                        children: conversation.expertName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: \"h-4 w-4 text-yellow-400 fill-current\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 644,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatMessageDate(conversation.timestamp)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 646,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 642,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: conversation.expertTitle\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 651,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                      children: conversation.lastMessage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: onlineUsers.has(conversation.expertId) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-green-500 font-medium\",\n                          children: \"\\xC7evrimi\\xE7i\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 660,\n                          columnNumber: 35\n                        }, this) : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 658,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            toggleStar(conversation.id);\n                          },\n                          className: \"text-gray-400 hover:text-yellow-400\",\n                          children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                            className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 673,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 666,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            toggleArchive(conversation.id);\n                          },\n                          className: \"text-gray-400 hover:text-gray-600\",\n                          children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 682,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 675,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 23\n                }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white\",\n                  children: \"Yeni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 25\n                }, this)]\n              }, conversation.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-12 md:col-span-8 flex flex-col\",\n            children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative mr-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: selectedConversation.avatar,\n                      alt: selectedConversation.expertName,\n                      className: \"h-10 w-10 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 706,\n                      columnNumber: 25\n                    }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg font-medium text-gray-800\",\n                      children: selectedConversation.expertName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [selectedConversation.expertTitle, \" \", ' • ', selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/experts/${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(UserIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 734,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/appointments?expert=${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(ClockIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/client/sessions?expert=${selectedConversation.expertId}`,\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 746,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                    children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 749,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesContainerRef,\n                className: \"p-4 bg-gray-50\",\n                style: {\n                  height: 'calc(75vh - 195px)',\n                  overflowY: 'auto',\n                  scrollbarWidth: 'thin',\n                  scrollbarColor: '#D1D5DB #F3F4F6'\n                },\n                children: [messages.map((message, index) => {\n                  const isSender = message.senderId === user.id;\n                  const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                    children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: message.senderAvatar,\n                      alt: message.senderName,\n                      className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 772,\n                      columnNumber: 29\n                    }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 778,\n                      columnNumber: 56\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-teal-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm\",\n                        children: message.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 786,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`,\n                        children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                          className: `h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`,\n                          title: message.read ? 'Okundu' : 'İletildi'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 790,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 787,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 779,\n                      columnNumber: 27\n                    }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: message.senderAvatar,\n                      alt: message.senderName,\n                      className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 798,\n                      columnNumber: 29\n                    }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 ml-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 55\n                    }, this)]\n                  }, message.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  ref: messagesEndRef\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3 border-t border-gray-200 bg-white\",\n                children: /*#__PURE__*/_jsxDEV(\"form\", {\n                  onSubmit: handleSendMessage,\n                  className: \"flex items-end\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 818,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none\",\n                      placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                      rows: \"2\",\n                      value: messageText,\n                      onChange: e => setMessageText(e.target.value),\n                      onKeyDown: e => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault();\n                          sendMessage();\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 821,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 839,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"submit\",\n                    disabled: !messageText.trim(),\n                    className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-teal-600 text-white hover:bg-teal-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                    children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 850,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Mesaj seçilmediğinde\n            _jsxDEV(\"div\", {\n              className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full max-w-md text-center\",\n                children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                  className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-medium text-gray-800 mb-2\",\n                  children: \"Mesajlar\\u0131n\\u0131z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500 mx-auto\",\n                  children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir uzmanla ileti\\u015Fime ge\\xE7in.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/client/experts\",\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500\",\n                    children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                      className: \"-ml-1 mr-2 h-5 w-5\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 25\n                    }, this), \"Uzmanlar\\u0131 Ke\\u015Ffedin\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 515,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientMessagesPage, \"8B7RxxYStSTw62EYx5HVIfMSfbM=\", false, function () {\n  return [useAuth];\n});\n_c = ClientMessagesPage;\nexport default ClientMessagesPage;\nvar _c;\n$RefreshReg$(_c, \"ClientMessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "api", "toast", "socketService", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "UserIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "UserCircleIcon", "EllipsisHorizontalIcon", "PhoneIcon", "VideoCameraIcon", "InformationCircleIcon", "ClockIcon", "CheckCircleIcon", "XMarkIcon", "TrashIcon", "ArchiveBoxIcon", "StarIcon", "UserGroupIcon", "CalendarIcon", "format", "tr", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ClientMessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "typingUsers", "setTypingUsers", "messagesEndRef", "conversationsRef", "messagesContainerRef", "token", "localStorage", "getItem", "id", "console", "log", "socketConnection", "io", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "on", "connected", "rooms", "emit", "reason", "error", "attemptNumber", "heartbeatInterval", "setInterval", "response", "clearInterval", "disconnect", "hasToken", "<PERSON><PERSON>ser", "handleNewMessage", "message", "senderId", "conversationId", "prev", "map", "conv", "lastMessage", "content", "timestamp", "createdAt", "unread", "currentSelected", "messageExists", "some", "msg", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "delivered", "setTimeout", "markConversationAsRead", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleMessageSent", "handleUserStatusChange", "data", "newSet", "status", "add", "userId", "delete", "Array", "from", "handleOnlineUsersList", "userIds", "handleUserTyping", "handleUserStoppedTyping", "handleMessagesRead", "readBy", "messageIds", "includes", "off", "loadConversations", "joinedConversations", "setJoinedConversations", "length", "for<PERSON>ach", "conversation", "has", "get", "formattedConversations", "_conversation$lastMes", "_conversation$lastMes2", "expertId", "otherUser", "expertName", "expert<PERSON><PERSON>le", "role", "avatar", "starred", "archived", "loadMessages", "put", "updatedCount", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "src", "alt", "lastSeen", "stopPropagation", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "onKeyDown", "key", "shift<PERSON>ey", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/client/messages/ClientMessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport socketService from '../../../services/socketService';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  UserCircleIcon,\n  EllipsisHorizontalIcon,\n  PhoneIcon,\n  VideoCameraIcon,\n  InformationCircleIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XMarkIcon,\n  TrashIcon,\n  ArchiveBoxIcon,\n  StarIcon,\n  UserGroupIcon,\n  CalendarIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON><PERSON><PERSON> mesajlaş<PERSON> sayfası\n */\nconst ClientMessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    const token = localStorage.getItem('accessToken');\n    if (token && user?.id) {\n      console.log('🔌 CLIENT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        timeout: 20000\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ CLIENT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 CLIENT: Socket connected:', socketConnection.connected);\n        console.log('🏠 CLIENT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n        // Online kullanıcı listesini al\n        socketConnection.emit('get_online_users');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ CLIENT: Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 CLIENT: Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 CLIENT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 CLIENT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', (response) => {\n            if (response === 'pong') {\n              console.log('💓 CLIENT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        clearInterval(heartbeatInterval);\n        socketConnection.disconnect();\n      };\n    } else {\n      console.log('❌ CLIENT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!user?.id\n      });\n    }\n  }, [user?.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user?.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Sayfa yüklendiğinde online users listesini al\n      if (socket.connected) {\n        console.log('🔄 CLIENT: Requesting online users list on page load');\n        socket.emit('get_online_users');\n      }\n\n      // Socket event listeners\n      const handleNewMessage = (message) => {\n        console.log('📨 CLIENT: Yeni mesaj alındı:', message);\n        console.log('👤 CLIENT: Current user ID:', user.id);\n        console.log('💬 CLIENT: Message sender ID:', message.senderId);\n        console.log('🏠 CLIENT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 CLIENT: Updating conversations list');\n          return prev.map(conv =>\n            conv.id === message.conversationId\n              ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n              : conv\n          );\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 CLIENT: Current selected conversation:', currentSelected?.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ CLIENT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ CLIENT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle\n            if (message.senderId !== user.id) {\n              console.log('📖 CLIENT: Auto-marking message as read since conversation is open');\n              setTimeout(() => {\n                markConversationAsRead(message.conversationId);\n              }, 500); // Kısa bir gecikme ile\n            }\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          } else {\n            console.log('❌ CLIENT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleMessageSent = (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n\n      const handleUserStatusChange = (data) => {\n        console.log('🔄 CLIENT: User status change:', data);\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          console.log('🟢 CLIENT: Online users updated:', Array.from(newSet));\n          return newSet;\n        });\n      };\n\n      const handleOnlineUsersList = (userIds) => {\n        console.log('📋 CLIENT: Online users list received:', userIds);\n        setOnlineUsers(new Set(userIds));\n      };\n\n      const handleUserTyping = (data) => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleUserStoppedTyping = (data) => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      const handleMessagesRead = (data) => {\n        console.log('👁️ CLIENT: Messages read event:', data);\n        console.log('👁️ CLIENT: Read by user:', data.readBy, 'Current user:', user.id);\n        console.log('👁️ CLIENT: Message IDs marked as read:', data.messageIds);\n\n        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle\n        if (data.readBy !== user.id) {\n          setMessages(prev => prev.map(msg => {\n            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle\n            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {\n              console.log('👁️ CLIENT: Marking my message as read:', msg.id);\n              return { ...msg, read: true };\n            }\n            // Ayrıca messageIds listesindeki mesajları da güncelle\n            if (data.messageIds.includes(msg.id)) {\n              console.log('👁️ CLIENT: Marking specific message as read:', msg.id);\n              return { ...msg, read: true };\n            }\n            return msg;\n          }));\n\n          // Conversation listesindeki unread durumunu da güncelle\n          setConversations(prev => prev.map(conv =>\n            conv.id === data.conversationId\n              ? { ...conv, unread: false }\n              : conv\n          ));\n        }\n      };\n\n      // Event listener'ları ekle\n      console.log('🎧 CLIENT: Adding socket event listeners...');\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      socket.on('online_users_list', handleOnlineUsersList);\n      console.log('✅ CLIENT: Socket event listeners added');\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n        socket.off('online_users_list', handleOnlineUsersList);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 CLIENT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 CLIENT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        expertId: conversation.otherUser.id,\n        expertName: conversation.otherUser.name,\n        expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n\n      // Conversation'ı seçildiğinde unread durumunu false yap\n      setConversations(prev => prev.map(conv =>\n        conv.id === selectedConversation.id\n          ? { ...conv, unread: false }\n          : conv\n      ));\n    }\n  }, [selectedConversation]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async (conversationId) => {\n    try {\n      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)\n      const response = await api.put(`/messages/conversations/${conversationId}/read`);\n\n      if (response.data.updatedCount > 0) {\n        console.log('📖 CLIENT: Marked', response.data.updatedCount, 'messages as read');\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg =>\n          msg.senderId !== user.id ? { ...msg, read: true } : msg\n        ));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Conversations yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true, // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.expertId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.expertId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-gray-50 min-h-screen pb-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\">\n        {/* Başlık ve Üst Kısım */}\n        <div className=\"bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-white\">Mesajlarım</h1>\n              <p className=\"mt-1 text-pink-100\">\n                Uzmanlarınızla güvenli bir şekilde iletişim kurun\n              </p>\n            </div>\n            <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n              <Link\n                to=\"/client/experts\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\"\n              >\n                <UserGroupIcon className=\"h-4 w-4 mr-2\" />\n                Uzmanlar\n              </Link>\n              <Link\n                to=\"/client/appointments\"\n                className=\"inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150\"\n              >\n                <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                Randevularım\n              </Link>\n            </div>\n          </div>\n        </div>\n        \n        {/* Mesajlaşma arayüzü */}\n        <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n          <div className=\"grid grid-cols-12 h-[75vh]\">\n            {/* Sol Kenar - Konuşma Listesi */}\n            <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n              <div className=\"p-4 border-b border-gray-200 bg-white\">\n                <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-teal-600 mr-2\" />\n                  Mesajlar\n                </h1>\n                <div className=\"mt-3 relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Konuşmalarda ara...\"\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                  />\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n                </div>\n                <div className=\"mt-3 flex space-x-2\">\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('all')}\n                  >\n                    Tümü\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('unread')}\n                  >\n                    Okunmamış\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('starred')}\n                  >\n                    Yıldızlı\n                  </button>\n                  <button\n                    className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                      ? 'bg-teal-100 text-teal-800' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                    onClick={() => setFilter('archived')}\n                  >\n                    Arşiv\n                  </button>\n                </div>\n              </div>\n              <div \n                ref={conversationsRef}\n                style={{\n                  height: 'calc(75vh - 145px)',\n                  overflowY: 'auto',\n                  scrollbarWidth: 'thin',\n                  scrollbarColor: '#D1D5DB #F3F4F6'\n                }}\n              >\n                {filteredConversations.length === 0 ? (\n                  <div className=\"p-4 text-center text-gray-500\">\n                    Hiç mesajınız yok\n                  </div>\n                ) : (\n                  filteredConversations.map(conversation => (\n                    <div\n                      key={conversation.id}\n                      className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                        selectedConversation?.id === conversation.id ? 'bg-teal-50' : ''\n                      } ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`}\n                      onClick={() => handleSelectConversation(conversation)}\n                    >\n                      <div className=\"flex items-start space-x-3\">\n                        <div className=\"relative flex-shrink-0\">\n                          <img\n                            src={conversation.avatar}\n                            alt={conversation.expertName}\n                            className={`h-10 w-10 rounded-full ${\n                              selectedConversation?.id === conversation.id \n                                ? 'ring-2 ring-teal-600' \n                                : ''\n                            }`}\n                          />\n                          {conversation.status === 'online' && (\n                            <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                          )}\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex justify-between items-start\">\n                            <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                              {conversation.expertName}\n                            </h3>\n                            <div className=\"flex items-center space-x-1\">\n                              {conversation.starred && (\n                                <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                              )}\n                              <span className=\"text-xs text-gray-500\">\n                                {formatMessageDate(conversation.timestamp)}\n                              </span>\n                            </div>\n                          </div>\n                          <p className=\"text-xs text-gray-500\">{conversation.expertTitle}</p>\n                          <p className={`text-sm truncate mt-1 ${\n                            conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                          }`}>\n                            {conversation.lastMessage}\n                          </p>\n                          <div className=\"flex justify-between items-center mt-1\">\n                            <span className=\"text-xs text-gray-500\">\n                              {onlineUsers.has(conversation.expertId)\n                                ? <span className=\"text-green-500 font-medium\">Çevrimiçi</span>\n                                : conversation.lastSeen\n                                  ? `Son görülme: ${conversation.lastSeen}`\n                                  : ''}\n                            </span>\n                            <div className=\"flex space-x-1\">\n                              <button \n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  toggleStar(conversation.id);\n                                }}\n                                className=\"text-gray-400 hover:text-yellow-400\"\n                              >\n                                <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                              </button>\n                              <button \n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  toggleArchive(conversation.id);\n                                }}\n                                className=\"text-gray-400 hover:text-gray-600\"\n                              >\n                                <ArchiveBoxIcon className=\"h-4 w-4\" />\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      {conversation.unread && (\n                        <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white\">\n                          Yeni\n                        </span>\n                      )}\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n            {/* Sağ Taraf - Mesaj Alanı */}\n            <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n              {selectedConversation ? (\n                <>\n                  {/* Mesajlaşma Başlığı */}\n                  <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                    <div className=\"flex items-center\">\n                      <div className=\"relative mr-3\">\n                        <img\n                          src={selectedConversation.avatar}\n                          alt={selectedConversation.expertName}\n                          className=\"h-10 w-10 rounded-full\"\n                        />\n                        {selectedConversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div>\n                        <h2 className=\"text-lg font-medium text-gray-800\">\n                          {selectedConversation.expertName}\n                        </h2>\n                        <p className=\"text-xs text-gray-500\">\n                          {selectedConversation.expertTitle} {' • '}\n                          {selectedConversation.status === 'online' \n                            ? 'Çevrimiçi' \n                            : selectedConversation.lastSeen \n                              ? `Son görülme: ${selectedConversation.lastSeen}` \n                              : ''}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <Link \n                        to={`/client/experts/${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <UserIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <Link \n                        to={`/client/appointments?expert=${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <ClockIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <Link \n                        to={`/client/sessions?expert=${selectedConversation.expertId}`}\n                        className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\"\n                      >\n                        <VideoCameraIcon className=\"h-5 w-5\" />\n                      </Link>\n                      <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                        <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Mesaj Alanı */}\n                  <div \n                    ref={messagesContainerRef}\n                    className=\"p-4 bg-gray-50\"\n                    style={{\n                      height: 'calc(75vh - 195px)',\n                      overflowY: 'auto',\n                      scrollbarWidth: 'thin',\n                      scrollbarColor: '#D1D5DB #F3F4F6'\n                    }}\n                  >\n                    {messages.map((message, index) => {\n                      const isSender = message.senderId === user.id;\n                      const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                      \n                      return (\n                        <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                          {!isSender && showAvatar && (\n                            <img \n                              src={message.senderAvatar} \n                              alt={message.senderName}\n                              className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                            />\n                          )}\n                          {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                          <div \n                            className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                              isSender \n                                ? 'bg-teal-600 text-white rounded-br-none' \n                                : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                            }`}\n                          >\n                            <p className=\"text-sm\">{message.text}</p>\n                            <div className={`text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`}>\n                              {formatMessageDate(message.timestamp)}\n                              {isSender && (\n                                <CheckCircleIcon\n                                  className={`h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`}\n                                  title={message.read ? 'Okundu' : 'İletildi'}\n                                />\n                              )}\n                            </div>\n                          </div>\n                          {isSender && showAvatar && (\n                            <img \n                              src={message.senderAvatar}\n                              alt={message.senderName}\n                              className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                            />\n                          )}\n                          {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                        </div>\n                      );\n                    })}\n                    <div ref={messagesEndRef} />\n                  </div>\n\n                  {/* Mesaj Giriş Alanı */}\n                  <div className=\"p-3 border-t border-gray-200 bg-white\">\n                    <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                      <button\n                        type=\"button\"\n                        className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                      >\n                        <PaperClipIcon className=\"h-5 w-5\" />\n                      </button>\n                      <div className=\"flex-1 mx-2\">\n                        <textarea\n                          className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none\"\n                          placeholder=\"Mesajınızı yazın...\"\n                          rows=\"2\"\n                          value={messageText}\n                          onChange={(e) => setMessageText(e.target.value)}\n                          onKeyDown={(e) => {\n                            if (e.key === 'Enter' && !e.shiftKey) {\n                              e.preventDefault();\n                              sendMessage();\n                            }\n                          }}\n                        ></textarea>\n                      </div>\n                      <button\n                        type=\"button\"\n                        className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                      >\n                        <FaceSmileIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        type=\"submit\"\n                        disabled={!messageText.trim()}\n                        className={`ml-2 p-2 rounded-full ${\n                          messageText.trim() \n                            ? 'bg-teal-600 text-white hover:bg-teal-700' \n                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                        } focus:outline-none`}\n                      >\n                        <PaperAirplaneIcon className=\"h-5 w-5\" />\n                      </button>\n                    </form>\n                  </div>\n                </>\n              ) : (\n                // Mesaj seçilmediğinde\n                <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                  <div className=\"w-full max-w-md text-center\">\n                    <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                    <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                    <p className=\"text-gray-500 mx-auto\">\n                      Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir uzmanla iletişime geçin.\n                    </p>\n                    <div className=\"mt-6\">\n                      <Link\n                        to=\"/client/experts\"\n                        className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500\"\n                      >\n                        <UserIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n                        Uzmanları Keşfedin\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n} \n\nexport default ClientMessagesPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,sBAAsB,EACtBC,SAAS,EACTC,eAAe,EACfC,qBAAqB,EACrBC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,aAAa,EACbC,YAAY,QACP,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkD,MAAM,EAAEC,SAAS,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAMG,cAAc,GAAGvD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMwD,gBAAgB,GAAGxD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMyD,oBAAoB,GAAGzD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,MAAM2D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,IAAIF,KAAK,IAAIzB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE,EAAE;MACrBC,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE9B,IAAI,CAAC4B,EAAE,CAAC;MAC7E,MAAMG,gBAAgB,GAAGC,EAAE,CAACC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,EAAE;QACpFC,IAAI,EAAE;UAAEX;QAAM,CAAC;QACfY,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFzB,SAAS,CAACe,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACW,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCb,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,gBAAgB,CAACH,EAAE,CAAC;QAC3EC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,gBAAgB,CAACY,SAAS,CAAC;QACvEd,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,gBAAgB,CAACa,KAAK,CAAC;QAC/D;QACAb,gBAAgB,CAACc,IAAI,CAAC,aAAa,CAAC;QACpC;QACAd,gBAAgB,CAACc,IAAI,CAAC,kBAAkB,CAAC;MAC3C,CAAC,CAAC;MAEFd,gBAAgB,CAACW,EAAE,CAAC,YAAY,EAAGI,MAAM,IAAK;QAC5CjB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEgB,MAAM,CAAC;MAChE,CAAC,CAAC;MAEFf,gBAAgB,CAACW,EAAE,CAAC,eAAe,EAAGK,KAAK,IAAK;QAC9ClB,OAAO,CAACkB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;MAEFhB,gBAAgB,CAACW,EAAE,CAAC,WAAW,EAAGM,aAAa,IAAK;QAClDnB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEkB,aAAa,CAAC;MAC9E,CAAC,CAAC;MAEFjB,gBAAgB,CAACW,EAAE,CAAC,iBAAiB,EAAGK,KAAK,IAAK;QAChDlB,OAAO,CAACkB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE,CAAC,CAAC;;MAEF;MACA,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;QAC1C,IAAInB,gBAAgB,CAACY,SAAS,EAAE;UAC9BZ,gBAAgB,CAACc,IAAI,CAAC,MAAM,EAAGM,QAAQ,IAAK;YAC1C,IAAIA,QAAQ,KAAK,MAAM,EAAE;cACvBtB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAM;QACXsB,aAAa,CAACH,iBAAiB,CAAC;QAChClB,gBAAgB,CAACsB,UAAU,CAAC,CAAC;MAC/B,CAAC;IACH,CAAC,MAAM;MACLxB,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAE;QAC9EwB,QAAQ,EAAE,CAAC,CAAC7B,KAAK;QACjB8B,OAAO,EAAE,CAAC,EAACvD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,EAAE,CAAC,CAAC;;EAEd;EACA9D,SAAS,CAAC,MAAM;IACd,IAAIiD,MAAM,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4B,EAAE,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAIf,MAAM,CAAC4B,SAAS,EAAE;QACpBd,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnEf,MAAM,CAAC8B,IAAI,CAAC,kBAAkB,CAAC;MACjC;;MAEA;MACA,MAAMW,gBAAgB,GAAIC,OAAO,IAAK;QACpC5B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2B,OAAO,CAAC;QACrD5B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE9B,IAAI,CAAC4B,EAAE,CAAC;QACnDC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2B,OAAO,CAACC,QAAQ,CAAC;QAC9D7B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE2B,OAAO,CAACE,cAAc,CAAC;;QAE1E;QACAvD,gBAAgB,CAACwD,IAAI,IAAI;UACvB/B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,OAAO8B,IAAI,CAACC,GAAG,CAACC,IAAI,IAClBA,IAAI,CAAClC,EAAE,KAAK6B,OAAO,CAACE,cAAc,GAC9B;YAAE,GAAGG,IAAI;YAAEC,WAAW,EAAEN,OAAO,CAACO,OAAO;YAAEC,SAAS,EAAER,OAAO,CAACS,SAAS;YAAEC,MAAM,EAAEV,OAAO,CAACC,QAAQ,KAAK1D,IAAI,CAAC4B;UAAG,CAAC,GAC7GkC,IACN,CAAC;QACH,CAAC,CAAC;;QAEF;QACAxD,uBAAuB,CAAC8D,eAAe,IAAI;UACzCvC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEsC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAExC,EAAE,CAAC;UAC7E,IAAIwC,eAAe,IAAIX,OAAO,CAACE,cAAc,KAAKS,eAAe,CAACxC,EAAE,EAAE;YACpEC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/DtB,WAAW,CAACoD,IAAI,IAAI;cAClB;cACA,MAAMS,aAAa,GAAGT,IAAI,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3C,EAAE,KAAK6B,OAAO,CAAC7B,EAAE,CAAC;cAC7D,IAAIyC,aAAa,EAAE;gBACjBxC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE2B,OAAO,CAAC7B,EAAE,CAAC;gBACvE,OAAOgC,IAAI;cACb;cAEA,OAAO,CAAC,GAAGA,IAAI,EAAE;gBACfhC,EAAE,EAAE6B,OAAO,CAAC7B,EAAE;gBACd8B,QAAQ,EAAED,OAAO,CAACC,QAAQ;gBAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;gBAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;gBAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;gBACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;gBAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;gBACpBC,SAAS,EAAE,IAAI,CAAC;cAClB,CAAC,CAAC;YACJ,CAAC,CAAC;;YAEF;YACA,IAAIvB,OAAO,CAACC,QAAQ,KAAK1D,IAAI,CAAC4B,EAAE,EAAE;cAChCC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;cACjFmD,UAAU,CAAC,MAAM;gBACfC,sBAAsB,CAACzB,OAAO,CAACE,cAAc,CAAC;cAChD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YACX;;YAEA;YACAsB,UAAU,CAAC,MAAM;cAAA,IAAAE,qBAAA;cACf,CAAAA,qBAAA,GAAA7D,cAAc,CAAC8D,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACLzD,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;UACnG;UACA,OAAOsC,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMmB,iBAAiB,GAAI9B,OAAO,IAAK;QACrC5B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE2B,OAAO,CAAC;MACnD,CAAC;MAED,MAAM+B,sBAAsB,GAAIC,IAAI,IAAK;QACvC5D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE2D,IAAI,CAAC;QACnDvE,cAAc,CAAC0C,IAAI,IAAI;UACrB,MAAM8B,MAAM,GAAG,IAAIvE,GAAG,CAACyC,IAAI,CAAC;UAC5B,IAAI6B,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACAhE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEiE,KAAK,CAACC,IAAI,CAACN,MAAM,CAAC,CAAC;UACnE,OAAOA,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMO,qBAAqB,GAAIC,OAAO,IAAK;QACzCrE,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEoE,OAAO,CAAC;QAC9DhF,cAAc,CAAC,IAAIC,GAAG,CAAC+E,OAAO,CAAC,CAAC;MAClC,CAAC;MAED,MAAMC,gBAAgB,GAAIV,IAAI,IAAK;QACjCnF,uBAAuB,CAAC8D,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIqB,IAAI,CAAC9B,cAAc,KAAKS,eAAe,CAACxC,EAAE,EAAE;YACjEP,cAAc,CAACuC,IAAI,IAAI,IAAIzC,GAAG,CAAC,CAAC,GAAGyC,IAAI,EAAE6B,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;UACzD;UACA,OAAOzB,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMgC,uBAAuB,GAAIX,IAAI,IAAK;QACxCpE,cAAc,CAACuC,IAAI,IAAI;UACrB,MAAM8B,MAAM,GAAG,IAAIvE,GAAG,CAACyC,IAAI,CAAC;UAC5B8B,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC1B,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMW,kBAAkB,GAAIZ,IAAI,IAAK;QACnC5D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE2D,IAAI,CAAC;QACrD5D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE2D,IAAI,CAACa,MAAM,EAAE,eAAe,EAAEtG,IAAI,CAAC4B,EAAE,CAAC;QAC/EC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE2D,IAAI,CAACc,UAAU,CAAC;;QAEvE;QACA,IAAId,IAAI,CAACa,MAAM,KAAKtG,IAAI,CAAC4B,EAAE,EAAE;UAC3BpB,WAAW,CAACoD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAAI;YAClC;YACA,IAAIA,GAAG,CAACb,QAAQ,KAAK1D,IAAI,CAAC4B,EAAE,IAAI2C,GAAG,CAACZ,cAAc,KAAK8B,IAAI,CAAC9B,cAAc,EAAE;cAC1E9B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEyC,GAAG,CAAC3C,EAAE,CAAC;cAC9D,OAAO;gBAAE,GAAG2C,GAAG;gBAAEO,IAAI,EAAE;cAAK,CAAC;YAC/B;YACA;YACA,IAAIW,IAAI,CAACc,UAAU,CAACC,QAAQ,CAACjC,GAAG,CAAC3C,EAAE,CAAC,EAAE;cACpCC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEyC,GAAG,CAAC3C,EAAE,CAAC;cACpE,OAAO;gBAAE,GAAG2C,GAAG;gBAAEO,IAAI,EAAE;cAAK,CAAC;YAC/B;YACA,OAAOP,GAAG;UACZ,CAAC,CAAC,CAAC;;UAEH;UACAnE,gBAAgB,CAACwD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAAClC,EAAE,KAAK6D,IAAI,CAAC9B,cAAc,GAC3B;YAAE,GAAGG,IAAI;YAAEK,MAAM,EAAE;UAAM,CAAC,GAC1BL,IACN,CAAC,CAAC;QACJ;MACF,CAAC;;MAED;MACAjC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1Df,MAAM,CAAC2B,EAAE,CAAC,aAAa,EAAEc,gBAAgB,CAAC;MAC1CzC,MAAM,CAAC2B,EAAE,CAAC,cAAc,EAAE6C,iBAAiB,CAAC;MAC5CxE,MAAM,CAAC2B,EAAE,CAAC,oBAAoB,EAAE8C,sBAAsB,CAAC;MACvDzE,MAAM,CAAC2B,EAAE,CAAC,aAAa,EAAEyD,gBAAgB,CAAC;MAC1CpF,MAAM,CAAC2B,EAAE,CAAC,qBAAqB,EAAE0D,uBAAuB,CAAC;MACzDrF,MAAM,CAAC2B,EAAE,CAAC,eAAe,EAAE2D,kBAAkB,CAAC;MAC9CtF,MAAM,CAAC2B,EAAE,CAAC,mBAAmB,EAAEuD,qBAAqB,CAAC;MACrDpE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,OAAO,MAAM;QACXf,MAAM,CAAC0F,GAAG,CAAC,aAAa,EAAEjD,gBAAgB,CAAC;QAC3CzC,MAAM,CAAC0F,GAAG,CAAC,cAAc,EAAElB,iBAAiB,CAAC;QAC7CxE,MAAM,CAAC0F,GAAG,CAAC,oBAAoB,EAAEjB,sBAAsB,CAAC;QACxDzE,MAAM,CAAC0F,GAAG,CAAC,aAAa,EAAEN,gBAAgB,CAAC;QAC3CpF,MAAM,CAAC0F,GAAG,CAAC,qBAAqB,EAAEL,uBAAuB,CAAC;QAC1DrF,MAAM,CAAC0F,GAAG,CAAC,eAAe,EAAEJ,kBAAkB,CAAC;QAC/CtF,MAAM,CAAC0F,GAAG,CAAC,mBAAmB,EAAER,qBAAqB,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAAClF,MAAM,EAAEf,IAAI,CAAC4B,EAAE,CAAC,CAAC;;EAErB;EACA9D,SAAS,CAAC,MAAM;IACd4I,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/I,QAAQ,CAAC,IAAIsD,GAAG,CAAC,CAAC,CAAC;EAEzErD,SAAS,CAAC,MAAM;IACd,IAAIiD,MAAM,IAAIZ,aAAa,CAAC0G,MAAM,GAAG,CAAC,EAAE;MACtChF,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE3B,aAAa,CAAC0G,MAAM,EAAE,cAAc,CAAC;MAEnG1G,aAAa,CAAC2G,OAAO,CAACC,YAAY,IAAI;QACpC,IAAI,CAACJ,mBAAmB,CAACK,GAAG,CAACD,YAAY,CAACnF,EAAE,CAAC,EAAE;UAC7Cb,MAAM,CAAC8B,IAAI,CAAC,mBAAmB,EAAEkE,YAAY,CAACnF,EAAE,CAAC;UACjDC,OAAO,CAACC,GAAG,CAAC,2BAA2BiF,YAAY,CAACnF,EAAE,UAAU,CAAC;UACjEgF,sBAAsB,CAAChD,IAAI,IAAI,IAAIzC,GAAG,CAAC,CAAC,GAAGyC,IAAI,EAAEmD,YAAY,CAACnF,EAAE,CAAC,CAAC,CAAC;QACrE;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACb,MAAM,EAAEZ,aAAa,EAAEwG,mBAAmB,CAAC,CAAC;;EAEhD;EACA,MAAMD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFxG,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMiD,QAAQ,GAAG,MAAMlF,GAAG,CAACgJ,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAG/D,QAAQ,CAACsC,IAAI,CAACtF,aAAa,CAAC0D,GAAG,CAACkD,YAAY;QAAA,IAAAI,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9ExF,EAAE,EAAEmF,YAAY,CAACnF,EAAE;UACnByF,QAAQ,EAAEN,YAAY,CAACO,SAAS,CAAC1F,EAAE;UACnC2F,UAAU,EAAER,YAAY,CAACO,SAAS,CAAC5C,IAAI;UACvC8C,WAAW,EAAET,YAAY,CAACO,SAAS,CAACG,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAGV,YAAY,CAACO,SAAS,CAACG,IAAI;UAC7F1D,WAAW,EAAE,EAAAoD,qBAAA,GAAAJ,YAAY,CAAChD,WAAW,cAAAoD,qBAAA,uBAAxBA,qBAAA,CAA0BnD,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAAmD,sBAAA,GAAAL,YAAY,CAAChD,WAAW,cAAAqD,sBAAA,uBAAxBA,sBAAA,CAA0BnD,SAAS,KAAI8C,YAAY,CAAC7C,SAAS;UACxEC,MAAM,EAAE4C,YAAY,CAAChD,WAAW,GAAG,CAACgD,YAAY,CAAChD,WAAW,CAACgB,MAAM,IAAIgC,YAAY,CAAChD,WAAW,CAACL,QAAQ,KAAK1D,IAAI,CAAC4B,EAAE,GAAG,KAAK;UAC5H8F,MAAM,EAAE,oCAAoC9C,kBAAkB,CAACmC,YAAY,CAACO,SAAS,CAAC5C,IAAI,CAAC,qDAAqD;UAChJiB,MAAM,EAAE1E,WAAW,CAAC+F,GAAG,CAACD,YAAY,CAACO,SAAS,CAAC1F,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzE+F,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEHxH,gBAAgB,CAAC8G,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAOnE,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD7E,KAAK,CAAC6E,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR7C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd,IAAIuC,oBAAoB,EAAE;MACxBwH,YAAY,CAACxH,oBAAoB,CAACuB,EAAE,CAAC;MACrCsD,sBAAsB,CAAC7E,oBAAoB,CAACuB,EAAE,CAAC;;MAE/C;MACAxB,gBAAgB,CAACwD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAAClC,EAAE,KAAKvB,oBAAoB,CAACuB,EAAE,GAC/B;QAAE,GAAGkC,IAAI;QAAEK,MAAM,EAAE;MAAM,CAAC,GAC1BL,IACN,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACzD,oBAAoB,CAAC,CAAC;;EAE1B;EACA,MAAM6E,sBAAsB,GAAG,MAAOvB,cAAc,IAAK;IACvD,IAAI;MACF;MACA,MAAMR,QAAQ,GAAG,MAAMlF,GAAG,CAAC6J,GAAG,CAAC,2BAA2BnE,cAAc,OAAO,CAAC;MAEhF,IAAIR,QAAQ,CAACsC,IAAI,CAACsC,YAAY,GAAG,CAAC,EAAE;QAClClG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEqB,QAAQ,CAACsC,IAAI,CAACsC,YAAY,EAAE,kBAAkB,CAAC;;QAEhF;QACAvH,WAAW,CAACoD,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAC9BA,GAAG,CAACb,QAAQ,KAAK1D,IAAI,CAAC4B,EAAE,GAAG;UAAE,GAAG2C,GAAG;UAAEO,IAAI,EAAE;QAAK,CAAC,GAAGP,GACtD,CAAC,CAAC;;QAEF;QACA,IAAIxD,MAAM,EAAE;UACVA,MAAM,CAAC8B,IAAI,CAAC,oBAAoB,EAAE;YAChCc,cAAc;YACd4C,UAAU,EAAE,EAAE,CAAC;UACjB,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOxD,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE;EACF,CAAC;;EAED;EACA,MAAM8E,YAAY,GAAG,MAAOlE,cAAc,IAAK;IAC7C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMlF,GAAG,CAACgJ,GAAG,CAAC,2BAA2BtD,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAMqE,iBAAiB,GAAG7E,QAAQ,CAACsC,IAAI,CAAClF,QAAQ,CAACsD,GAAG,CAACJ,OAAO,KAAK;QAC/D7B,EAAE,EAAE6B,OAAO,CAAC7B,EAAE;QACd8B,QAAQ,EAAED,OAAO,CAACC,QAAQ;QAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;QACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;QAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;QACpBC,SAAS,EAAE,IAAI;QAAE;QACjBiD,WAAW,EAAExE,OAAO,CAACwE;MACvB,CAAC,CAAC,CAAC;MAEHzH,WAAW,CAACwH,iBAAiB,CAAC;;MAE9B;MACA/C,UAAU,CAAC,MAAM;QAAA,IAAAiD,sBAAA;QACf,CAAAA,sBAAA,GAAA5G,cAAc,CAAC8D,OAAO,cAAA8C,sBAAA,uBAAtBA,sBAAA,CAAwB7C,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD7E,KAAK,CAAC6E,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAMoF,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC1H,WAAW,CAAC2H,IAAI,CAAC,CAAC,IAAI,CAAC/H,oBAAoB,EAAE;IAElDwB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCuG,UAAU,EAAEhI,oBAAoB,CAACgH,QAAQ;MACzCrD,OAAO,EAAEvD,WAAW,CAAC2H,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMjF,QAAQ,GAAG,MAAMlF,GAAG,CAACqK,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAEhI,oBAAoB,CAACgH,QAAQ;QACzCrD,OAAO,EAAEvD,WAAW,CAAC2H,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEFvG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEqB,QAAQ,CAACsC,IAAI,CAAC;;MAEjD;MACA/E,cAAc,CAAC,EAAE,CAAC;IAEpB,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7E,KAAK,CAAC6E,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAEDjF,SAAS,CAAC,MAAM;IACd;IACA,IAAIwD,cAAc,CAAC8D,OAAO,IAAI5D,oBAAoB,CAAC4D,OAAO,EAAE;MAC1D5D,oBAAoB,CAAC4D,OAAO,CAACmD,SAAS,GAAG/G,oBAAoB,CAAC4D,OAAO,CAACoD,YAAY;IACpF;EACF,CAAC,EAAE,CAACjI,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMkI,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBR,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMS,wBAAwB,GAAI7B,YAAY,IAAK;IACjDzG,uBAAuB,CAACyG,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM8B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAO9J,MAAM,CAACwJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAE9J;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIuJ,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAG9J,MAAM,CAACwJ,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAE9J;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAACwJ,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAE9J;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM+J,qBAAqB,GAAGpJ,aAAa,CAACU,MAAM,CAACiD,IAAI,IAAI;IACzD;IACA,MAAM0F,aAAa,GAAG1F,IAAI,CAACyD,UAAU,CAACkC,WAAW,CAAC,CAAC,CAACjD,QAAQ,CAAC7F,UAAU,CAAC8I,WAAW,CAAC,CAAC,CAAC,IACjE3F,IAAI,CAACC,WAAW,CAAC0F,WAAW,CAAC,CAAC,CAACjD,QAAQ,CAAC7F,UAAU,CAAC8I,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAMC,aAAa,GAAG7I,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAIiD,IAAI,CAACK,MAAO,IACnCtD,MAAM,KAAK,UAAU,IAAIiD,IAAI,CAAC8D,QAAS,IACvC/G,MAAM,KAAK,SAAS,IAAIiD,IAAI,CAAC6D,OAAQ;IAE3D,OAAO6B,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAI/H,EAAE,IAAK;IACzBxB,gBAAgB,CAACwJ,iBAAiB,IAChCA,iBAAiB,CAAC/F,GAAG,CAACC,IAAI,IACxBA,IAAI,CAAClC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGkC,IAAI;MAAE6D,OAAO,EAAE,CAAC7D,IAAI,CAAC6D;IAAQ,CAAC,GAAG7D,IACzD,CACF,CAAC;IAED,IAAIzD,oBAAoB,IAAIA,oBAAoB,CAACuB,EAAE,KAAKA,EAAE,EAAE;MAC1DtB,uBAAuB,CAACsD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE+D,OAAO,EAAE,CAAC/D,IAAI,CAAC+D;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMkC,aAAa,GAAIjI,EAAE,IAAK;IAC5BxB,gBAAgB,CAACwJ,iBAAiB,IAChCA,iBAAiB,CAAC/F,GAAG,CAACC,IAAI,IACxBA,IAAI,CAAClC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGkC,IAAI;MAAE8D,QAAQ,EAAE,CAAC9D,IAAI,CAAC8D;IAAS,CAAC,GAAG9D,IAC3D,CACF,CAAC;IAED,IAAIzD,oBAAoB,IAAIA,oBAAoB,CAACuB,EAAE,KAAKA,EAAE,EAAE;MAC1DtB,uBAAuB,CAACsD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEgE,QAAQ,EAAE,CAAChE,IAAI,CAACgE;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAI3H,SAAS,EAAE;IACb,oBACEN,OAAA;MAAKmK,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DpK,OAAA;QAAKmK,SAAS,EAAC;MAA2E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC;EAEV;EAEA,oBACExK,OAAA;IAAKmK,SAAS,EAAC,+BAA+B;IAAAC,QAAA,eAC5CpK,OAAA;MAAKmK,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DpK,OAAA;QAAKmK,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvFpK,OAAA;UAAKmK,SAAS,EAAC,uEAAuE;UAAAC,QAAA,gBACpFpK,OAAA;YAAAoK,QAAA,gBACEpK,OAAA;cAAImK,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DxK,OAAA;cAAGmK,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNxK,OAAA;YAAKmK,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CpK,OAAA,CAACF,IAAI;cACH2K,EAAE,EAAC,iBAAiB;cACpBN,SAAS,EAAC,sPAAsP;cAAAC,QAAA,gBAEhQpK,OAAA,CAACN,aAAa;gBAACyK,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPxK,OAAA,CAACF,IAAI;cACH2K,EAAE,EAAC,sBAAsB;cACzBN,SAAS,EAAC,qOAAqO;cAAAC,QAAA,gBAE/OpK,OAAA,CAACL,YAAY;gBAACwK,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxK,OAAA;QAAKmK,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DpK,OAAA;UAAKmK,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBAEzCpK,OAAA;YAAKmK,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC/EpK,OAAA;cAAKmK,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDpK,OAAA;gBAAImK,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACnEpK,OAAA,CAACvB,0BAA0B;kBAAC0L,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEvE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxK,OAAA;gBAAKmK,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BpK,OAAA;kBACE0K,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,0BAAqB;kBACjCR,SAAS,EAAC,iHAAiH;kBAC3HS,KAAK,EAAE5J,UAAW;kBAClB6J,QAAQ,EAAG9B,CAAC,IAAK9H,aAAa,CAAC8H,CAAC,CAAC+B,MAAM,CAACF,KAAK;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACFxK,OAAA,CAACtB,mBAAmB;kBAACyL,SAAS,EAAC;gBAA+C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNxK,OAAA;gBAAKmK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCpK,OAAA;kBACEmK,SAAS,EAAE,kCAAkCjJ,MAAM,KAAK,KAAK,GACzD,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpD6J,OAAO,EAAEA,CAAA,KAAM5J,SAAS,CAAC,KAAK,CAAE;kBAAAiJ,QAAA,EACjC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxK,OAAA;kBACEmK,SAAS,EAAE,kCAAkCjJ,MAAM,KAAK,QAAQ,GAC5D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpD6J,OAAO,EAAEA,CAAA,KAAM5J,SAAS,CAAC,QAAQ,CAAE;kBAAAiJ,QAAA,EACpC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxK,OAAA;kBACEmK,SAAS,EAAE,kCAAkCjJ,MAAM,KAAK,SAAS,GAC7D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpD6J,OAAO,EAAEA,CAAA,KAAM5J,SAAS,CAAC,SAAS,CAAE;kBAAAiJ,QAAA,EACrC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxK,OAAA;kBACEmK,SAAS,EAAE,kCAAkCjJ,MAAM,KAAK,UAAU,GAC9D,2BAA2B,GAC3B,6CAA6C,EAAG;kBACpD6J,OAAO,EAAEA,CAAA,KAAM5J,SAAS,CAAC,UAAU,CAAE;kBAAAiJ,QAAA,EACtC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxK,OAAA;cACEgL,GAAG,EAAEpJ,gBAAiB;cACtBqJ,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAjB,QAAA,EAEDR,qBAAqB,CAAC1C,MAAM,KAAK,CAAC,gBACjClH,OAAA;gBAAKmK,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAENZ,qBAAqB,CAAC1F,GAAG,CAACkD,YAAY,iBACpCpH,OAAA;gBAEEmK,SAAS,EAAE,sEACT,CAAAzJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEuB,EAAE,MAAKmF,YAAY,CAACnF,EAAE,GAAG,YAAY,GAAG,EAAE,IAC9DmF,YAAY,CAAC5C,MAAM,GAAG,8BAA8B,GAAG,EAAE,EAAG;gBAChEuG,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAAC7B,YAAY,CAAE;gBAAAgD,QAAA,gBAEtDpK,OAAA;kBAAKmK,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCpK,OAAA;oBAAKmK,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCpK,OAAA;sBACEsL,GAAG,EAAElE,YAAY,CAACW,MAAO;sBACzBwD,GAAG,EAAEnE,YAAY,CAACQ,UAAW;sBAC7BuC,SAAS,EAAE,0BACT,CAAAzJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEuB,EAAE,MAAKmF,YAAY,CAACnF,EAAE,GACxC,sBAAsB,GACtB,EAAE;oBACL;sBAAAoI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EACDpD,YAAY,CAACpB,MAAM,KAAK,QAAQ,iBAC/BhG,OAAA;sBAAMmK,SAAS,EAAC;oBAAyF;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CACjH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNxK,OAAA;oBAAKmK,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BpK,OAAA;sBAAKmK,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/CpK,OAAA;wBAAImK,SAAS,EAAE,uBAAuB/C,YAAY,CAAC5C,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;wBAAA4F,QAAA,EAC7FhD,YAAY,CAACQ;sBAAU;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eACLxK,OAAA;wBAAKmK,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GACzChD,YAAY,CAACY,OAAO,iBACnBhI,OAAA,CAACP,QAAQ;0BAAC0K,SAAS,EAAC;wBAAsC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAC7D,eACDxK,OAAA;0BAAMmK,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EACpClB,iBAAiB,CAAC9B,YAAY,CAAC9C,SAAS;wBAAC;0BAAA+F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNxK,OAAA;sBAAGmK,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEhD,YAAY,CAACS;oBAAW;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnExK,OAAA;sBAAGmK,SAAS,EAAE,yBACZ/C,YAAY,CAAC5C,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;sBAAA4F,QAAA,EACAhD,YAAY,CAAChD;oBAAW;sBAAAiG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACJxK,OAAA;sBAAKmK,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDpK,OAAA;wBAAMmK,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpC9I,WAAW,CAAC+F,GAAG,CAACD,YAAY,CAACM,QAAQ,CAAC,gBACnC1H,OAAA;0BAAMmK,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,GAC7DpD,YAAY,CAACoE,QAAQ,GACnB,gBAAgBpE,YAAY,CAACoE,QAAQ,EAAE,GACvC;sBAAE;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACPxK,OAAA;wBAAKmK,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7BpK,OAAA;0BACE+K,OAAO,EAAGhC,CAAC,IAAK;4BACdA,CAAC,CAAC0C,eAAe,CAAC,CAAC;4BACnBzB,UAAU,CAAC5C,YAAY,CAACnF,EAAE,CAAC;0BAC7B,CAAE;0BACFkI,SAAS,EAAC,qCAAqC;0BAAAC,QAAA,eAE/CpK,OAAA,CAACP,QAAQ;4BAAC0K,SAAS,EAAE,WAAW/C,YAAY,CAACY,OAAO,GAAG,8BAA8B,GAAG,EAAE;0BAAG;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1F,CAAC,eACTxK,OAAA;0BACE+K,OAAO,EAAGhC,CAAC,IAAK;4BACdA,CAAC,CAAC0C,eAAe,CAAC,CAAC;4BACnBvB,aAAa,CAAC9C,YAAY,CAACnF,EAAE,CAAC;0BAChC,CAAE;0BACFkI,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,eAE7CpK,OAAA,CAACR,cAAc;4BAAC2K,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLpD,YAAY,CAAC5C,MAAM,iBAClBxE,OAAA;kBAAMmK,SAAS,EAAC,yGAAyG;kBAAAC,QAAA,EAAC;gBAE1H;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA,GA5EIpD,YAAY,CAACnF,EAAE;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6EjB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxK,OAAA;YAAKmK,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACrD1J,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;cAAAkK,QAAA,gBAEEpK,OAAA;gBAAKmK,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACtFpK,OAAA;kBAAKmK,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCpK,OAAA;oBAAKmK,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BpK,OAAA;sBACEsL,GAAG,EAAE5K,oBAAoB,CAACqH,MAAO;sBACjCwD,GAAG,EAAE7K,oBAAoB,CAACkH,UAAW;sBACrCuC,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,EACD9J,oBAAoB,CAACsF,MAAM,KAAK,QAAQ,iBACvChG,OAAA;sBAAMmK,SAAS,EAAC;oBAAyF;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CACjH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNxK,OAAA;oBAAAoK,QAAA,gBACEpK,OAAA;sBAAImK,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC9C1J,oBAAoB,CAACkH;oBAAU;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACLxK,OAAA;sBAAGmK,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACjC1J,oBAAoB,CAACmH,WAAW,EAAC,GAAC,EAAC,KAAK,EACxCnH,oBAAoB,CAACsF,MAAM,KAAK,QAAQ,GACrC,WAAW,GACXtF,oBAAoB,CAAC8K,QAAQ,GAC3B,gBAAgB9K,oBAAoB,CAAC8K,QAAQ,EAAE,GAC/C,EAAE;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxK,OAAA;kBAAKmK,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CpK,OAAA,CAACF,IAAI;oBACH2K,EAAE,EAAE,mBAAmB/J,oBAAoB,CAACgH,QAAQ,EAAG;oBACvDyC,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5DpK,OAAA,CAACrB,QAAQ;sBAACwL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACPxK,OAAA,CAACF,IAAI;oBACH2K,EAAE,EAAE,+BAA+B/J,oBAAoB,CAACgH,QAAQ,EAAG;oBACnEyC,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5DpK,OAAA,CAACZ,SAAS;sBAAC+K,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACPxK,OAAA,CAACF,IAAI;oBACH2K,EAAE,EAAE,2BAA2B/J,oBAAoB,CAACgH,QAAQ,EAAG;oBAC/DyC,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAE5DpK,OAAA,CAACd,eAAe;sBAACiL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACPxK,OAAA;oBAAQmK,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAClEpK,OAAA,CAAChB,sBAAsB;sBAACmL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxK,OAAA;gBACEgL,GAAG,EAAEnJ,oBAAqB;gBAC1BsI,SAAS,EAAC,gBAAgB;gBAC1Bc,KAAK,EAAE;kBACLC,MAAM,EAAE,oBAAoB;kBAC5BC,SAAS,EAAE,MAAM;kBACjBC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE;gBAClB,CAAE;gBAAAjB,QAAA,GAEDxJ,QAAQ,CAACsD,GAAG,CAAC,CAACJ,OAAO,EAAE4H,KAAK,KAAK;kBAChC,MAAMC,QAAQ,GAAG7H,OAAO,CAACC,QAAQ,KAAK1D,IAAI,CAAC4B,EAAE;kBAC7C,MAAM2J,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAI9K,QAAQ,CAAC8K,KAAK,GAAG,CAAC,CAAC,CAAC3H,QAAQ,KAAKD,OAAO,CAACC,QAAQ;kBAEnF,oBACE/D,OAAA;oBAAsBmK,SAAS,EAAE,QAAQwB,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;oBAAAvB,QAAA,GACxF,CAACuB,QAAQ,IAAIC,UAAU,iBACtB5L,OAAA;sBACEsL,GAAG,EAAExH,OAAO,CAACkB,YAAa;sBAC1BuG,GAAG,EAAEzH,OAAO,CAACe,UAAW;sBACxBsF,SAAS,EAAC;oBAAgC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CACF,EACA,CAACmB,QAAQ,IAAI,CAACC,UAAU,iBAAI5L,OAAA;sBAAKmK,SAAS,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7DxK,OAAA;sBACEmK,SAAS,EAAE,yDACTwB,QAAQ,GACJ,wCAAwC,GACxC,+DAA+D,EAClE;sBAAAvB,QAAA,gBAEHpK,OAAA;wBAAGmK,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAEtG,OAAO,CAACoB;sBAAI;wBAAAmF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzCxK,OAAA;wBAAKmK,SAAS,EAAE,gBAAgBwB,QAAQ,GAAG,eAAe,GAAG,eAAe,gCAAiC;wBAAAvB,QAAA,GAC1GlB,iBAAiB,CAACpF,OAAO,CAACQ,SAAS,CAAC,EACpCqH,QAAQ,iBACP3L,OAAA,CAACX,eAAe;0BACd8K,SAAS,EAAE,gBAAgBrG,OAAO,CAACqB,IAAI,GAAG,eAAe,GAAG,eAAe,EAAG;0BAC9E0G,KAAK,EAAE/H,OAAO,CAACqB,IAAI,GAAG,QAAQ,GAAG;wBAAW;0BAAAkF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7C,CACF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACLmB,QAAQ,IAAIC,UAAU,iBACrB5L,OAAA;sBACEsL,GAAG,EAAExH,OAAO,CAACkB,YAAa;sBAC1BuG,GAAG,EAAEzH,OAAO,CAACe,UAAW;sBACxBsF,SAAS,EAAC;oBAAgC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CACF,EACAmB,QAAQ,IAAI,CAACC,UAAU,iBAAI5L,OAAA;sBAAKmK,SAAS,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GAlCpD1G,OAAO,CAAC7B,EAAE;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmCf,CAAC;gBAEV,CAAC,CAAC,eACFxK,OAAA;kBAAKgL,GAAG,EAAErJ;gBAAe;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAGNxK,OAAA;gBAAKmK,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,eACpDpK,OAAA;kBAAM8L,QAAQ,EAAEhD,iBAAkB;kBAACqB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3DpK,OAAA;oBACE0K,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eAEjFpK,OAAA,CAACnB,aAAa;sBAACsL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTxK,OAAA;oBAAKmK,SAAS,EAAC,aAAa;oBAAAC,QAAA,eAC1BpK,OAAA;sBACEmK,SAAS,EAAC,iHAAiH;sBAC3HQ,WAAW,EAAC,yCAAqB;sBACjCoB,IAAI,EAAC,GAAG;sBACRnB,KAAK,EAAE9J,WAAY;sBACnB+J,QAAQ,EAAG9B,CAAC,IAAKhI,cAAc,CAACgI,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAE;sBAChDoB,SAAS,EAAGjD,CAAC,IAAK;wBAChB,IAAIA,CAAC,CAACkD,GAAG,KAAK,OAAO,IAAI,CAAClD,CAAC,CAACmD,QAAQ,EAAE;0BACpCnD,CAAC,CAACC,cAAc,CAAC,CAAC;0BAClBR,WAAW,CAAC,CAAC;wBACf;sBACF;oBAAE;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACNxK,OAAA;oBACE0K,IAAI,EAAC,QAAQ;oBACbP,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,eAEjFpK,OAAA,CAAClB,aAAa;sBAACqL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTxK,OAAA;oBACE0K,IAAI,EAAC,QAAQ;oBACbyB,QAAQ,EAAE,CAACrL,WAAW,CAAC2H,IAAI,CAAC,CAAE;oBAC9B0B,SAAS,EAAE,yBACTrJ,WAAW,CAAC2H,IAAI,CAAC,CAAC,GACd,0CAA0C,GAC1C,8CAA8C,qBAC9B;oBAAA2B,QAAA,eAEtBpK,OAAA,CAACpB,iBAAiB;sBAACuL,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,eACN,CAAC;YAAA;YAEH;YACAxK,OAAA;cAAKmK,SAAS,EAAC,iEAAiE;cAAAC,QAAA,eAC9EpK,OAAA;gBAAKmK,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CpK,OAAA,CAACvB,0BAA0B;kBAAC0L,SAAS,EAAC;gBAAsC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/ExK,OAAA;kBAAImK,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxExK,OAAA;kBAAGmK,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxK,OAAA;kBAAKmK,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBpK,OAAA,CAACF,IAAI;oBACH2K,EAAE,EAAC,iBAAiB;oBACpBN,SAAS,EAAC,wNAAwN;oBAAAC,QAAA,gBAElOpK,OAAA,CAACrB,QAAQ;sBAACwL,SAAS,EAAC,oBAAoB;sBAAC,eAAY;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gCAEhE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAApK,EAAA,CAh1BKD,kBAAkB;EAAA,QACL9B,OAAO;AAAA;AAAA+N,EAAA,GADpBjM,kBAAkB;AAk1BxB,eAAeA,kBAAkB;AAAC,IAAAiM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}