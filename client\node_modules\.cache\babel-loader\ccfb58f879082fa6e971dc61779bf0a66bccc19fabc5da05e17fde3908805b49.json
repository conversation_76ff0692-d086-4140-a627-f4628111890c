{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"да н.э.\", \"н.э.\"],\n  abbreviated: [\"да н. э.\", \"н. э.\"],\n  wide: [\"да нашай эры\", \"нашай эры\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ы кв.\", \"2-і кв.\", \"3-і кв.\", \"4-ы кв.\"],\n  wide: [\"1-ы квартал\", \"2-і квартал\", \"3-і квартал\", \"4-ы квартал\"]\n};\nconst monthValues = {\n  narrow: [\"С\", \"Л\", \"С\", \"К\", \"М\", \"Ч\", \"Л\", \"Ж\", \"В\", \"К\", \"Л\", \"С\"],\n  abbreviated: [\"студз.\", \"лют.\", \"сак.\", \"крас.\", \"май\", \"чэрв.\", \"ліп.\", \"жн.\", \"вер.\", \"кастр.\", \"ліст.\", \"снеж.\"],\n  wide: [\"студзень\", \"люты\", \"сакавік\", \"красавік\", \"май\", \"чэрвень\", \"ліпень\", \"жнівень\", \"верасень\", \"кастрычнік\", \"лістапад\", \"снежань\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"С\", \"Л\", \"С\", \"К\", \"М\", \"Ч\", \"Л\", \"Ж\", \"В\", \"К\", \"Л\", \"С\"],\n  abbreviated: [\"студз.\", \"лют.\", \"сак.\", \"крас.\", \"мая\", \"чэрв.\", \"ліп.\", \"жн.\", \"вер.\", \"кастр.\", \"ліст.\", \"снеж.\"],\n  wide: [\"студзеня\", \"лютага\", \"сакавіка\", \"красавіка\", \"мая\", \"чэрвеня\", \"ліпеня\", \"жніўня\", \"верасня\", \"кастрычніка\", \"лістапада\", \"снежня\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"А\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"аў\", \"ср\", \"чц\", \"пт\", \"сб\"],\n  abbreviated: [\"нядз\", \"пан\", \"аўт\", \"сер\", \"чац\", \"пят\", \"суб\"],\n  wide: [\"нядзеля\", \"панядзелак\", \"аўторак\", \"серада\", \"чацвер\", \"пятніца\", \"субота\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дзень\",\n    evening: \"веч.\",\n    night: \"ноч\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дзень\",\n    evening: \"веч.\",\n    night: \"ноч\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўнач\",\n    noon: \"поўдзень\",\n    morning: \"раніца\",\n    afternoon: \"дзень\",\n    evening: \"вечар\",\n    night: \"ноч\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночы\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўн.\",\n    noon: \"поўд.\",\n    morning: \"ран.\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночы\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"поўнач\",\n    noon: \"поўдзень\",\n    morning: \"раніцы\",\n    afternoon: \"дня\",\n    evening: \"вечара\",\n    night: \"ночы\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const unit = String(options?.unit);\n  const number = Number(dirtyNumber);\n  let suffix;\n\n  /** Though it's an incorrect ordinal form of a date we use it here for consistency with other similar locales (ru, uk)\n   *  For date-month combinations should be used `d` formatter.\n   *  Correct:   `d MMMM` (4 верасня)\n   *  Incorrect: `do MMMM` (4-га верасня)\n   *\n   *  But following the consistency leads to mistakes for literal uses of `do` formatter (ordinal day of month).\n   *  So for phrase \"5th day of month\" (`do дзень месяца`)\n   *  library will produce:            `5-га дзень месяца`\n   *  but correct spelling should be:  `5-ы дзень месяца`\n   *\n   *  So I guess there should be a stand-alone and a formatting version of \"day of month\" formatters\n   */\n  if (unit === \"date\") {\n    suffix = \"-га\";\n  } else if (unit === \"hour\" || unit === \"minute\" || unit === \"second\") {\n    suffix = \"-я\";\n  } else {\n    suffix = (number % 10 === 2 || number % 10 === 3) && number % 100 !== 12 && number % 100 !== 13 ? \"-і\" : \"-ы\";\n  }\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}