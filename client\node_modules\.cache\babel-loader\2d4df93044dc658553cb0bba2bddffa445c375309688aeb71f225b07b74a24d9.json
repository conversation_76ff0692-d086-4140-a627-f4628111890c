{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"eaa.\", \"jaa.\"],\n  abbreviated: [\"eaa.\", \"jaa.\"],\n  wide: [\"ennen ajanlaskun alkua\", \"jä<PERSON>een ajanlaskun alun\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. kvartaali\", \"2. kvartaali\", \"3. kvartaali\", \"4. kvartaali\"]\n};\nconst monthValues = {\n  narrow: [\"T\", \"H\", \"M\", \"H\", \"T\", \"K\", \"H\", \"E\", \"S\", \"L\", \"M\", \"J\"],\n  abbreviated: [\"tammi\", \"helmi\", \"maalis\", \"huhti\", \"touko\", \"kesä\", \"heinä\", \"elo\", \"syys\", \"loka\", \"marras\", \"joulu\"],\n  wide: [\"tammikuu\", \"helmikuu\", \"maaliskuu\", \"huhtikuu\", \"toukokuu\", \"kesäkuu\", \"heinäkuu\", \"elokuu\", \"syyskuu\", \"lokakuu\", \"marraskuu\", \"joulukuu\"]\n};\nconst formattingMonthValues = {\n  narrow: monthValues.narrow,\n  abbreviated: monthValues.abbreviated,\n  wide: [\"tammikuuta\", \"helmikuuta\", \"maaliskuuta\", \"huhtikuuta\", \"toukokuuta\", \"kesäkuuta\", \"heinäkuuta\", \"elokuuta\", \"syyskuuta\", \"lokakuuta\", \"marraskuuta\", \"joulukuuta\"]\n};\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"T\", \"K\", \"T\", \"P\", \"L\"],\n  short: [\"su\", \"ma\", \"ti\", \"ke\", \"to\", \"pe\", \"la\"],\n  abbreviated: [\"sunn.\", \"maan.\", \"tiis.\", \"kesk.\", \"torst.\", \"perj.\", \"la\"],\n  wide: [\"sunnuntai\", \"maanantai\", \"tiistai\", \"keskiviikko\", \"torstai\", \"perjantai\", \"lauantai\"]\n};\nconst formattingDayValues = {\n  narrow: dayValues.narrow,\n  short: dayValues.short,\n  abbreviated: dayValues.abbreviated,\n  wide: [\"sunnuntaina\", \"maanantaina\", \"tiistaina\", \"keskiviikkona\", \"torstaina\", \"perjantaina\", \"lauantaina\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ap\",\n    pm: \"ip\",\n    midnight: \"keskiyö\",\n    noon: \"keskipäivä\",\n    morning: \"ap\",\n    afternoon: \"ip\",\n    evening: \"illalla\",\n    night: \"yöllä\"\n  },\n  abbreviated: {\n    am: \"ap\",\n    pm: \"ip\",\n    midnight: \"keskiyö\",\n    noon: \"keskipäivä\",\n    morning: \"ap\",\n    afternoon: \"ip\",\n    evening: \"illalla\",\n    night: \"yöllä\"\n  },\n  wide: {\n    am: \"ap\",\n    pm: \"ip\",\n    midnight: \"keskiyöllä\",\n    noon: \"keskipäivällä\",\n    morning: \"aamupäivällä\",\n    afternoon: \"iltapäivällä\",\n    evening: \"illalla\",\n    night: \"yöllä\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}