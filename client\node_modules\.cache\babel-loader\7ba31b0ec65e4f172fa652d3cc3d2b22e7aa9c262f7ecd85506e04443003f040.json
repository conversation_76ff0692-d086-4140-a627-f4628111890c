{"ast": null, "code": "import { getWeekYear } from \"../../../getWeekYear.js\";\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// Local week-numbering year\nexport class LocalWeekYearParser extends Parser {\n  priority = 130;\n  parse(dateString, token, match) {\n    const valueCallback = year => ({\n      year,\n      isTwoDigitYear: token === \"YY\"\n    });\n    switch (token) {\n      case \"Y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"Yo\":\n        return mapValue(match.ordinalNumber(dateString, {\n          unit: \"year\"\n        }), valueCallback);\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n  set(date, flags, value, options) {\n    const currentYear = getWeekYear(date, options);\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);\n      date.setFullYear(normalizedTwoDigitYear, 0, options.firstWeekContainsDate);\n      date.setHours(0, 0, 0, 0);\n      return startOfWeek(date, options);\n    }\n    const year = !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, options.firstWeekContainsDate);\n    date.setHours(0, 0, 0, 0);\n    return startOfWeek(date, options);\n  }\n  incompatibleTokens = [\"y\", \"R\", \"u\", \"Q\", \"q\", \"M\", \"L\", \"I\", \"d\", \"D\", \"i\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}