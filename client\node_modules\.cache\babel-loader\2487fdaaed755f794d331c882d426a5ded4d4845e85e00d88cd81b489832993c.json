{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"o.Kr.\", \"m.Kr.\"],\n  abbreviated: [\"o.Kr.\", \"m.Kr.\"],\n  wide: [\"ov<PERSON>\", \"ma<PERSON><PERSON><PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. kvart<PERSON>la\", \"2. kvart<PERSON>la\", \"3. kvart<PERSON>la\", \"4. kvartála\"]\n};\nconst monthValues = {\n  narrow: [\"O\", \"G\", \"N\", \"C\", \"M\", \"G\", \"S\", \"B\", \"Č\", \"G\", \"S\", \"J\"],\n  abbreviated: [\"ođđa\", \"guov\", \"njuk\", \"cuo\", \"mies\", \"geas\", \"suoi\", \"borg\", \"čak<PERSON>\", \"golg\", \"skáb\", \"juov\"],\n  wide: [\"ođđaja<PERSON><PERSON>nu\", \"guov<PERSON><PERSON>nu\", \"njuk<PERSON><PERSON><PERSON>nu\", \"cuoŋom<PERSON>nu\", \"miess<PERSON><PERSON>nu\", \"geassemánnu\", \"suoidnemánnu\", \"borgemánnu\", \"čakčamánnu\", \"golggotmánnu\", \"skábmamánnu\", \"juovlamánnu\"]\n};\nconst dayValues = {\n  narrow: [\"S\", \"V\", \"M\", \"G\", \"D\", \"B\", \"L\"],\n  short: [\"sotn\", \"vuos\", \"maŋ\", \"gask\", \"duor\", \"bear\", \"láv\"],\n  abbreviated: [\"sotn\", \"vuos\", \"maŋ\", \"gask\", \"duor\", \"bear\", \"láv\"],\n  wide: [\"sotnabeaivi\", \"vuossárga\", \"maŋŋebárga\", \"gaskavahkku\", \"duorastat\", \"bearjadat\", \"lávvardat\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"gaskaidja\",\n    noon: \"gaskabeaivi\",\n    morning: \"iđđes\",\n    afternoon: \"maŋŋel gaska.\",\n    evening: \"eahkes\",\n    night: \"ihkku\"\n  },\n  abbreviated: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gaskaidja\",\n    noon: \"gaskabeaivvi\",\n    morning: \"iđđes\",\n    afternoon: \"maŋŋel gaskabea.\",\n    evening: \"eahkes\",\n    night: \"ihkku\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gaskaidja\",\n    noon: \"gaskabeavvi\",\n    morning: \"iđđes\",\n    afternoon: \"maŋŋel gaskabeaivvi\",\n    evening: \"eahkes\",\n    night: \"ihkku\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}