{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'sonun<PERSON>' eeee p -'də'\",\n  yesterday: \"'dünən' p -'də'\",\n  today: \"'bugün' p -'də'\",\n  tomorrow: \"'sabah' p -'də'\",\n  nextWeek: \"eeee p -'də'\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}