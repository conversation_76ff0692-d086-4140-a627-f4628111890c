{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"R\", \"A\"],\n  abbreviated: [\"RC\", \"AD\"],\n  wide: [\"ro Chr<PERSON>ost<PERSON>\", \"anno domini\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"C1\", \"C2\", \"C3\", \"C4\"],\n  wide: [\"a' chiad chairteal\", \"an dàrna cairteal\", \"an treas cairteal\", \"an ceathramh cairteal\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"F\", \"G\", \"M\", \"G\", \"C\", \"Ò\", \"I\", \"L\", \"S\", \"D\", \"S\", \"D\"],\n  abbreviated: [\"<PERSON>ao<PERSON>\", \"<PERSON>\", \"<PERSON>àrt\", \"<PERSON>ib<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Ògmh\", \"Iuch\", \"<PERSON>ùn\", \"<PERSON>t\", \"Dàmh\", \"Samh\", \"Dùbh\"],\n  wide: [\"Am Faoilleach\", \"An Gearran\", \"Am Màrt\", \"An Giblean\", \"An Cèitean\", \"An t-Ògmhios\", \"An t-Iuchar\", \"An Lùnastal\", \"An t-Sultain\", \"An Dàmhair\", \"An t-Samhain\", \"An Dùbhlachd\"]\n};\nconst dayValues = {\n  narrow: [\"D\", \"L\", \"M\", \"C\", \"A\", \"H\", \"S\"],\n  short: [\"Dò\", \"Lu\", \"Mà\", \"Ci\", \"Ar\", \"Ha\", \"Sa\"],\n  abbreviated: [\"Did\", \"Dil\", \"Dim\", \"Dic\", \"Dia\", \"Dih\", \"Dis\"],\n  wide: [\"Didòmhnaich\", \"Diluain\", \"Dimàirt\", \"Diciadain\", \"Diardaoin\", \"Dihaoine\", \"Disathairne\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"m\",\n    pm: \"f\",\n    midnight: \"m.o.\",\n    noon: \"m.l.\",\n    morning: \"madainn\",\n    afternoon: \"feasgar\",\n    evening: \"feasgar\",\n    night: \"oidhche\"\n  },\n  abbreviated: {\n    am: \"M.\",\n    pm: \"F.\",\n    midnight: \"meadhan oidhche\",\n    noon: \"meadhan là\",\n    morning: \"madainn\",\n    afternoon: \"feasgar\",\n    evening: \"feasgar\",\n    night: \"oidhche\"\n  },\n  wide: {\n    am: \"m.\",\n    pm: \"f.\",\n    midnight: \"meadhan oidhche\",\n    noon: \"meadhan là\",\n    morning: \"madainn\",\n    afternoon: \"feasgar\",\n    evening: \"feasgar\",\n    night: \"oidhche\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"m\",\n    pm: \"f\",\n    midnight: \"m.o.\",\n    noon: \"m.l.\",\n    morning: \"sa mhadainn\",\n    afternoon: \"feasgar\",\n    evening: \"feasgar\",\n    night: \"air an oidhche\"\n  },\n  abbreviated: {\n    am: \"M.\",\n    pm: \"F.\",\n    midnight: \"meadhan oidhche\",\n    noon: \"meadhan là\",\n    morning: \"sa mhadainn\",\n    afternoon: \"feasgar\",\n    evening: \"feasgar\",\n    night: \"air an oidhche\"\n  },\n  wide: {\n    am: \"m.\",\n    pm: \"f.\",\n    midnight: \"meadhan oidhche\",\n    noon: \"meadhan là\",\n    morning: \"sa mhadainn\",\n    afternoon: \"feasgar\",\n    evening: \"feasgar\",\n    night: \"air an oidhche\"\n  }\n};\nconst ordinalNumber = dirtyNumber => {\n  const number = Number(dirtyNumber);\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + \"d\";\n      case 2:\n        return number + \"na\";\n    }\n  }\n  if (rem100 === 12) {\n    return number + \"na\";\n  }\n  return number + \"mh\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}