import React, { useState, useEffect } from 'react';
import {
  BellIcon,
  XMarkIcon,
  CheckIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  CalendarIcon,
  UserIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';
import { format, formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';
import api from '../../services/api';
import toast from 'react-hot-toast';
import io from 'socket.io-client';
import { useAuth } from '../../hooks/useAuth';
import { useNavigate } from 'react-router-dom';

/**
 * Bildirim Merkezi Komponenti
 */
const NotificationCenter = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [buttonRef, setButtonRef] = useState(null);
  const [socket, setSocket] = useState(null);
  const { user } = useAuth();
  const navigate = useNavigate();

  // Load notifications from API
  useEffect(() => {
    loadNotifications();
    loadUnreadCount();
  }, []);

  // Socket connection
  useEffect(() => {
    const token = localStorage.getItem('accessToken');
    if (token && user?.id) {
      console.log('🔌 NOTIFICATIONS: Socket.IO bağlantısı kuruluyor... User ID:', user.id);
      const socketConnection = io(process.env.REACT_APP_API_URL || 'https://localhost:5000', {
        auth: { token },
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5,
        timeout: 20000,
        rejectUnauthorized: false, // SSL sertifika kontrolünü bypass et
        secure: true
      });

      setSocket(socketConnection);

      // Connection events
      socketConnection.on('connect', () => {
        console.log('✅ NOTIFICATIONS: Socket.IO bağlantısı kuruldu:', socketConnection.id);
      });

      socketConnection.on('disconnect', (reason) => {
        console.log('❌ NOTIFICATIONS: Socket.IO bağlantısı kesildi:', reason);
      });

      return () => {
        socketConnection.disconnect();
      };
    }
  }, [user?.id]);

  // Socket listener for new notifications
  useEffect(() => {
    if (!socket) return;

    const handleNewNotification = (notification) => {
      console.log('🔔 New notification received:', notification);

      // Add new notification to the list
      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);

      // Sadece basit bildirim - toast'a gerek yok aslında
      // toast.success('Yeni bildiriminiz var');
    };

    socket.on('new_notification', handleNewNotification);

    return () => {
      socket.off('new_notification', handleNewNotification);
    };
  }, [socket]);

  const loadNotifications = async () => {
    try {
      const response = await api.get('/notifications');
      setNotifications(response.data.notifications || []);
    } catch (error) {
      console.error('Bildirimler yüklenirken hata:', error);
      toast.error('Bildirimler yüklenemedi');
    }
  };

  const loadUnreadCount = async () => {
    try {
      const response = await api.get('/notifications/unread-count');
      setUnreadCount(response.data.count || 0);
    } catch (error) {
      console.error('Okunmamış bildirim sayısı yüklenirken hata:', error);
    }
  };

  // Bildirim tipine göre ikon
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'message':
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-blue-500" />;
      case 'appointment':
        return <CalendarIcon className="h-5 w-5 text-green-500" />;
      case 'session':
        return <ClockIcon className="h-5 w-5 text-orange-500" />;
      case 'system':
        return <ExclamationTriangleIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <BellIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  // Bildirimi okundu olarak işaretle
  const markAsRead = async (notificationId) => {
    try {
      await api.put(`/notifications/${notificationId}/read`);

      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true }
            : notification
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Bildirim okundu işaretlenirken hata:', error);
      toast.error('Bildirim güncellenemedi');
    }
  };

  // Tüm bildirimleri okundu olarak işaretle
  const markAllAsRead = async () => {
    try {
      await api.put('/notifications/mark-all-read');

      setNotifications(prev =>
        prev.map(notification => ({ ...notification, isRead: true }))
      );
      setUnreadCount(0);
      setIsOpen(false); // Dropdown'ı kapat
      toast.success('Tüm bildirimler okundu işaretlendi');
    } catch (error) {
      console.error('Bildirimler okundu işaretlenirken hata:', error);
      toast.error('Bildirimler güncellenemedi');
    }
  };

  // Bildirimi sil
  const deleteNotification = async (notificationId) => {
    try {
      await api.delete(`/notifications/${notificationId}`);

      const notification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      if (notification && !notification.isRead) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      toast.success('Bildirim silindi');
    } catch (error) {
      console.error('Bildirim silinirken hata:', error);
      toast.error('Bildirim silinemedi');
    }
  };

  // Dropdown pozisyonunu hesapla
  const getDropdownPosition = () => {
    if (!buttonRef) return { top: 0, left: 0 };

    const rect = buttonRef.getBoundingClientRect();
    return {
      top: rect.bottom + 8,
      left: Math.max(16, rect.left - 160) // 160px dropdown'ın yarısı, minimum 16px margin
    };
  };

  // Bildirime tıklama - ilgili sayfaya yönlendir
  const handleNotificationClick = async (notification) => {
    // Önce okundu işaretle
    if (!notification.isRead) {
      await markAsRead(notification.id);
    }

    // Dropdown'ı kapat
    setIsOpen(false);

    // Bildirim tipine göre yönlendir
    if (notification.type === 'message') {
      // Kullanıcı rolüne göre mesaj sayfasına yönlendir ve conversation ID'yi state olarak geç
      if (user?.role?.name === 'Client') {
        navigate('/client/messages', {
          state: { selectedConversationId: notification.relatedEntityId }
        });
      } else if (user?.role?.name === 'Expert') {
        navigate('/expert/messages', {
          state: { selectedConversationId: notification.relatedEntityId }
        });
      }
    } else if (notification.type === 'appointment') {
      // Randevu sayfasına yönlendir
      if (user?.role?.name === 'Client') {
        navigate('/client/appointments');
      } else if (user?.role?.name === 'Expert') {
        navigate('/expert/appointments');
      }
    } else if (notification.type === 'session') {
      // Seans sayfasına yönlendir
      if (user?.role?.name === 'Client') {
        navigate('/client/sessions');
      } else if (user?.role?.name === 'Expert') {
        navigate('/expert/sessions');
      }
    }
  };

  return (
    <div className="relative">
      {/* Bildirim Butonu */}
      <button
        ref={setButtonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md"
      >
        {unreadCount > 0 ? (
          <BellSolidIcon className="h-6 w-6 text-primary-600" />
        ) : (
          <BellIcon className="h-6 w-6" />
        )}
        
        {/* Okunmamış bildirim sayısı */}
        {unreadCount > 0 && (
          <span className="absolute -top-0.5 -right-0.5 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[18px] h-[18px]">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Bildirim Paneli */}
      {isOpen && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 z-[9998]"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Bildirim Dropdown */}
          <div
            className="fixed w-80 bg-white rounded-lg shadow-xl ring-1 ring-black ring-opacity-5 z-[9999] max-h-96 overflow-hidden"
            style={{
              top: `${getDropdownPosition().top}px`,
              left: `${getDropdownPosition().left}px`,
              zIndex: 9999
            }}
          >
            {/* Header */}
            <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Bildirimler</h3>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-xs text-primary-600 hover:text-primary-700 font-medium"
                >
                  Tümünü Okundu İşaretle
                </button>
              )}
            </div>

            {/* Bildirim Listesi */}
            <div className="max-h-80 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="px-4 py-8 text-center">
                  <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-500">Henüz bildiriminiz yok</p>
                </div>
              ) : (
                notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${
                      !notification.isRead ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start space-x-3">
                      {/* Avatar veya İkon */}
                      <div className="flex-shrink-0">
                        {notification.avatar ? (
                          <img
                            className="h-8 w-8 rounded-full"
                            src={notification.avatar}
                            alt=""
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                            {getNotificationIcon(notification.type)}
                          </div>
                        )}
                      </div>

                      {/* İçerik */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {notification.title}
                            </p>
                            <p className="text-sm text-gray-600 mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {(() => {
                                try {
                                  const date = new Date(notification.timestamp);
                                  if (isNaN(date.getTime())) {
                                    return 'Geçersiz tarih';
                                  }
                                  return formatDistanceToNow(date, {
                                    addSuffix: true,
                                    locale: tr
                                  });
                                } catch (error) {
                                  console.error('Date formatting error:', error, notification.timestamp);
                                  return 'Tarih hatası';
                                }
                              })()}
                            </p>
                          </div>

                          {/* Okunmamış işareti ve sil butonu */}
                          <div className="flex items-center space-x-1 ml-2">
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteNotification(notification.id);
                              }}
                              className="p-1 text-gray-400 hover:text-gray-600 rounded"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Footer */}
            {notifications.length > 0 && (
              <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
                <button
                  onClick={() => setIsOpen(false)}
                  className="w-full text-center text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  Tüm Bildirimleri Görüntüle
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default NotificationCenter;
