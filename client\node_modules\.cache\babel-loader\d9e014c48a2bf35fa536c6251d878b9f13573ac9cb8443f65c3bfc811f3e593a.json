{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ម.គស\", \"គស\"],\n  abbreviated: [\"មុនគ.ស\", \"គ.ស\"],\n  wide: [\"មុនគ្រិស្តសករាជ\", \"នៃគ្រិស្តសករាជ\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"ត្រីមាសទី 1\", \"ត្រីមាសទី 2\", \"ត្រីមាសទី 3\", \"ត្រីមាសទី 4\"]\n};\nconst monthValues = {\n  narrow: [\"ម.ក\", \"ក.ម\", \"មិ\", \"ម.ស\", \"ឧ.ស\", \"ម.ថ\", \"ក.ដ\", \"សី\", \"កញ\", \"តុ\", \"វិ\", \"ធ\"],\n  abbreviated: [\"មករា\", \"កុម្ភៈ\", \"មីនា\", \"មេសា\", \"ឧសភា\", \"មិថុនា\", \"កក្កដា\", \"សីហា\", \"កញ្ញា\", \"តុលា\", \"វិច្ឆិកា\", \"ធ្នូ\"],\n  wide: [\"មករា\", \"កុម្ភៈ\", \"មីនា\", \"មេសា\", \"ឧសភា\", \"មិថុនា\", \"កក្កដា\", \"សីហា\", \"កញ្ញា\", \"តុលា\", \"វិច្ឆិកា\", \"ធ្នូ\"]\n};\nconst dayValues = {\n  narrow: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  short: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  abbreviated: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  wide: [\"អាទិត្យ\", \"ចន្ទ\", \"អង្គារ\", \"ពុធ\", \"ព្រហស្បតិ៍\", \"សុក្រ\", \"សៅរ៍\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  },\n  abbreviated: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  },\n  wide: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  },\n  abbreviated: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  },\n  wide: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _) => {\n  const number = Number(dirtyNumber);\n  return number.toString();\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}