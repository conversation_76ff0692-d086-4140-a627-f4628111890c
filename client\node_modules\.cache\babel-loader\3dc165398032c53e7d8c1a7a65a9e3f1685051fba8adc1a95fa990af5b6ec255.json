{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"f.Kr.\", \"e.Kr.\"],\n  abbreviated: [\"f.Kr.\", \"e.Kr.\"],\n  wide: [\"fø<PERSON>\", \"et<PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. kvartal\", \"2. kvartal\", \"3. kvartal\", \"4. kvartal\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"jan.\", \"feb.\", \"mars\", \"apr.\", \"mai\", \"juni\", \"juli\", \"aug.\", \"sep.\", \"okt.\", \"nov.\", \"des.\"],\n  wide: [\"januar\", \"februar\", \"mars\", \"april\", \"mai\", \"juni\", \"juli\", \"august\", \"september\", \"oktober\", \"november\", \"desember\"]\n};\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"T\", \"O\", \"T\", \"F\", \"L\"],\n  short: [\"sø\", \"ma\", \"ti\", \"on\", \"to\", \"fr\", \"lø\"],\n  abbreviated: [\"søn\", \"man\", \"tir\", \"ons\", \"tor\", \"fre\", \"lør\"],\n  wide: [\"søndag\", \"mandag\", \"tirsdag\", \"onsdag\", \"torsdag\", \"fredag\", \"lørdag\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"på morg.\",\n    afternoon: \"på etterm.\",\n    evening: \"på kvelden\",\n    night: \"på natten\"\n  },\n  abbreviated: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"på morg.\",\n    afternoon: \"på etterm.\",\n    evening: \"på kvelden\",\n    night: \"på natten\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"midnatt\",\n    noon: \"middag\",\n    morning: \"på morgenen\",\n    afternoon: \"på ettermiddagen\",\n    evening: \"på kvelden\",\n    night: \"på natten\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}