/**
 * Socket URL Utility
 * Dinamik olarak doğru socket URL'sini belirler
 */

/**
 * Doğru socket URL'sini belirler
 * @returns {string} Socket URL
 */
export const getSocketUrl = () => {
  // Environment'tan URL'yi al
  const envUrl = process.env.REACT_APP_API_URL || process.env.REACT_APP_SOCKET_URL;
  
  if (envUrl) {
    return envUrl;
  }
  
  // Fallback: Mevcut hostname'e göre belirle
  const hostname = window.location.hostname;
  const protocol = window.location.protocol === 'https:' ? 'https:' : 'https:';
  const port = '5000';
  
  // Localhost veya 127.0.0.1 ise localhost kullan
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `${protocol}//localhost:${port}`;
  }
  
  // IP adresi ise o IP'yi kullan
  if (hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
    return `${protocol}//${hostname}:${port}`;
  }
  
  // Fallback IP
  const fallbackIp = process.env.REACT_APP_FALLBACK_IP || '*************';
  return `${protocol}//${fallbackIp}:${port}`;
};

/**
 * Socket.IO için optimize edilmiş konfigürasyon
 * @param {string} token - JWT token
 * @returns {object} Socket.IO konfigürasyonu
 */
export const getSocketConfig = (token) => {
  return {
    auth: { token },
    transports: ['websocket', 'polling'],
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionAttempts: 5,
    timeout: 20000,
    rejectUnauthorized: false, // SSL sertifika kontrolünü bypass et
    secure: true,
    forceNew: false,
    upgrade: true,
    rememberUpgrade: false
  };
};
