import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import api from '../../../services/api';
import { toast } from 'react-hot-toast';
import io from 'socket.io-client';
import {
  ChatBubbleLeftEllipsisIcon,
  MagnifyingGlassIcon,
  UserIcon,
  PaperAirplaneIcon,
  PaperClipIcon,
  FaceSmileIcon,
  UserCircleIcon,
  EllipsisHorizontalIcon,
  PhoneIcon,
  VideoCameraIcon,
  InformationCircleIcon,
  ClockIcon,
  CheckCircleIcon,
  XMarkIcon,
  TrashIcon,
  ArchiveBoxIcon,
  StarIcon,
  UserGroupIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Link } from 'react-router-dom';

/**
 * Danışan mesajlaşma sayfası
 */
const ClientMessagesPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all'); // all, unread, archived, starred
  const [socket, setSocket] = useState(null);
  const [onlineUsers, setOnlineUsers] = useState(new Set());
  const [typingUsers, setTypingUsers] = useState(new Set());
  const messagesEndRef = useRef(null);
  const conversationsRef = useRef(null);
  const messagesContainerRef = useRef(null);

  // Socket.IO connection - ayrı useEffect
  useEffect(() => {
    const token = localStorage.getItem('accessToken');
    if (token && user?.id) {
      console.log('🔌 CLIENT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);
      const socketConnection = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {
        auth: { token },
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5,
        timeout: 20000
      });

      setSocket(socketConnection);

      // Connection events
      socketConnection.on('connect', () => {
        console.log('✅ CLIENT: Socket.IO bağlantısı kuruldu:', socketConnection.id);
        console.log('🔗 CLIENT: Socket connected:', socketConnection.connected);
        console.log('🏠 CLIENT: Socket rooms:', socketConnection.rooms);
        // Kullanıcının online olduğunu bildir
        socketConnection.emit('user_online');
        // Online kullanıcı listesini al
        socketConnection.emit('get_online_users');
      });

      socketConnection.on('disconnect', (reason) => {
        console.log('❌ CLIENT: Socket.IO bağlantısı kesildi:', reason);
      });

      socketConnection.on('connect_error', (error) => {
        console.error('🔥 CLIENT: Socket.IO bağlantı hatası:', error);
      });

      socketConnection.on('reconnect', (attemptNumber) => {
        console.log('🔄 CLIENT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);
      });

      socketConnection.on('reconnect_error', (error) => {
        console.error('🔥 CLIENT: Socket.IO yeniden bağlanma hatası:', error);
      });

      // Heartbeat mekanizması
      const heartbeatInterval = setInterval(() => {
        if (socketConnection.connected) {
          socketConnection.emit('ping', (response) => {
            if (response === 'pong') {
              console.log('💓 CLIENT: Heartbeat OK');
            }
          });
        }
      }, 30000); // 30 saniyede bir ping gönder

      return () => {
        clearInterval(heartbeatInterval);
        socketConnection.disconnect();
      };
    } else {
      console.log('❌ CLIENT: Socket bağlantısı kurulamıyor - token veya user eksik:', {
        hasToken: !!token,
        hasUser: !!user?.id
      });
    }
  }, [user?.id]);

  // Socket event listeners - ayrı useEffect
  useEffect(() => {
    if (socket && user?.id) {
      console.log('🎧 Socket event listeners kuruluyor...');

      // Sayfa yüklendiğinde online users listesini al
      if (socket.connected) {
        console.log('🔄 CLIENT: Requesting online users list on page load');
        socket.emit('get_online_users');
      }

      // Socket event listeners
      const handleNewMessage = (message) => {
        console.log('📨 CLIENT: Yeni mesaj alındı:', message);
        console.log('👤 CLIENT: Current user ID:', user.id);
        console.log('💬 CLIENT: Message sender ID:', message.senderId);
        console.log('🏠 CLIENT: Message conversation ID:', message.conversationId);

        // Conversation listesini ÖNCE güncelle (her zaman)
        setConversations(prev => {
          console.log('📋 CLIENT: Updating conversations list');
          return prev.map(conv =>
            conv.id === message.conversationId
              ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }
              : conv
          );
        });

        // Eğer şu anda bu conversation açıksa, mesajı da ekle
        setSelectedConversation(currentSelected => {
          console.log('🎯 CLIENT: Current selected conversation:', currentSelected?.id);
          if (currentSelected && message.conversationId === currentSelected.id) {
            console.log('✅ CLIENT: Adding message to current conversation');
            setMessages(prev => {
              // Mesajın zaten var olup olmadığını kontrol et
              const messageExists = prev.some(msg => msg.id === message.id);
              if (messageExists) {
                console.log('⚠️ CLIENT: Message already exists, skipping:', message.id);
                return prev;
              }

              return [...prev, {
                id: message.id,
                senderId: message.senderId,
                senderName: message.sender.name,
                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,
                text: message.content,
                timestamp: message.createdAt,
                read: message.isRead,
                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir
              }];
            });

            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle
            if (message.senderId !== user.id) {
              console.log('📖 CLIENT: Auto-marking message as read since conversation is open');
              setTimeout(() => {
                markConversationAsRead(message.conversationId);
              }, 500); // Kısa bir gecikme ile
            }

            // Scroll to bottom
            setTimeout(() => {
              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }, 100);
          } else {
            console.log('❌ CLIENT: Not adding message - conversation not selected or different conversation');
          }
          return currentSelected;
        });
      };

      const handleMessageSent = (message) => {
        console.log('✅ Mesaj gönderildi onayı:', message);
      };

      const handleUserStatusChange = (data) => {
        console.log('🔄 CLIENT: User status change:', data);
        setOnlineUsers(prev => {
          const newSet = new Set(prev);
          if (data.status === 'online') {
            newSet.add(data.userId);
          } else {
            newSet.delete(data.userId);
          }
          console.log('🟢 CLIENT: Online users updated:', Array.from(newSet));
          return newSet;
        });
      };

      const handleOnlineUsersList = (userIds) => {
        console.log('📋 CLIENT: Online users list received:', userIds);
        setOnlineUsers(new Set(userIds));
      };

      const handleUserTyping = (data) => {
        setSelectedConversation(currentSelected => {
          if (currentSelected && data.conversationId === currentSelected.id) {
            setTypingUsers(prev => new Set([...prev, data.userId]));
          }
          return currentSelected;
        });
      };

      const handleUserStoppedTyping = (data) => {
        setTypingUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.userId);
          return newSet;
        });
      };

      const handleMessagesRead = (data) => {
        console.log('👁️ CLIENT: Messages read event:', data);
        console.log('👁️ CLIENT: Read by user:', data.readBy, 'Current user:', user.id);
        console.log('👁️ CLIENT: Message IDs marked as read:', data.messageIds);

        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle
        if (data.readBy !== user.id) {
          setMessages(prev => prev.map(msg => {
            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle
            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {
              console.log('👁️ CLIENT: Marking my message as read:', msg.id);
              return { ...msg, read: true };
            }
            // Ayrıca messageIds listesindeki mesajları da güncelle
            if (data.messageIds.includes(msg.id)) {
              console.log('👁️ CLIENT: Marking specific message as read:', msg.id);
              return { ...msg, read: true };
            }
            return msg;
          }));

          // Conversation listesindeki unread durumunu da güncelle
          setConversations(prev => prev.map(conv =>
            conv.id === data.conversationId
              ? { ...conv, unread: false }
              : conv
          ));
        }
      };

      // Event listener'ları ekle
      console.log('🎧 CLIENT: Adding socket event listeners...');
      socket.on('new_message', handleNewMessage);
      socket.on('message_sent', handleMessageSent);
      socket.on('user_status_change', handleUserStatusChange);
      socket.on('user_typing', handleUserTyping);
      socket.on('user_stopped_typing', handleUserStoppedTyping);
      socket.on('messages_read', handleMessagesRead);
      socket.on('online_users_list', handleOnlineUsersList);
      console.log('✅ CLIENT: Socket event listeners added');

      // Cleanup function
      return () => {
        socket.off('new_message', handleNewMessage);
        socket.off('message_sent', handleMessageSent);
        socket.off('user_status_change', handleUserStatusChange);
        socket.off('user_typing', handleUserTyping);
        socket.off('user_stopped_typing', handleUserStoppedTyping);
        socket.off('messages_read', handleMessagesRead);
        socket.off('online_users_list', handleOnlineUsersList);
      };
    }
  }, [socket, user.id]);

  // Conversations yükleme - ayrı useEffect
  useEffect(() => {
    loadConversations();
  }, []);

  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez
  const [joinedConversations, setJoinedConversations] = useState(new Set());

  useEffect(() => {
    if (socket && conversations.length > 0) {
      console.log('🏠 CLIENT: Tüm conversation\'lara katılıyor...', conversations.length, 'conversation');

      conversations.forEach(conversation => {
        if (!joinedConversations.has(conversation.id)) {
          socket.emit('join_conversation', conversation.id);
          console.log(`🏠 CLIENT: Conversation ${conversation.id} katıldı`);
          setJoinedConversations(prev => new Set([...prev, conversation.id]));
        }
      });
    }
  }, [socket, conversations, joinedConversations]);

  // Conversations yükleme
  const loadConversations = async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/messages/conversations');

      // API verisini frontend formatına çevir
      const formattedConversations = response.data.conversations.map(conversation => ({
        id: conversation.id,
        expertId: conversation.otherUser.id,
        expertName: conversation.otherUser.name,
        expertTitle: conversation.otherUser.role === 'Expert' ? 'Uzman' : conversation.otherUser.role,
        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',
        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,
        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,
        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',
        starred: false,
        archived: false
      }));

      setConversations(formattedConversations);
    } catch (error) {
      console.error('Konuşmalar yüklenirken hata:', error);
      toast.error('Konuşmalar yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  };

  // Mesaj yükleme ve okundu işaretleme
  useEffect(() => {
    if (selectedConversation) {
      loadMessages(selectedConversation.id);
      markConversationAsRead(selectedConversation.id);

      // Conversation'ı seçildiğinde unread durumunu false yap
      setConversations(prev => prev.map(conv =>
        conv.id === selectedConversation.id
          ? { ...conv, unread: false }
          : conv
      ));
    }
  }, [selectedConversation]);

  // Conversation'daki mesajları okundu olarak işaretle
  const markConversationAsRead = async (conversationId) => {
    try {
      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)
      const response = await api.put(`/messages/conversations/${conversationId}/read`);

      if (response.data.updatedCount > 0) {
        console.log('📖 CLIENT: Marked', response.data.updatedCount, 'messages as read');

        // Local state'i güncelle
        setMessages(prev => prev.map(msg =>
          msg.senderId !== user.id ? { ...msg, read: true } : msg
        ));

        // Socket'e okundu bilgisi gönder
        if (socket) {
          socket.emit('mark_messages_read', {
            conversationId,
            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor
          });
        }
      }
    } catch (error) {
      console.error('Mesajları okundu olarak işaretlerken hata:', error);
    }
  };

  // Conversations yükleme
  const loadMessages = async (conversationId) => {
    try {
      const response = await api.get(`/messages/conversations/${conversationId}`);

      // API verisini frontend formatına çevir
      const formattedMessages = response.data.messages.map(message => ({
        id: message.id,
        senderId: message.senderId,
        senderName: message.sender.name,
        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,
        text: message.content,
        timestamp: message.createdAt,
        read: message.isRead,
        delivered: true, // Veritabanından gelen mesajlar delivered olarak kabul edilir
        attachments: message.attachments
      }));

      setMessages(formattedMessages);

      // Scroll to bottom
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    } catch (error) {
      console.error('Mesajlar yüklenirken hata:', error);
      toast.error('Mesajlar yüklenemedi');
    }
  };
  // Mesaj gönderme
  const sendMessage = async () => {
    if (!messageText.trim() || !selectedConversation) return;

    console.log('📤 Mesaj gönderiliyor:', {
      receiverId: selectedConversation.expertId,
      content: messageText.trim()
    });

    try {
      const response = await api.post('/messages/send', {
        receiverId: selectedConversation.expertId,
        content: messageText.trim()
      });

      console.log('✅ Mesaj gönderildi:', response.data);

      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek
      setMessageText('');

    } catch (error) {
      console.error('Mesaj gönderilirken hata:', error);
      toast.error('Mesaj gönderilemedi');
    }
  };

  useEffect(() => {
    // Mesajları otomatik kaydır
    if (messagesEndRef.current && messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Mesaj gönderme
  const handleSendMessage = (e) => {
    e.preventDefault();
    sendMessage();
  };

  // Konuşma seçme
  const handleSelectConversation = (conversation) => {
    setSelectedConversation(conversation);
  };

  // Tarih formatı
  const formatMessageDate = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return format(date, 'HH:mm', { locale: tr });
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Dün ' + format(date, 'HH:mm', { locale: tr });
    } else {
      return format(date, 'dd MMM HH:mm', { locale: tr });
    }
  };

  // Konuşmaları filtrele
  const filteredConversations = conversations.filter(conv => {
    // Arama filtresi
    const matchesSearch = conv.expertName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Durum filtresi
    const matchesFilter = filter === 'all' ||
                         (filter === 'unread' && conv.unread) ||
                         (filter === 'archived' && conv.archived) ||
                         (filter === 'starred' && conv.starred);
                        
    return matchesSearch && matchesFilter;
  });

  // Konuşmayı yıldızla
  const toggleStar = (id) => {
    setConversations(prevConversations =>
      prevConversations.map(conv =>
        conv.id === id ? { ...conv, starred: !conv.starred } : conv
      )
    );
    
    if (selectedConversation && selectedConversation.id === id) {
      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));
    }
  };

  // Konuşmayı arşivle
  const toggleArchive = (id) => {
    setConversations(prevConversations =>
      prevConversations.map(conv =>
        conv.id === id ? { ...conv, archived: !conv.archived } : conv
      )
    );
    
    if (selectedConversation && selectedConversation.id === id) {
      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        {/* Başlık ve Üst Kısım */}
        <div className="bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg rounded-lg p-6 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h1 className="text-2xl font-bold text-white">Mesajlarım</h1>
              <p className="mt-1 text-pink-100">
                Uzmanlarınızla güvenli bir şekilde iletişim kurun
              </p>
            </div>
            <div className="mt-3 sm:mt-0 flex space-x-2">
              <Link
                to="/client/experts"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-pink-600 bg-white hover:bg-pink-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150"
              >
                <UserGroupIcon className="h-4 w-4 mr-2" />
                Uzmanlar
              </Link>
              <Link
                to="/client/appointments"
                className="inline-flex items-center px-4 py-2 border border-white text-sm font-medium rounded-md shadow-sm text-white hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-150"
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Randevularım
              </Link>
            </div>
          </div>
        </div>
        
        {/* Mesajlaşma arayüzü */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="grid grid-cols-12 h-[75vh]">
            {/* Sol Kenar - Konuşma Listesi */}
            <div className="col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col">
              <div className="p-4 border-b border-gray-200 bg-white">
                <h1 className="text-xl font-semibold text-gray-800 flex items-center">
                  <ChatBubbleLeftEllipsisIcon className="h-6 w-6 text-teal-600 mr-2" />
                  Mesajlar
                </h1>
                <div className="mt-3 relative">
                  <input
                    type="text"
                    placeholder="Konuşmalarda ara..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-2.5" />
                </div>
                <div className="mt-3 flex space-x-2">
                  <button
                    className={`px-3 py-1 text-sm rounded-full ${filter === 'all' 
                      ? 'bg-teal-100 text-teal-800' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                    onClick={() => setFilter('all')}
                  >
                    Tümü
                  </button>
                  <button
                    className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' 
                      ? 'bg-teal-100 text-teal-800' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                    onClick={() => setFilter('unread')}
                  >
                    Okunmamış
                  </button>
                  <button
                    className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' 
                      ? 'bg-teal-100 text-teal-800' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                    onClick={() => setFilter('starred')}
                  >
                    Yıldızlı
                  </button>
                  <button
                    className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' 
                      ? 'bg-teal-100 text-teal-800' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
                    onClick={() => setFilter('archived')}
                  >
                    Arşiv
                  </button>
                </div>
              </div>
              <div 
                ref={conversationsRef}
                style={{
                  height: 'calc(75vh - 145px)',
                  overflowY: 'auto',
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#D1D5DB #F3F4F6'
                }}
              >
                {filteredConversations.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    Hiç mesajınız yok
                  </div>
                ) : (
                  filteredConversations.map(conversation => (
                    <div
                      key={conversation.id}
                      className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${
                        selectedConversation?.id === conversation.id ? 'bg-teal-50' : ''
                      } ${conversation.unread ? 'bg-teal-50 hover:bg-teal-100' : ''}`}
                      onClick={() => handleSelectConversation(conversation)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="relative flex-shrink-0">
                          <img
                            src={conversation.avatar}
                            alt={conversation.expertName}
                            className={`h-10 w-10 rounded-full ${
                              selectedConversation?.id === conversation.id 
                                ? 'ring-2 ring-teal-600' 
                                : ''
                            }`}
                          />
                          {conversation.status === 'online' && (
                            <span className="absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white"></span>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start">
                            <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>
                              {conversation.expertName}
                            </h3>
                            <div className="flex items-center space-x-1">
                              {conversation.starred && (
                                <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                              )}
                              <span className="text-xs text-gray-500">
                                {formatMessageDate(conversation.timestamp)}
                              </span>
                            </div>
                          </div>
                          <p className="text-xs text-gray-500">{conversation.expertTitle}</p>
                          <p className={`text-sm truncate mt-1 ${
                            conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'
                          }`}>
                            {conversation.lastMessage}
                          </p>
                          <div className="flex justify-between items-center mt-1">
                            <span className="text-xs text-gray-500">
                              {onlineUsers.has(conversation.expertId)
                                ? <span className="text-green-500 font-medium">Çevrimiçi</span>
                                : conversation.lastSeen
                                  ? `Son görülme: ${conversation.lastSeen}`
                                  : ''}
                            </span>
                            <div className="flex space-x-1">
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleStar(conversation.id);
                                }}
                                className="text-gray-400 hover:text-yellow-400"
                              >
                                <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />
                              </button>
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleArchive(conversation.id);
                                }}
                                className="text-gray-400 hover:text-gray-600"
                              >
                                <ArchiveBoxIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                      {conversation.unread && (
                        <span className="inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-teal-600 text-white">
                          Yeni
                        </span>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
            {/* Sağ Taraf - Mesaj Alanı */}
            <div className="col-span-12 md:col-span-8 flex flex-col">
              {selectedConversation ? (
                <>
                  {/* Mesajlaşma Başlığı */}
                  <div className="p-4 border-b border-gray-200 bg-white flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="relative mr-3">
                        <img
                          src={selectedConversation.avatar}
                          alt={selectedConversation.expertName}
                          className="h-10 w-10 rounded-full"
                        />
                        {selectedConversation.status === 'online' && (
                          <span className="absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white"></span>
                        )}
                      </div>
                      <div>
                        <h2 className="text-lg font-medium text-gray-800">
                          {selectedConversation.expertName}
                        </h2>
                        <p className="text-xs text-gray-500">
                          {selectedConversation.expertTitle} {' • '}
                          {selectedConversation.status === 'online' 
                            ? 'Çevrimiçi' 
                            : selectedConversation.lastSeen 
                              ? `Son görülme: ${selectedConversation.lastSeen}` 
                              : ''}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Link 
                        to={`/client/experts/${selectedConversation.expertId}`}
                        className="p-2 rounded-full hover:bg-gray-100 text-gray-600"
                      >
                        <UserIcon className="h-5 w-5" />
                      </Link>
                      <Link 
                        to={`/client/appointments?expert=${selectedConversation.expertId}`}
                        className="p-2 rounded-full hover:bg-gray-100 text-gray-600"
                      >
                        <ClockIcon className="h-5 w-5" />
                      </Link>
                      <Link 
                        to={`/client/sessions?expert=${selectedConversation.expertId}`}
                        className="p-2 rounded-full hover:bg-gray-100 text-gray-600"
                      >
                        <VideoCameraIcon className="h-5 w-5" />
                      </Link>
                      <button className="p-2 rounded-full hover:bg-gray-100 text-gray-600">
                        <EllipsisHorizontalIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  {/* Mesaj Alanı */}
                  <div 
                    ref={messagesContainerRef}
                    className="p-4 bg-gray-50"
                    style={{
                      height: 'calc(75vh - 195px)',
                      overflowY: 'auto',
                      scrollbarWidth: 'thin',
                      scrollbarColor: '#D1D5DB #F3F4F6'
                    }}
                  >
                    {messages.map((message, index) => {
                      const isSender = message.senderId === user.id;
                      const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;
                      
                      return (
                        <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>
                          {!isSender && showAvatar && (
                            <img 
                              src={message.senderAvatar} 
                              alt={message.senderName}
                              className="h-8 w-8 rounded-full mr-2 mt-1"
                            />
                          )}
                          {!isSender && !showAvatar && <div className="w-8 mr-2"></div>}
                          <div 
                            className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${
                              isSender 
                                ? 'bg-teal-600 text-white rounded-br-none' 
                                : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'
                            }`}
                          >
                            <p className="text-sm">{message.text}</p>
                            <div className={`text-xs mt-1 ${isSender ? 'text-teal-200' : 'text-gray-500'} flex items-center justify-end`}>
                              {formatMessageDate(message.timestamp)}
                              {isSender && (
                                <CheckCircleIcon
                                  className={`h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`}
                                  title={message.read ? 'Okundu' : 'İletildi'}
                                />
                              )}
                            </div>
                          </div>
                          {isSender && showAvatar && (
                            <img 
                              src={message.senderAvatar}
                              alt={message.senderName}
                              className="h-8 w-8 rounded-full ml-2 mt-1"
                            />
                          )}
                          {isSender && !showAvatar && <div className="w-8 ml-2"></div>}
                        </div>
                      );
                    })}
                    <div ref={messagesEndRef} />
                  </div>

                  {/* Mesaj Giriş Alanı */}
                  <div className="p-3 border-t border-gray-200 bg-white">
                    <form onSubmit={handleSendMessage} className="flex items-end">
                      <button
                        type="button"
                        className="p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none"
                      >
                        <PaperClipIcon className="h-5 w-5" />
                      </button>
                      <div className="flex-1 mx-2">
                        <textarea
                          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none"
                          placeholder="Mesajınızı yazın..."
                          rows="2"
                          value={messageText}
                          onChange={(e) => setMessageText(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              sendMessage();
                            }
                          }}
                        ></textarea>
                      </div>
                      <button
                        type="button"
                        className="p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none"
                      >
                        <FaceSmileIcon className="h-5 w-5" />
                      </button>
                      <button
                        type="submit"
                        disabled={!messageText.trim()}
                        className={`ml-2 p-2 rounded-full ${
                          messageText.trim() 
                            ? 'bg-teal-600 text-white hover:bg-teal-700' 
                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        } focus:outline-none`}
                      >
                        <PaperAirplaneIcon className="h-5 w-5" />
                      </button>
                    </form>
                  </div>
                </>
              ) : (
                // Mesaj seçilmediğinde
                <div className="flex-1 flex flex-col items-center justify-center p-6 bg-gray-50">
                  <div className="w-full max-w-md text-center">
                    <ChatBubbleLeftEllipsisIcon className="h-16 w-16 text-gray-300 mb-4 mx-auto" />
                    <h3 className="text-xl font-medium text-gray-800 mb-2">Mesajlarınız</h3>
                    <p className="text-gray-500 mx-auto">
                      Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir uzmanla iletişime geçin.
                    </p>
                    <div className="mt-6">
                      <Link
                        to="/client/experts"
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                      >
                        <UserIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                        Uzmanları Keşfedin
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 

export default ClientMessagesPage; 