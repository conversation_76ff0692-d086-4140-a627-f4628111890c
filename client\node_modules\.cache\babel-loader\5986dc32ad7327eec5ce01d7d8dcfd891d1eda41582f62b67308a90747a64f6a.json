{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\n// Source: https://www.unicode.org/cldr/charts/32/summary/te.html\n\n// CLDR #1807 - #1811\nconst dateFormats = {\n  full: \"d, MMMM y, EEEE\",\n  long: \"d MMMM, y\",\n  medium: \"d MMM, y\",\n  short: \"dd-MM-yy\"\n};\n\n// CLDR #1807 - #1811\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\",\n  long: \"h:mm:ss a z\",\n  medium: \"h:mm:ss a\",\n  short: \"h:mm a\"\n};\n\n// CLDR #1815 - #1818\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}'కి'\",\n  long: \"{{date}} {{time}}'కి'\",\n  medium: \"{{date}} {{time}}\",\n  short: \"{{date}} {{time}}\"\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}