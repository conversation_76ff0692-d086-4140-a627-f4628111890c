{"ast": null, "code": "import { numberToLocale } from \"./localize.js\";\n\n// Source: https://www.unicode.org/cldr/charts/32/summary/hi.html\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"१ सेकंड से कम\",\n    // CLDR #1310\n    other: \"{{count}} सेकंड से कम\"\n  },\n  xSeconds: {\n    one: \"१ सेकंड\",\n    other: \"{{count}} सेकंड\"\n  },\n  halfAMinute: \"आधा मिनट\",\n  lessThanXMinutes: {\n    one: \"१ मिनट से कम\",\n    other: \"{{count}} मिनट से कम\"\n  },\n  xMinutes: {\n    one: \"१ मिनट\",\n    // CLDR #1307\n    other: \"{{count}} मिनट\"\n  },\n  aboutXHours: {\n    one: \"लगभग १ घंटा\",\n    other: \"लगभग {{count}} घंटे\"\n  },\n  xHours: {\n    one: \"१ घंटा\",\n    // CLDR #1304\n    other: \"{{count}} घंटे\" // CLDR #4467\n  },\n  xDays: {\n    one: \"१ दिन\",\n    // CLDR #1286\n    other: \"{{count}} दिन\"\n  },\n  aboutXWeeks: {\n    one: \"लगभग १ सप्ताह\",\n    other: \"लगभग {{count}} सप्ताह\"\n  },\n  xWeeks: {\n    one: \"१ सप्ताह\",\n    other: \"{{count}} सप्ताह\"\n  },\n  aboutXMonths: {\n    one: \"लगभग १ महीना\",\n    other: \"लगभग {{count}} महीने\"\n  },\n  xMonths: {\n    one: \"१ महीना\",\n    other: \"{{count}} महीने\"\n  },\n  aboutXYears: {\n    one: \"लगभग १ वर्ष\",\n    other: \"लगभग {{count}} वर्ष\" // CLDR #4823\n  },\n  xYears: {\n    one: \"१ वर्ष\",\n    other: \"{{count}} वर्ष\"\n  },\n  overXYears: {\n    one: \"१ वर्ष से अधिक\",\n    other: \"{{count}} वर्ष से अधिक\"\n  },\n  almostXYears: {\n    one: \"लगभग १ वर्ष\",\n    other: \"लगभग {{count}} वर्ष\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", numberToLocale(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"मे \";\n    } else {\n      return result + \" पहले\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}