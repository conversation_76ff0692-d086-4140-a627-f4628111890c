{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"Î\", \"D\"],\n  abbreviated: [\"Î.d.C.\", \"D.C.\"],\n  wide: [\"Înainte de Cristos\", \"După Cristos\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  wide: [\"primul trimestru\", \"al doilea trimestru\", \"al treilea trimestru\", \"al patrulea trimestru\"]\n};\nconst monthValues = {\n  narrow: [\"I\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"ian\", \"feb\", \"mar\", \"apr\", \"mai\", \"iun\", \"iul\", \"aug\", \"sep\", \"oct\", \"noi\", \"dec\"],\n  wide: [\"ianuarie\", \"februarie\", \"martie\", \"aprilie\", \"mai\", \"iunie\", \"iulie\", \"august\", \"septembrie\", \"octombrie\", \"noiembrie\", \"decembrie\"]\n};\nconst dayValues = {\n  narrow: [\"d\", \"l\", \"m\", \"m\", \"j\", \"v\", \"s\"],\n  short: [\"du\", \"lu\", \"ma\", \"mi\", \"jo\", \"vi\", \"sâ\"],\n  abbreviated: [\"dum\", \"lun\", \"mar\", \"mie\", \"joi\", \"vin\", \"sâm\"],\n  wide: [\"duminică\", \"luni\", \"marți\", \"miercuri\", \"joi\", \"vineri\", \"sâmbătă\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"ami\",\n    morning: \"dim\",\n    afternoon: \"da\",\n    evening: \"s\",\n    night: \"n\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}