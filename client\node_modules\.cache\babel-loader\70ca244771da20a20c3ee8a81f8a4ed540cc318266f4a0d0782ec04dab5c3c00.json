{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل از میلاد\", \"بعد از میلاد\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"س‌م1\", \"س‌م2\", \"س‌م3\", \"س‌م4\"],\n  wide: [\"سه‌ماهه 1\", \"سه‌ماهه 2\", \"سه‌ماهه 3\", \"سه‌ماهه 4\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ژ\", \"ف\", \"م\", \"آ\", \"م\", \"ج\", \"ج\", \"آ\", \"س\", \"ا\", \"ن\", \"د\"],\n  abbreviated: [\"ژانـ\", \"فور\", \"مارس\", \"آپر\", \"می\", \"جون\", \"جولـ\", \"آگو\", \"سپتـ\", \"اکتـ\", \"نوامـ\", \"دسامـ\"],\n  wide: [\"ژانویه\", \"فوریه\", \"مارس\", \"آپریل\", \"می\", \"جون\", \"جولای\", \"آگوست\", \"سپتامبر\", \"اکتبر\", \"نوامبر\", \"دسامبر\"]\n};\nconst dayValues = {\n  narrow: [\"ی\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  short: [\"1ش\", \"2ش\", \"3ش\", \"4ش\", \"5ش\", \"ج\", \"ش\"],\n  abbreviated: [\"یکشنبه\", \"دوشنبه\", \"سه‌شنبه\", \"چهارشنبه\", \"پنجشنبه\", \"جمعه\", \"شنبه\"],\n  wide: [\"یکشنبه\", \"دوشنبه\", \"سه‌شنبه\", \"چهارشنبه\", \"پنجشنبه\", \"جمعه\", \"شنبه\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ق\",\n    pm: \"ب\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"ص\",\n    afternoon: \"ب.ظ.\",\n    evening: \"ع\",\n    night: \"ش\"\n  },\n  abbreviated: {\n    am: \"ق.ظ.\",\n    pm: \"ب.ظ.\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\"\n  },\n  wide: {\n    am: \"قبل‌ازظهر\",\n    pm: \"بعدازظهر\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ق\",\n    pm: \"ب\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"ص\",\n    afternoon: \"ب.ظ.\",\n    evening: \"ع\",\n    night: \"ش\"\n  },\n  abbreviated: {\n    am: \"ق.ظ.\",\n    pm: \"ب.ظ.\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\"\n  },\n  wide: {\n    am: \"قبل‌ازظهر\",\n    pm: \"بعدازظهر\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}