{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"bir saniyeden az\",\n    other: \"{{count}} saniyeden az\"\n  },\n  xSeconds: {\n    one: \"1 saniye\",\n    other: \"{{count}} saniye\"\n  },\n  halfAMinute: \"yarım dakika\",\n  lessThanXMinutes: {\n    one: \"bir dakikadan az\",\n    other: \"{{count}} dakikadan az\"\n  },\n  xMinutes: {\n    one: \"1 dakika\",\n    other: \"{{count}} dakika\"\n  },\n  aboutXHours: {\n    one: \"yaklaşık 1 saat\",\n    other: \"yaklaşık {{count}} saat\"\n  },\n  xHours: {\n    one: \"1 saat\",\n    other: \"{{count}} saat\"\n  },\n  xDays: {\n    one: \"1 gün\",\n    other: \"{{count}} gün\"\n  },\n  aboutXWeeks: {\n    one: \"yaklaşık 1 hafta\",\n    other: \"yaklaşık {{count}} hafta\"\n  },\n  xWeeks: {\n    one: \"1 hafta\",\n    other: \"{{count}} hafta\"\n  },\n  aboutXMonths: {\n    one: \"yaklaşık 1 ay\",\n    other: \"yaklaşık {{count}} ay\"\n  },\n  xMonths: {\n    one: \"1 ay\",\n    other: \"{{count}} ay\"\n  },\n  aboutXYears: {\n    one: \"yaklaşık 1 yıl\",\n    other: \"yaklaşık {{count}} yıl\"\n  },\n  xYears: {\n    one: \"1 yıl\",\n    other: \"{{count}} yıl\"\n  },\n  overXYears: {\n    one: \"1 yıldan fazla\",\n    other: \"{{count}} yıldan fazla\"\n  },\n  almostXYears: {\n    one: \"neredeyse 1 yıl\",\n    other: \"neredeyse {{count}} yıl\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" sonra\";\n    } else {\n      return result + \" önce\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}