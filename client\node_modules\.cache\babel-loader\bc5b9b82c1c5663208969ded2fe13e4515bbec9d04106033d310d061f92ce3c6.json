{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"б.з.д.\", \"б.з.\"],\n  abbreviated: [\"б.з.д.\", \"б.з.\"],\n  wide: [\"біздің заманымызға дейін\", \"біздің заманымыз\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ші тоқ.\", \"2-ші тоқ.\", \"3-ші тоқ.\", \"4-ші тоқ.\"],\n  wide: [\"1-ші тоқсан\", \"2-ші тоқсан\", \"3-ші тоқсан\", \"4-ші тоқсан\"]\n};\nconst monthValues = {\n  narrow: [\"Қ\", \"А\", \"Н\", \"С\", \"М\", \"М\", \"Ш\", \"Т\", \"Қ\", \"Қ\", \"Қ\", \"Ж\"],\n  abbreviated: [\"қаң\", \"ақп\", \"нау\", \"сәу\", \"мам\", \"мау\", \"шіл\", \"там\", \"қыр\", \"қаз\", \"қар\", \"жел\"],\n  wide: [\"қаңтар\", \"ақпан\", \"наурыз\", \"сәуір\", \"мамыр\", \"маусым\", \"шілде\", \"тамыз\", \"қыркүйек\", \"қазан\", \"қараша\", \"желтоқсан\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"Қ\", \"А\", \"Н\", \"С\", \"М\", \"М\", \"Ш\", \"Т\", \"Қ\", \"Қ\", \"Қ\", \"Ж\"],\n  abbreviated: [\"қаң\", \"ақп\", \"нау\", \"сәу\", \"мам\", \"мау\", \"шіл\", \"там\", \"қыр\", \"қаз\", \"қар\", \"жел\"],\n  wide: [\"қаңтар\", \"ақпан\", \"наурыз\", \"сәуір\", \"мамыр\", \"маусым\", \"шілде\", \"тамыз\", \"қыркүйек\", \"қазан\", \"қараша\", \"желтоқсан\"]\n};\nconst dayValues = {\n  narrow: [\"Ж\", \"Д\", \"С\", \"С\", \"Б\", \"Ж\", \"С\"],\n  short: [\"жс\", \"дс\", \"сс\", \"ср\", \"бс\", \"жм\", \"сб\"],\n  abbreviated: [\"жс\", \"дс\", \"сс\", \"ср\", \"бс\", \"жм\", \"сб\"],\n  wide: [\"жексенбі\", \"дүйсенбі\", \"сейсенбі\", \"сәрсенбі\", \"бейсенбі\", \"жұма\", \"сенбі\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасы\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күндіз\",\n    evening: \"кеш\",\n    night: \"түн\"\n  },\n  wide: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасы\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күндіз\",\n    evening: \"кеш\",\n    night: \"түн\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасында\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күн\",\n    evening: \"кеш\",\n    night: \"түн\"\n  },\n  wide: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасында\",\n    noon: \"түсте\",\n    morning: \"таңертең\",\n    afternoon: \"күндіз\",\n    evening: \"кеште\",\n    night: \"түнде\"\n  }\n};\nconst suffixes = {\n  0: \"-ші\",\n  1: \"-ші\",\n  2: \"-ші\",\n  3: \"-ші\",\n  4: \"-ші\",\n  5: \"-ші\",\n  6: \"-шы\",\n  7: \"-ші\",\n  8: \"-ші\",\n  9: \"-шы\",\n  10: \"-шы\",\n  20: \"-шы\",\n  30: \"-шы\",\n  40: \"-шы\",\n  50: \"-ші\",\n  60: \"-шы\",\n  70: \"-ші\",\n  80: \"-ші\",\n  90: \"-шы\",\n  100: \"-ші\"\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const mod10 = number % 10;\n  const b = number >= 100 ? 100 : null;\n  const suffix = suffixes[number] || suffixes[mod10] || b && suffixes[b] || \"\";\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}