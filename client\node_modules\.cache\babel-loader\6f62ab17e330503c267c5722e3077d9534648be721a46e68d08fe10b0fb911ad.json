{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"М.А\", \"М\"],\n  abbreviated: [\"М.А\", \"М\"],\n  wide: [\"Милоддан Аввалги\", \"Мило<PERSON><PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-чор.\", \"2-чор.\", \"3-чор.\", \"4-чор.\"],\n  wide: [\"1-чорак\", \"2-чорак\", \"3-чорак\", \"4-чорак\"]\n};\nconst monthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\"янв\", \"фев\", \"мар\", \"апр\", \"май\", \"июн\", \"июл\", \"авг\", \"сен\", \"окт\", \"ноя\", \"дек\"],\n  wide: [\"январ\", \"феврал\", \"март\", \"апрел\", \"май\", \"июн\", \"июл\", \"август\", \"сентабр\", \"октабр\", \"ноябр\", \"декабр\"]\n};\nconst dayValues = {\n  narrow: [\"Я\", \"Д\", \"С\", \"Ч\", \"П\", \"Ж\", \"Ш\"],\n  short: [\"як\", \"ду\", \"се\", \"чо\", \"па\", \"жу\", \"ша\"],\n  abbreviated: [\"якш\", \"душ\", \"сеш\", \"чор\", \"пай\", \"жум\", \"шан\"],\n  wide: [\"якшанба\", \"душанба\", \"сешанба\", \"чоршанба\", \"пайшанба\", \"жума\", \"шанба\"]\n};\nconst dayPeriodValues = {\n  any: {\n    am: \"П.О.\",\n    pm: \"П.К.\",\n    midnight: \"ярим тун\",\n    noon: \"пешин\",\n    morning: \"эрталаб\",\n    afternoon: \"пешиндан кейин\",\n    evening: \"кечаси\",\n    night: \"тун\"\n  }\n};\nconst formattingDayPeriodValues = {\n  any: {\n    am: \"П.О.\",\n    pm: \"П.К.\",\n    midnight: \"ярим тун\",\n    noon: \"пешин\",\n    morning: \"эрталаб\",\n    afternoon: \"пешиндан кейин\",\n    evening: \"кечаси\",\n    night: \"тун\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"any\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}