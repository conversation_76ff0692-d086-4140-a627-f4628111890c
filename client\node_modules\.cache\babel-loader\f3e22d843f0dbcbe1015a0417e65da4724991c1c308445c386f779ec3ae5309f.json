{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst accusativeWeekdays = [\"воскресенье\", \"понедельник\", \"вторник\", \"среду\", \"четверг\", \"пятницу\", \"субботу\"];\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n      return \"'в прошлое \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в прошлый \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в прошлую \" + weekday + \" в' p\";\n  }\n}\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  if (day === 2 /* Tue */) {\n    return \"'во \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0:\n      return \"'в следующее \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n      return \"'в следующий \" + weekday + \" в' p\";\n    case 3:\n    case 5:\n    case 6:\n      return \"'в следующую \" + weekday + \" в' p\";\n  }\n}\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера в' p\",\n  today: \"'сегодня в' p\",\n  tomorrow: \"'завтра в' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}