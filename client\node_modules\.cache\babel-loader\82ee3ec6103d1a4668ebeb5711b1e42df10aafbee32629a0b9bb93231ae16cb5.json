{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"BC\", \"AD\"],\n  abbreviated: [\"BC\", \"AD\"],\n  wide: [\"기원전\", \"서기\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1분기\", \"2분기\", \"3분기\", \"4분기\"]\n};\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n  abbreviated: [\"1월\", \"2월\", \"3월\", \"4월\", \"5월\", \"6월\", \"7월\", \"8월\", \"9월\", \"10월\", \"11월\", \"12월\"],\n  wide: [\"1월\", \"2월\", \"3월\", \"4월\", \"5월\", \"6월\", \"7월\", \"8월\", \"9월\", \"10월\", \"11월\", \"12월\"]\n};\nconst dayValues = {\n  narrow: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  short: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  abbreviated: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  wide: [\"일요일\", \"월요일\", \"화요일\", \"수요일\", \"목요일\", \"금요일\", \"토요일\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  },\n  abbreviated: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  },\n  wide: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  },\n  abbreviated: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  },\n  wide: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options?.unit);\n  switch (unit) {\n    case \"minute\":\n    case \"second\":\n      return String(number);\n    case \"date\":\n      return number + \"일\";\n    default:\n      return number + \"번째\";\n  }\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}