{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ie.\", \"isz.\"],\n  abbreviated: [\"i. e.\", \"i. sz.\"],\n  wide: [\"<PERSON><PERSON><PERSON>\", \"id<PERSON><PERSON><PERSON>mításunk szerint\"]\n};\nconst quarterValues = {\n  narrow: [\"1.\", \"2.\", \"3.\", \"4.\"],\n  abbreviated: [\"1. n.év\", \"2. n.év\", \"3. n.év\", \"4. n.év\"],\n  wide: [\"1. negyedév\", \"2. negyedév\", \"3. negyedév\", \"4. negyedév\"]\n};\nconst formattingQuarterValues = {\n  narrow: [\"I.\", \"II.\", \"III.\", \"IV.\"],\n  abbreviated: [\"I. n.év\", \"II. n.év\", \"III. n.év\", \"IV. n.év\"],\n  wide: [\"I. negyedév\", \"II. negyedév\", \"III. negyedév\", \"IV. negyedév\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"Á\", \"M\", \"J\", \"J\", \"A\", \"Sz\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"jan.\", \"febr.\", \"m<PERSON>rc.\", \"ápr.\", \"m<PERSON>j.\", \"jún.\", \"júl.\", \"aug.\", \"szept.\", \"okt.\", \"nov.\", \"dec.\"],\n  wide: [\"janu<PERSON>r\", \"febru<PERSON>r\", \"m<PERSON>rcius\", \"április\", \"május\", \"június\", \"július\", \"augusztus\", \"szeptember\", \"október\", \"november\", \"december\"]\n};\nconst dayValues = {\n  narrow: [\"V\", \"H\", \"K\", \"Sz\", \"Cs\", \"P\", \"Sz\"],\n  short: [\"V\", \"H\", \"K\", \"Sze\", \"Cs\", \"P\", \"Szo\"],\n  abbreviated: [\"V\", \"H\", \"K\", \"Sze\", \"Cs\", \"P\", \"Szo\"],\n  wide: [\"vasárnap\", \"hétfő\", \"kedd\", \"szerda\", \"csütörtök\", \"péntek\", \"szombat\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"du.\",\n    evening: \"este\",\n    night: \"éjjel\"\n  },\n  abbreviated: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"du.\",\n    evening: \"este\",\n    night: \"éjjel\"\n  },\n  wide: {\n    am: \"de.\",\n    pm: \"du.\",\n    midnight: \"éjfél\",\n    noon: \"dél\",\n    morning: \"reggel\",\n    afternoon: \"délután\",\n    evening: \"este\",\n    night: \"éjjel\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1,\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}