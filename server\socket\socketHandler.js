/**
 * Socket.IO Handler
 * Handles real-time messaging functionality
 */

const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

/**
 * Initialize Socket.IO server
 * @param {object} server - HTTP server instance
 * @returns {object} Socket.IO server instance
 */
const initializeSocket = (server) => {
  const io = new Server(server, {
    cors: {
      origin: [
        "https://localhost:3000",
        "http://localhost:3000",
        "https://127.0.0.1:3000",
        "http://127.0.0.1:3000",
        "https://*************:3000",
        "http://*************:3000",
        "https://***********:3000",
        "http://***********:3000",
        // Tüm local network IP'leri için regex
        /^https?:\/\/192\.168\.\d+\.\d+:3000$/,
        /^https?:\/\/localhost:3000$/,
        /^https?:\/\/127\.0\.0\.1:3000$/
      ],
      methods: ["GET", "POST"],
      credentials: true
    },
    pingTimeout: 60000,
    pingInterval: 25000,
    transports: ['websocket', 'polling']
  });

  // Çevrimiçi kullanıcıları takip et
  const onlineUsers = new Set();

  // Authentication middleware for socket connections
  io.use((socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      console.log('🔐 Socket authentication attempt, token:', token ? 'exists' : 'missing');

      if (!token) {
        console.log('❌ Socket auth failed: No token');
        return next(new Error('Authentication error: No token provided'));
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      socket.userId = decoded.id;
      socket.userRole = decoded.role;

      console.log('✅ SERVER: Socket authenticated for user:', decoded.id, 'role:', decoded.role);
      console.log('🔑 SERVER: JWT decoded:', decoded);
      logger.info(`Socket authenticated for user ${decoded.id}`);
      next();
    } catch (error) {
      console.log('❌ Socket auth error:', error.message);
      logger.error('Socket authentication error:', error);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  // Handle socket connections
  io.on('connection', (socket) => {
    console.log(`🔌 SERVER: User ${socket.userId} connected via socket`);
    console.log(`🔗 SERVER: Socket ID: ${socket.id}`);
    console.log(`👤 SERVER: User role: ${socket.userRole}`);
    logger.info(`User ${socket.userId} connected via socket`);

    // Kullanıcıyı çevrimiçi listesine ekle
    onlineUsers.add(socket.userId);
    console.log(`🟢 SERVER: User ${socket.userId} is now online. Total online: ${onlineUsers.size}`);

    // Join user to their personal room
    socket.join(`user_${socket.userId}`);
    console.log(`🏠 SERVER: User ${socket.userId} joined room: user_${socket.userId}`);
    console.log(`🏠 SERVER: Socket rooms after join:`, Array.from(socket.rooms));

    // Diğer kullanıcılara online durumunu bildir
    socket.broadcast.emit('user_status_change', {
      userId: socket.userId,
      status: 'online'
    });

    // Handle joining conversation rooms
    socket.on('join_conversation', (conversationId) => {
      socket.join(`conversation_${conversationId}`);
      console.log(`🏠 SERVER: User ${socket.userId} joined conversation ${conversationId}`);
      console.log(`🏠 SERVER: Socket rooms after conversation join:`, Array.from(socket.rooms));
      logger.info(`User ${socket.userId} joined conversation ${conversationId}`);
    });

    // Handle leaving conversation rooms
    socket.on('leave_conversation', (conversationId) => {
      socket.leave(`conversation_${conversationId}`);
      logger.info(`User ${socket.userId} left conversation ${conversationId}`);
    });

    // Handle typing indicators
    socket.on('typing_start', (data) => {
      socket.to(`conversation_${data.conversationId}`).emit('user_typing', {
        userId: socket.userId,
        conversationId: data.conversationId
      });
    });

    socket.on('typing_stop', (data) => {
      socket.to(`conversation_${data.conversationId}`).emit('user_stopped_typing', {
        userId: socket.userId,
        conversationId: data.conversationId
      });
    });

    // Handle online status
    socket.on('user_online', () => {
      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'online'
      });
    });

    // Online kullanıcı listesi gönder
    socket.on('get_online_users', () => {
      socket.emit('online_users_list', Array.from(onlineUsers));
    });

    // Handle messages read
    socket.on('mark_messages_read', (data) => {
      console.log(`📖 SERVER: User ${socket.userId} marked messages as read in conversation ${data.conversationId}`);
      // Conversation'daki diğer kullanıcılara bildir
      socket.to(`conversation_${data.conversationId}`).emit('messages_read', {
        conversationId: data.conversationId,
        readBy: socket.userId,
        messageIds: data.messageIds
      });
    });

    // Handle heartbeat/ping
    socket.on('ping', (callback) => {
      if (callback) callback('pong');
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log(`🔌 User ${socket.userId} disconnected, reason: ${reason}`);
      logger.info(`User ${socket.userId} disconnected, reason: ${reason}`);

      // Kullanıcıyı çevrimiçi listesinden çıkar
      onlineUsers.delete(socket.userId);
      console.log(`🔴 SERVER: User ${socket.userId} is now offline. Total online: ${onlineUsers.size}`);

      // Broadcast offline status
      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'offline'
      });
    });

    // Handle connection errors
    socket.on('error', (error) => {
      console.error(`🔥 Socket error for user ${socket.userId}:`, error);
      logger.error(`Socket error for user ${socket.userId}:`, error);
    });

    // Handle message read receipts
    socket.on('mark_messages_read', (data) => {
      socket.to(`conversation_${data.conversationId}`).emit('messages_read', {
        conversationId: data.conversationId,
        userId: socket.userId,
        messageIds: data.messageIds
      });
    });

    // ========== P2P WebRTC Meeting Handlers ==========

    // Join meeting room
    socket.on('join', (roomId) => {
      console.log(`🎥 User ${socket.userId} joining meeting room: ${roomId}`);

      // Check if user is already in this room
      if (socket.rooms.has(roomId)) {
        console.log(`⚠️ User ${socket.userId} already in room ${roomId}, ignoring join request`);
        return;
      }

      const room = io.sockets.adapter.rooms.get(roomId);
      const roomSize = room ? room.size : 0;

      // Maximum 2 participants per room
      if (roomSize >= 2) {
        socket.emit('full', roomId);
        console.log(`🚫 Meeting room ${roomId} is full`);
        return;
      }

      socket.join(roomId);
      const sizeAfterJoin = io.sockets.adapter.rooms.get(roomId)?.size || 0;

      console.log(`✅ User ${socket.userId} joined meeting room ${roomId}, participants: ${sizeAfterJoin}`);
      socket.emit('joined', { roomId, roomSize: sizeAfterJoin });

      // If 2 participants, start WebRTC negotiation
      if (sizeAfterJoin === 2) {
        console.log(`🚀 Meeting room ${roomId} ready for WebRTC`);

        // İkinci katılan kullanıcıya initiator olduğunu söyle
        socket.emit('ready', { isInitiator: true });

        // İlk katılan kullanıcıya initiator olmadığını söyle
        socket.to(roomId).emit('ready', { isInitiator: false });

        console.log(`📤 User ${socket.userId} is initiator, others are waiting for offer`);
      }
    });

    // WebRTC Offer
    socket.on('offer', ({ roomId, sdp }) => {
      console.log(`📤 WebRTC offer from user ${socket.userId} in room ${roomId}`);
      socket.to(roomId).emit('offer', sdp);
    });

    // WebRTC Answer
    socket.on('answer', ({ roomId, sdp }) => {
      console.log(`📥 WebRTC answer from user ${socket.userId} in room ${roomId}`);
      socket.to(roomId).emit('answer', sdp);
    });

    // ICE Candidate
    socket.on('candidate', ({ roomId, candidate }) => {
      console.log(`🧊 ICE candidate from user ${socket.userId} in room ${roomId}`);
      socket.to(roomId).emit('candidate', candidate);
    });

    // Leave meeting room
    socket.on('leave', (roomId) => {
      console.log(`👋 User ${socket.userId} leaving meeting room: ${roomId}`);
      socket.leave(roomId);
      socket.to(roomId).emit('leave');
    });
  });

  return io;
};

module.exports = { initializeSocket };
