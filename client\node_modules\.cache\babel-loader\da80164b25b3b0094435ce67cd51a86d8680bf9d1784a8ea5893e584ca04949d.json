{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\nconst dateFormats = {\n  full: \"วันEEEEที่ do MMMM y\",\n  long: \"do MMMM y\",\n  medium: \"d MMM y\",\n  short: \"dd/MM/yyyy\"\n};\nconst timeFormats = {\n  full: \"H:mm:ss น. zzzz\",\n  long: \"H:mm:ss น. z\",\n  medium: \"H:mm:ss น.\",\n  short: \"H:mm น.\"\n};\nconst dateTimeFormats = {\n  full: \"{{date}} 'เวลา' {{time}}\",\n  long: \"{{date}} 'เวลา' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\"\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"medium\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}