{"ast": null, "code": "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextFriday} function options.\n */\n\n/**\n * @name nextFriday\n * @category Weekday Helpers\n * @summary When is the next Friday?\n *\n * @description\n * When is the next Friday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Friday\n *\n * @example\n * // When is the next Friday after Mar, 22, 2020?\n * const result = nextFriday(new Date(2020, 2, 22))\n * //=> Fri Mar 27 2020 00:00:00\n */\nexport function nextFriday(date, options) {\n  return nextDay(date, 5, options);\n}\n\n// Fallback for modularized imports:\nexport default nextFriday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}