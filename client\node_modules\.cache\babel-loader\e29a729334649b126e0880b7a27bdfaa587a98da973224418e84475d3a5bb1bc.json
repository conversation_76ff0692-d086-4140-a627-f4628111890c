{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"לפנה״ס\", \"לספירה\"],\n  abbreviated: [\"לפנה״ס\", \"לספירה\"],\n  wide: [\"לפני הספירה\", \"לספירה\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"רבעון 1\", \"רבעון 2\", \"רבעון 3\", \"רבעון 4\"]\n};\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n  abbreviated: [\"ינו׳\", \"פבר׳\", \"מרץ\", \"אפר׳\", \"מאי\", \"יוני\", \"יולי\", \"אוג׳\", \"ספט׳\", \"אוק׳\", \"נוב׳\", \"דצמ׳\"],\n  wide: [\"ינואר\", \"פברואר\", \"מרץ\", \"אפריל\", \"מאי\", \"יוני\", \"יולי\", \"אוגוסט\", \"ספטמבר\", \"אוקטובר\", \"נובמבר\", \"דצמבר\"]\n};\nconst dayValues = {\n  narrow: [\"א׳\", \"ב׳\", \"ג׳\", \"ד׳\", \"ה׳\", \"ו׳\", \"ש׳\"],\n  short: [\"א׳\", \"ב׳\", \"ג׳\", \"ד׳\", \"ה׳\", \"ו׳\", \"ש׳\"],\n  abbreviated: [\"יום א׳\", \"יום ב׳\", \"יום ג׳\", \"יום ד׳\", \"יום ה׳\", \"יום ו׳\", \"שבת\"],\n  wide: [\"יום ראשון\", \"יום שני\", \"יום שלישי\", \"יום רביעי\", \"יום חמישי\", \"יום שישי\", \"יום שבת\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"לפנה״צ\",\n    pm: \"אחה״צ\",\n    midnight: \"חצות\",\n    noon: \"צהריים\",\n    morning: \"בוקר\",\n    afternoon: \"אחר הצהריים\",\n    evening: \"ערב\",\n    night: \"לילה\"\n  },\n  abbreviated: {\n    am: \"לפנה״צ\",\n    pm: \"אחה״צ\",\n    midnight: \"חצות\",\n    noon: \"צהריים\",\n    morning: \"בוקר\",\n    afternoon: \"אחר הצהריים\",\n    evening: \"ערב\",\n    night: \"לילה\"\n  },\n  wide: {\n    am: \"לפנה״צ\",\n    pm: \"אחה״צ\",\n    midnight: \"חצות\",\n    noon: \"צהריים\",\n    morning: \"בוקר\",\n    afternoon: \"אחר הצהריים\",\n    evening: \"ערב\",\n    night: \"לילה\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"לפנה״צ\",\n    pm: \"אחה״צ\",\n    midnight: \"חצות\",\n    noon: \"צהריים\",\n    morning: \"בבוקר\",\n    afternoon: \"בצהריים\",\n    evening: \"בערב\",\n    night: \"בלילה\"\n  },\n  abbreviated: {\n    am: \"לפנה״צ\",\n    pm: \"אחה״צ\",\n    midnight: \"חצות\",\n    noon: \"צהריים\",\n    morning: \"בבוקר\",\n    afternoon: \"אחר הצהריים\",\n    evening: \"בערב\",\n    night: \"בלילה\"\n  },\n  wide: {\n    am: \"לפנה״צ\",\n    pm: \"אחה״צ\",\n    midnight: \"חצות\",\n    noon: \"צהריים\",\n    morning: \"בבוקר\",\n    afternoon: \"אחר הצהריים\",\n    evening: \"בערב\",\n    night: \"בלילה\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n\n  // We only show words till 10\n  if (number <= 0 || number > 10) return String(number);\n  const unit = String(options?.unit);\n  const isFemale = [\"year\", \"hour\", \"minute\", \"second\"].indexOf(unit) >= 0;\n  const male = [\"ראשון\", \"שני\", \"שלישי\", \"רביעי\", \"חמישי\", \"שישי\", \"שביעי\", \"שמיני\", \"תשיעי\", \"עשירי\"];\n  const female = [\"ראשונה\", \"שנייה\", \"שלישית\", \"רביעית\", \"חמישית\", \"שישית\", \"שביעית\", \"שמינית\", \"תשיעית\", \"עשירית\"];\n  const index = number - 1;\n  return isFemale ? female[index] : male[index];\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}