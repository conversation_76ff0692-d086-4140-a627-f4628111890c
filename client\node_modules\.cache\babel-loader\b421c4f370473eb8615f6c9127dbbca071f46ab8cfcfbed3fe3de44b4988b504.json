{"ast": null, "code": "import { addQuarters } from \"./addQuarters.js\";\n\n/**\n * The {@link subQuarters} function options.\n */\n\n/**\n * @name subQuarters\n * @category Quarter Helpers\n * @summary Subtract the specified number of year quarters from the given date.\n *\n * @description\n * Subtract the specified number of year quarters from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of quarters to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the quarters subtracted\n *\n * @example\n * // Subtract 3 quarters from 1 September 2014:\n * const result = subQuarters(new Date(2014, 8, 1), 3)\n * //=> Sun Dec 01 2013 00:00:00\n */\nexport function subQuarters(date, amount, options) {\n  return addQuarters(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subQuarters;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}