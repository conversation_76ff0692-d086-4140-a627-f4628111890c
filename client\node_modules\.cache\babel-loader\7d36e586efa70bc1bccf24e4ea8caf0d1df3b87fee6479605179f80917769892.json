{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst accusativeWeekdays = [\"жексенбіде\", \"дүйсенбіде\", \"сейсенбіде\", \"сәрсенбіде\", \"бейсенбіде\", \"жұмада\", \"сенбіде\"];\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'өткен \" + weekday + \" сағат' p'-де'\";\n}\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'\" + weekday + \" сағат' p'-де'\";\n}\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  return \"'келесі \" + weekday + \" сағат' p'-де'\";\n}\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'кеше сағат' p'-де'\",\n  today: \"'бүгін сағат' p'-де'\",\n  tomorrow: \"'ертең сағат' p'-де'\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}