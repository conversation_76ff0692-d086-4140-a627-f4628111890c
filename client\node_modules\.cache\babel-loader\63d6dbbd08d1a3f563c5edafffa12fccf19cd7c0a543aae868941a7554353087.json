{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst numberValues = {\n  locale: {\n    1: \"१\",\n    2: \"२\",\n    3: \"३\",\n    4: \"४\",\n    5: \"५\",\n    6: \"६\",\n    7: \"७\",\n    8: \"८\",\n    9: \"९\",\n    0: \"०\"\n  },\n  number: {\n    \"१\": \"1\",\n    \"२\": \"2\",\n    \"३\": \"3\",\n    \"४\": \"4\",\n    \"५\": \"5\",\n    \"६\": \"6\",\n    \"७\": \"7\",\n    \"८\": \"8\",\n    \"९\": \"9\",\n    \"०\": \"0\"\n  }\n};\n\n// CLDR #1585 - #1592\nconst eraValues = {\n  narrow: [\"ईसा-पूर्व\", \"ईस्वी\"],\n  abbreviated: [\"ईसा-पूर्व\", \"ईस्वी\"],\n  wide: [\"ईसा-पूर्व\", \"ईसवी सन\"]\n};\n\n// CLDR #1593 - #1616\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ति1\", \"ति2\", \"ति3\", \"ति4\"],\n  wide: [\"पहली तिमाही\", \"दूसरी तिमाही\", \"तीसरी तिमाही\", \"चौथी तिमाही\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n// https://www.unicode.org/cldr/charts/32/summary/hi.html\n// CLDR #1617 - #1688\nconst monthValues = {\n  narrow: [\"ज\", \"फ़\", \"मा\", \"अ\", \"मई\", \"जू\", \"जु\", \"अग\", \"सि\", \"अक्टू\", \"न\", \"दि\"],\n  abbreviated: [\"जन\", \"फ़र\", \"मार्च\", \"अप्रैल\", \"मई\", \"जून\", \"जुल\", \"अग\", \"सित\", \"अक्टू\", \"नव\", \"दिस\"],\n  wide: [\"जनवरी\", \"फ़रवरी\", \"मार्च\", \"अप्रैल\", \"मई\", \"जून\", \"जुलाई\", \"अगस्त\", \"सितंबर\", \"अक्टूबर\", \"नवंबर\", \"दिसंबर\"]\n};\n\n// CLDR #1689 - #1744\nconst dayValues = {\n  narrow: [\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"],\n  short: [\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"],\n  abbreviated: [\"रवि\", \"सोम\", \"मंगल\", \"बुध\", \"गुरु\", \"शुक्र\", \"शनि\"],\n  wide: [\"रविवार\", \"सोमवार\", \"मंगलवार\", \"बुधवार\", \"गुरुवार\", \"शुक्रवार\", \"शनिवार\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  },\n  abbreviated: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  },\n  wide: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  },\n  abbreviated: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  },\n  wide: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return numberToLocale(number);\n};\nexport function localeToNumber(locale) {\n  const enNumber = locale.toString().replace(/[१२३४५६७८९०]/g, function (match) {\n    return numberValues.number[match];\n  });\n  return Number(enNumber);\n}\nexport function numberToLocale(enNumber) {\n  return enNumber.toString().replace(/\\d/g, function (match) {\n    return numberValues.locale[match];\n  });\n}\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}