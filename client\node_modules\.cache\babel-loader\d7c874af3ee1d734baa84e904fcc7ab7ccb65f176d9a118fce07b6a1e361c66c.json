{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"أقل من ثانية\",\n    two: \"أقل من ثانيتين\",\n    threeToTen: \"أقل من {{count}} ثواني\",\n    other: \"أقل من {{count}} ثانية\"\n  },\n  xSeconds: {\n    one: \"ثانية واحدة\",\n    two: \"ثانيتان\",\n    threeToTen: \"{{count}} ثواني\",\n    other: \"{{count}} ثانية\"\n  },\n  halfAMinute: \"نصف دقيقة\",\n  lessThanXMinutes: {\n    one: \"أقل من دقيقة\",\n    two: \"أقل من دقيقتين\",\n    threeToTen: \"أقل من {{count}} دقائق\",\n    other: \"أقل من {{count}} دقيقة\"\n  },\n  xMinutes: {\n    one: \"دقيقة واحدة\",\n    two: \"دقيقتان\",\n    threeToTen: \"{{count}} دقائق\",\n    other: \"{{count}} دقيقة\"\n  },\n  aboutXHours: {\n    one: \"ساعة واحدة تقريباً\",\n    two: \"ساعتين تقريبا\",\n    threeToTen: \"{{count}} ساعات تقريباً\",\n    other: \"{{count}} ساعة تقريباً\"\n  },\n  xHours: {\n    one: \"ساعة واحدة\",\n    two: \"ساعتان\",\n    threeToTen: \"{{count}} ساعات\",\n    other: \"{{count}} ساعة\"\n  },\n  xDays: {\n    one: \"يوم واحد\",\n    two: \"يومان\",\n    threeToTen: \"{{count}} أيام\",\n    other: \"{{count}} يوم\"\n  },\n  aboutXWeeks: {\n    one: \"أسبوع واحد تقريبا\",\n    two: \"أسبوعين تقريبا\",\n    threeToTen: \"{{count}} أسابيع تقريبا\",\n    other: \"{{count}} أسبوعا تقريبا\"\n  },\n  xWeeks: {\n    one: \"أسبوع واحد\",\n    two: \"أسبوعان\",\n    threeToTen: \"{{count}} أسابيع\",\n    other: \"{{count}} أسبوعا\"\n  },\n  aboutXMonths: {\n    one: \"شهر واحد تقريباً\",\n    two: \"شهرين تقريبا\",\n    threeToTen: \"{{count}} أشهر تقريبا\",\n    other: \"{{count}} شهرا تقريباً\"\n  },\n  xMonths: {\n    one: \"شهر واحد\",\n    two: \"شهران\",\n    threeToTen: \"{{count}} أشهر\",\n    other: \"{{count}} شهرا\"\n  },\n  aboutXYears: {\n    one: \"سنة واحدة تقريباً\",\n    two: \"سنتين تقريبا\",\n    threeToTen: \"{{count}} سنوات تقريباً\",\n    other: \"{{count}} سنة تقريباً\"\n  },\n  xYears: {\n    one: \"سنة واحد\",\n    two: \"سنتان\",\n    threeToTen: \"{{count}} سنوات\",\n    other: \"{{count}} سنة\"\n  },\n  overXYears: {\n    one: \"أكثر من سنة\",\n    two: \"أكثر من سنتين\",\n    threeToTen: \"أكثر من {{count}} سنوات\",\n    other: \"أكثر من {{count}} سنة\"\n  },\n  almostXYears: {\n    one: \"ما يقارب سنة واحدة\",\n    two: \"ما يقارب سنتين\",\n    threeToTen: \"ما يقارب {{count}} سنوات\",\n    other: \"ما يقارب {{count}} سنة\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const usageGroup = formatDistanceLocale[token];\n  let result;\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace(\"{{count}}\", String(count));\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"خلال \" + result;\n    } else {\n      return \"منذ \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}