{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mindre end ét sekund\",\n    other: \"mindre end {{count}} sekunder\"\n  },\n  xSeconds: {\n    one: \"1 sekund\",\n    other: \"{{count}} sekunder\"\n  },\n  halfAMinute: \"ét halvt minut\",\n  lessThanXMinutes: {\n    one: \"mindre end ét minut\",\n    other: \"mindre end {{count}} minutter\"\n  },\n  xMinutes: {\n    one: \"1 minut\",\n    other: \"{{count}} minutter\"\n  },\n  aboutXHours: {\n    one: \"cirka 1 time\",\n    other: \"cirka {{count}} timer\"\n  },\n  xHours: {\n    one: \"1 time\",\n    other: \"{{count}} timer\"\n  },\n  xDays: {\n    one: \"1 dag\",\n    other: \"{{count}} dage\"\n  },\n  aboutXWeeks: {\n    one: \"cirka 1 uge\",\n    other: \"cirka {{count}} uger\"\n  },\n  xWeeks: {\n    one: \"1 uge\",\n    other: \"{{count}} uger\"\n  },\n  aboutXMonths: {\n    one: \"cirka 1 måned\",\n    other: \"cirka {{count}} måneder\"\n  },\n  xMonths: {\n    one: \"1 måned\",\n    other: \"{{count}} måneder\"\n  },\n  aboutXYears: {\n    one: \"cirka 1 år\",\n    other: \"cirka {{count}} år\"\n  },\n  xYears: {\n    one: \"1 år\",\n    other: \"{{count}} år\"\n  },\n  overXYears: {\n    one: \"over 1 år\",\n    other: \"over {{count}} år\"\n  },\n  almostXYears: {\n    one: \"næsten 1 år\",\n    other: \"næsten {{count}} år\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"om \" + result;\n    } else {\n      return result + \" siden\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}