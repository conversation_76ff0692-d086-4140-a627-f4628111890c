{"ast": null, "code": "/**\n * WebSocket Service\n * Centralized Socket.IO connection management\n */\n\nimport io from 'socket.io-client';\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.eventListeners = new Map();\n  }\n\n  /**\n   * Initialize socket connection\n   * @param {string} token - JWT token for authentication\n   * @returns {Promise<Socket>} Socket instance\n   */\n  connect(token) {\n    if (this.socket && this.socket.connected) {\n      console.log('🔌 SOCKET: Already connected, returning existing connection');\n      return Promise.resolve(this.socket);\n    }\n    return new Promise((resolve, reject) => {\n      try {\n        // Get socket URL from environment\n        const socketUrl = process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000';\n        console.log('🔌 SOCKET: Connecting to:', socketUrl);\n\n        // Create socket connection with optimized settings\n        this.socket = io(socketUrl, {\n          auth: {\n            token\n          },\n          transports: ['websocket', 'polling'],\n          reconnection: true,\n          reconnectionDelay: 1000,\n          reconnectionDelayMax: 5000,\n          reconnectionAttempts: this.maxReconnectAttempts,\n          timeout: 20000,\n          forceNew: false,\n          // SSL settings for self-signed certificates\n          rejectUnauthorized: false,\n          secure: true,\n          upgrade: true,\n          rememberUpgrade: false\n        });\n\n        // Connection event handlers\n        this.socket.on('connect', () => {\n          console.log('✅ SOCKET: Connected successfully:', this.socket.id);\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n          resolve(this.socket);\n        });\n        this.socket.on('disconnect', reason => {\n          console.log('❌ SOCKET: Disconnected:', reason);\n          this.isConnected = false;\n\n          // Clear event listeners on disconnect\n          this.eventListeners.clear();\n        });\n        this.socket.on('connect_error', error => {\n          console.error('🔥 SOCKET: Connection error:', error);\n          this.isConnected = false;\n\n          // Handle specific error types\n          if (error.message.includes('jwt expired')) {\n            console.log('🔑 SOCKET: JWT expired, need to refresh token');\n            this.handleTokenExpired();\n          }\n          reject(error);\n        });\n        this.socket.on('reconnect', attemptNumber => {\n          console.log('🔄 SOCKET: Reconnected after', attemptNumber, 'attempts');\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n        });\n        this.socket.on('reconnect_error', error => {\n          console.error('🔥 SOCKET: Reconnection error:', error);\n          this.reconnectAttempts++;\n          if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.error('🚫 SOCKET: Max reconnection attempts reached');\n            this.disconnect();\n          }\n        });\n\n        // Setup heartbeat\n        this.setupHeartbeat();\n      } catch (error) {\n        console.error('💥 SOCKET: Failed to create connection:', error);\n        reject(error);\n      }\n    });\n  }\n\n  /**\n   * Setup heartbeat mechanism\n   */\n  setupHeartbeat() {\n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n    }\n    this.heartbeatInterval = setInterval(() => {\n      if (this.socket && this.socket.connected) {\n        this.socket.emit('ping', response => {\n          if (response === 'pong') {\n            console.log('💓 SOCKET: Heartbeat OK');\n          }\n        });\n      }\n    }, 30000); // 30 seconds\n  }\n\n  /**\n   * Handle token expiration\n   */\n  async handleTokenExpired() {\n    console.log('🔑 SOCKET: Handling token expiration...');\n    const refreshToken = localStorage.getItem('refreshToken');\n    if (refreshToken) {\n      try {\n        console.log('🔄 SOCKET: Attempting to refresh token...');\n\n        // Import axios dynamically to avoid circular dependency\n        const axios = (await import('axios')).default;\n        const refreshResponse = await axios.post('/api/auth/refresh-token', {\n          refreshToken\n        });\n        if (refreshResponse.data.accessToken) {\n          console.log('✅ SOCKET: Token refreshed successfully');\n          localStorage.setItem('accessToken', refreshResponse.data.accessToken);\n\n          // Reconnect with new token\n          this.disconnect();\n          setTimeout(() => {\n            this.connect(refreshResponse.data.accessToken);\n          }, 1000);\n          return;\n        }\n      } catch (refreshError) {\n        console.error('❌ SOCKET: Token refresh failed:', refreshError);\n      }\n    }\n\n    // If refresh failed or no refresh token, logout\n    console.log('🚪 SOCKET: Logging out user...');\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n\n    // Import toast dynamically\n    const toast = (await import('react-hot-toast')).default;\n    toast.error('Oturumunuz sona erdi, lütfen tekrar giriş yapın.');\n    window.location.href = '/login';\n  }\n\n  /**\n   * Disconnect socket\n   */\n  disconnect() {\n    if (this.socket) {\n      console.log('🔌 SOCKET: Disconnecting...');\n      this.socket.disconnect();\n      this.socket = null;\n    }\n    this.isConnected = false;\n    this.eventListeners.clear();\n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n      this.heartbeatInterval = null;\n    }\n  }\n\n  /**\n   * Add event listener\n   * @param {string} event - Event name\n   * @param {function} handler - Event handler\n   */\n  on(event, handler) {\n    if (this.socket) {\n      this.socket.on(event, handler);\n\n      // Store listener for cleanup\n      if (!this.eventListeners.has(event)) {\n        this.eventListeners.set(event, []);\n      }\n      this.eventListeners.get(event).push(handler);\n    }\n  }\n\n  /**\n   * Remove event listener\n   * @param {string} event - Event name\n   * @param {function} handler - Event handler\n   */\n  off(event, handler) {\n    if (this.socket) {\n      this.socket.off(event, handler);\n\n      // Remove from stored listeners\n      if (this.eventListeners.has(event)) {\n        const handlers = this.eventListeners.get(event);\n        const index = handlers.indexOf(handler);\n        if (index > -1) {\n          handlers.splice(index, 1);\n        }\n      }\n    }\n  }\n\n  /**\n   * Emit event\n   * @param {string} event - Event name\n   * @param {*} data - Event data\n   */\n  emit(event, data) {\n    if (this.socket && this.socket.connected) {\n      this.socket.emit(event, data);\n    } else {\n      console.warn('⚠️ SOCKET: Cannot emit, not connected');\n    }\n  }\n\n  /**\n   * Join room\n   * @param {string} room - Room name\n   */\n  joinRoom(room) {\n    if (this.socket && this.socket.connected) {\n      this.socket.emit('join_conversation', room);\n      console.log('🏠 SOCKET: Joined room:', room);\n    }\n  }\n\n  /**\n   * Leave room\n   * @param {string} room - Room name\n   */\n  leaveRoom(room) {\n    if (this.socket && this.socket.connected) {\n      this.socket.emit('leave_conversation', room);\n      console.log('🏠 SOCKET: Left room:', room);\n    }\n  }\n\n  /**\n   * Get connection status\n   * @returns {boolean} Connection status\n   */\n  isSocketConnected() {\n    return this.isConnected && this.socket && this.socket.connected;\n  }\n\n  /**\n   * Get socket instance\n   * @returns {Socket|null} Socket instance\n   */\n  getSocket() {\n    return this.socket;\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\nexport default socketService;", "map": {"version": 3, "names": ["io", "SocketService", "constructor", "socket", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "eventListeners", "Map", "connect", "token", "connected", "console", "log", "Promise", "resolve", "reject", "socketUrl", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionDelayMax", "reconnectionAttempts", "timeout", "forceNew", "rejectUnauthorized", "secure", "upgrade", "rememberUpgrade", "on", "id", "reason", "clear", "error", "message", "includes", "handleTokenExpired", "attemptNumber", "disconnect", "setupHeartbeat", "heartbeatInterval", "clearInterval", "setInterval", "emit", "response", "refreshToken", "localStorage", "getItem", "axios", "default", "refreshResponse", "post", "data", "accessToken", "setItem", "setTimeout", "refreshError", "removeItem", "toast", "window", "location", "href", "event", "handler", "has", "set", "get", "push", "off", "handlers", "index", "indexOf", "splice", "warn", "joinRoom", "room", "leaveRoom", "isSocketConnected", "getSocket", "socketService"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/services/socketService.js"], "sourcesContent": ["/**\n * WebSocket Service\n * Centralized Socket.IO connection management\n */\n\nimport io from 'socket.io-client';\n\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.eventListeners = new Map();\n  }\n\n  /**\n   * Initialize socket connection\n   * @param {string} token - JWT token for authentication\n   * @returns {Promise<Socket>} Socket instance\n   */\n  connect(token) {\n    if (this.socket && this.socket.connected) {\n      console.log('🔌 SOCKET: Already connected, returning existing connection');\n      return Promise.resolve(this.socket);\n    }\n\n    return new Promise((resolve, reject) => {\n      try {\n        // Get socket URL from environment\n        const socketUrl = process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000';\n        console.log('🔌 SOCKET: Connecting to:', socketUrl);\n\n        // Create socket connection with optimized settings\n        this.socket = io(socketUrl, {\n          auth: { token },\n          transports: ['websocket', 'polling'],\n          reconnection: true,\n          reconnectionDelay: 1000,\n          reconnectionDelayMax: 5000,\n          reconnectionAttempts: this.maxReconnectAttempts,\n          timeout: 20000,\n          forceNew: false,\n          // SSL settings for self-signed certificates\n          rejectUnauthorized: false,\n          secure: true,\n          upgrade: true,\n          rememberUpgrade: false\n        });\n\n        // Connection event handlers\n        this.socket.on('connect', () => {\n          console.log('✅ SOCKET: Connected successfully:', this.socket.id);\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n          resolve(this.socket);\n        });\n\n        this.socket.on('disconnect', (reason) => {\n          console.log('❌ SOCKET: Disconnected:', reason);\n          this.isConnected = false;\n          \n          // Clear event listeners on disconnect\n          this.eventListeners.clear();\n        });\n\n        this.socket.on('connect_error', (error) => {\n          console.error('🔥 SOCKET: Connection error:', error);\n          this.isConnected = false;\n          \n          // Handle specific error types\n          if (error.message.includes('jwt expired')) {\n            console.log('🔑 SOCKET: JWT expired, need to refresh token');\n            this.handleTokenExpired();\n          }\n          \n          reject(error);\n        });\n\n        this.socket.on('reconnect', (attemptNumber) => {\n          console.log('🔄 SOCKET: Reconnected after', attemptNumber, 'attempts');\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n        });\n\n        this.socket.on('reconnect_error', (error) => {\n          console.error('🔥 SOCKET: Reconnection error:', error);\n          this.reconnectAttempts++;\n          \n          if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n            console.error('🚫 SOCKET: Max reconnection attempts reached');\n            this.disconnect();\n          }\n        });\n\n        // Setup heartbeat\n        this.setupHeartbeat();\n\n      } catch (error) {\n        console.error('💥 SOCKET: Failed to create connection:', error);\n        reject(error);\n      }\n    });\n  }\n\n  /**\n   * Setup heartbeat mechanism\n   */\n  setupHeartbeat() {\n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n    }\n\n    this.heartbeatInterval = setInterval(() => {\n      if (this.socket && this.socket.connected) {\n        this.socket.emit('ping', (response) => {\n          if (response === 'pong') {\n            console.log('💓 SOCKET: Heartbeat OK');\n          }\n        });\n      }\n    }, 30000); // 30 seconds\n  }\n\n  /**\n   * Handle token expiration\n   */\n  async handleTokenExpired() {\n    console.log('🔑 SOCKET: Handling token expiration...');\n\n    const refreshToken = localStorage.getItem('refreshToken');\n\n    if (refreshToken) {\n      try {\n        console.log('🔄 SOCKET: Attempting to refresh token...');\n\n        // Import axios dynamically to avoid circular dependency\n        const axios = (await import('axios')).default;\n\n        const refreshResponse = await axios.post('/api/auth/refresh-token', {\n          refreshToken\n        });\n\n        if (refreshResponse.data.accessToken) {\n          console.log('✅ SOCKET: Token refreshed successfully');\n          localStorage.setItem('accessToken', refreshResponse.data.accessToken);\n\n          // Reconnect with new token\n          this.disconnect();\n          setTimeout(() => {\n            this.connect(refreshResponse.data.accessToken);\n          }, 1000);\n\n          return;\n        }\n      } catch (refreshError) {\n        console.error('❌ SOCKET: Token refresh failed:', refreshError);\n      }\n    }\n\n    // If refresh failed or no refresh token, logout\n    console.log('🚪 SOCKET: Logging out user...');\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n\n    // Import toast dynamically\n    const toast = (await import('react-hot-toast')).default;\n    toast.error('Oturumunuz sona erdi, lütfen tekrar giriş yapın.');\n\n    window.location.href = '/login';\n  }\n\n  /**\n   * Disconnect socket\n   */\n  disconnect() {\n    if (this.socket) {\n      console.log('🔌 SOCKET: Disconnecting...');\n      this.socket.disconnect();\n      this.socket = null;\n    }\n    \n    this.isConnected = false;\n    this.eventListeners.clear();\n    \n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n      this.heartbeatInterval = null;\n    }\n  }\n\n  /**\n   * Add event listener\n   * @param {string} event - Event name\n   * @param {function} handler - Event handler\n   */\n  on(event, handler) {\n    if (this.socket) {\n      this.socket.on(event, handler);\n      \n      // Store listener for cleanup\n      if (!this.eventListeners.has(event)) {\n        this.eventListeners.set(event, []);\n      }\n      this.eventListeners.get(event).push(handler);\n    }\n  }\n\n  /**\n   * Remove event listener\n   * @param {string} event - Event name\n   * @param {function} handler - Event handler\n   */\n  off(event, handler) {\n    if (this.socket) {\n      this.socket.off(event, handler);\n      \n      // Remove from stored listeners\n      if (this.eventListeners.has(event)) {\n        const handlers = this.eventListeners.get(event);\n        const index = handlers.indexOf(handler);\n        if (index > -1) {\n          handlers.splice(index, 1);\n        }\n      }\n    }\n  }\n\n  /**\n   * Emit event\n   * @param {string} event - Event name\n   * @param {*} data - Event data\n   */\n  emit(event, data) {\n    if (this.socket && this.socket.connected) {\n      this.socket.emit(event, data);\n    } else {\n      console.warn('⚠️ SOCKET: Cannot emit, not connected');\n    }\n  }\n\n  /**\n   * Join room\n   * @param {string} room - Room name\n   */\n  joinRoom(room) {\n    if (this.socket && this.socket.connected) {\n      this.socket.emit('join_conversation', room);\n      console.log('🏠 SOCKET: Joined room:', room);\n    }\n  }\n\n  /**\n   * Leave room\n   * @param {string} room - Room name\n   */\n  leaveRoom(room) {\n    if (this.socket && this.socket.connected) {\n      this.socket.emit('leave_conversation', room);\n      console.log('🏠 SOCKET: Left room:', room);\n    }\n  }\n\n  /**\n   * Get connection status\n   * @returns {boolean} Connection status\n   */\n  isSocketConnected() {\n    return this.isConnected && this.socket && this.socket.connected;\n  }\n\n  /**\n   * Get socket instance\n   * @returns {Socket|null} Socket instance\n   */\n  getSocket() {\n    return this.socket;\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\n\nexport default socketService;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,EAAE,MAAM,kBAAkB;AAEjC,MAAMC,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;EACEC,OAAOA,CAACC,KAAK,EAAE;IACb,IAAI,IAAI,CAACP,MAAM,IAAI,IAAI,CAACA,MAAM,CAACQ,SAAS,EAAE;MACxCC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1E,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACZ,MAAM,CAAC;IACrC;IAEA,OAAO,IAAIW,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;QACF;QACA,MAAMC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,4BAA4B;QAC/ER,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,SAAS,CAAC;;QAEnD;QACA,IAAI,CAACd,MAAM,GAAGH,EAAE,CAACiB,SAAS,EAAE;UAC1BI,IAAI,EAAE;YAAEX;UAAM,CAAC;UACfY,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;UACpCC,YAAY,EAAE,IAAI;UAClBC,iBAAiB,EAAE,IAAI;UACvBC,oBAAoB,EAAE,IAAI;UAC1BC,oBAAoB,EAAE,IAAI,CAACpB,oBAAoB;UAC/CqB,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE,KAAK;UACf;UACAC,kBAAkB,EAAE,KAAK;UACzBC,MAAM,EAAE,IAAI;UACZC,OAAO,EAAE,IAAI;UACbC,eAAe,EAAE;QACnB,CAAC,CAAC;;QAEF;QACA,IAAI,CAAC7B,MAAM,CAAC8B,EAAE,CAAC,SAAS,EAAE,MAAM;UAC9BrB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACV,MAAM,CAAC+B,EAAE,CAAC;UAChE,IAAI,CAAC9B,WAAW,GAAG,IAAI;UACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;UAC1BU,OAAO,CAAC,IAAI,CAACZ,MAAM,CAAC;QACtB,CAAC,CAAC;QAEF,IAAI,CAACA,MAAM,CAAC8B,EAAE,CAAC,YAAY,EAAGE,MAAM,IAAK;UACvCvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsB,MAAM,CAAC;UAC9C,IAAI,CAAC/B,WAAW,GAAG,KAAK;;UAExB;UACA,IAAI,CAACG,cAAc,CAAC6B,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC;QAEF,IAAI,CAACjC,MAAM,CAAC8B,EAAE,CAAC,eAAe,EAAGI,KAAK,IAAK;UACzCzB,OAAO,CAACyB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpD,IAAI,CAACjC,WAAW,GAAG,KAAK;;UAExB;UACA,IAAIiC,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;YACzC3B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;YAC5D,IAAI,CAAC2B,kBAAkB,CAAC,CAAC;UAC3B;UAEAxB,MAAM,CAACqB,KAAK,CAAC;QACf,CAAC,CAAC;QAEF,IAAI,CAAClC,MAAM,CAAC8B,EAAE,CAAC,WAAW,EAAGQ,aAAa,IAAK;UAC7C7B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE4B,aAAa,EAAE,UAAU,CAAC;UACtE,IAAI,CAACrC,WAAW,GAAG,IAAI;UACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;QAC5B,CAAC,CAAC;QAEF,IAAI,CAACF,MAAM,CAAC8B,EAAE,CAAC,iBAAiB,EAAGI,KAAK,IAAK;UAC3CzB,OAAO,CAACyB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtD,IAAI,CAAChC,iBAAiB,EAAE;UAExB,IAAI,IAAI,CAACA,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,EAAE;YACvDM,OAAO,CAACyB,KAAK,CAAC,8CAA8C,CAAC;YAC7D,IAAI,CAACK,UAAU,CAAC,CAAC;UACnB;QACF,CAAC,CAAC;;QAEF;QACA,IAAI,CAACC,cAAc,CAAC,CAAC;MAEvB,CAAC,CAAC,OAAON,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/DrB,MAAM,CAACqB,KAAK,CAAC;MACf;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEM,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;IACvC;IAEA,IAAI,CAACA,iBAAiB,GAAGE,WAAW,CAAC,MAAM;MACzC,IAAI,IAAI,CAAC3C,MAAM,IAAI,IAAI,CAACA,MAAM,CAACQ,SAAS,EAAE;QACxC,IAAI,CAACR,MAAM,CAAC4C,IAAI,CAAC,MAAM,EAAGC,QAAQ,IAAK;UACrC,IAAIA,QAAQ,KAAK,MAAM,EAAE;YACvBpC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;UACxC;QACF,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACb;;EAEA;AACF;AACA;EACE,MAAM2B,kBAAkBA,CAAA,EAAG;IACzB5B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD,MAAMoC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAEzD,IAAIF,YAAY,EAAE;MAChB,IAAI;QACFrC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;QAExD;QACA,MAAMuC,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,EAAEC,OAAO;QAE7C,MAAMC,eAAe,GAAG,MAAMF,KAAK,CAACG,IAAI,CAAC,yBAAyB,EAAE;UAClEN;QACF,CAAC,CAAC;QAEF,IAAIK,eAAe,CAACE,IAAI,CAACC,WAAW,EAAE;UACpC7C,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrDqC,YAAY,CAACQ,OAAO,CAAC,aAAa,EAAEJ,eAAe,CAACE,IAAI,CAACC,WAAW,CAAC;;UAErE;UACA,IAAI,CAACf,UAAU,CAAC,CAAC;UACjBiB,UAAU,CAAC,MAAM;YACf,IAAI,CAAClD,OAAO,CAAC6C,eAAe,CAACE,IAAI,CAACC,WAAW,CAAC;UAChD,CAAC,EAAE,IAAI,CAAC;UAER;QACF;MACF,CAAC,CAAC,OAAOG,YAAY,EAAE;QACrBhD,OAAO,CAACyB,KAAK,CAAC,iCAAiC,EAAEuB,YAAY,CAAC;MAChE;IACF;;IAEA;IACAhD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7CqC,YAAY,CAACW,UAAU,CAAC,aAAa,CAAC;IACtCX,YAAY,CAACW,UAAU,CAAC,cAAc,CAAC;;IAEvC;IACA,MAAMC,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC,iBAAiB,CAAC,EAAET,OAAO;IACvDS,KAAK,CAACzB,KAAK,CAAC,kDAAkD,CAAC;IAE/D0B,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;;EAEA;AACF;AACA;EACEvB,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACvC,MAAM,EAAE;MACfS,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,IAAI,CAACV,MAAM,CAACuC,UAAU,CAAC,CAAC;MACxB,IAAI,CAACvC,MAAM,GAAG,IAAI;IACpB;IAEA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACG,cAAc,CAAC6B,KAAK,CAAC,CAAC;IAE3B,IAAI,IAAI,CAACQ,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;IAC/B;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEX,EAAEA,CAACiC,KAAK,EAAEC,OAAO,EAAE;IACjB,IAAI,IAAI,CAAChE,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,EAAE,CAACiC,KAAK,EAAEC,OAAO,CAAC;;MAE9B;MACA,IAAI,CAAC,IAAI,CAAC5D,cAAc,CAAC6D,GAAG,CAACF,KAAK,CAAC,EAAE;QACnC,IAAI,CAAC3D,cAAc,CAAC8D,GAAG,CAACH,KAAK,EAAE,EAAE,CAAC;MACpC;MACA,IAAI,CAAC3D,cAAc,CAAC+D,GAAG,CAACJ,KAAK,CAAC,CAACK,IAAI,CAACJ,OAAO,CAAC;IAC9C;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEK,GAAGA,CAACN,KAAK,EAAEC,OAAO,EAAE;IAClB,IAAI,IAAI,CAAChE,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACqE,GAAG,CAACN,KAAK,EAAEC,OAAO,CAAC;;MAE/B;MACA,IAAI,IAAI,CAAC5D,cAAc,CAAC6D,GAAG,CAACF,KAAK,CAAC,EAAE;QAClC,MAAMO,QAAQ,GAAG,IAAI,CAAClE,cAAc,CAAC+D,GAAG,CAACJ,KAAK,CAAC;QAC/C,MAAMQ,KAAK,GAAGD,QAAQ,CAACE,OAAO,CAACR,OAAO,CAAC;QACvC,IAAIO,KAAK,GAAG,CAAC,CAAC,EAAE;UACdD,QAAQ,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAC3B;MACF;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE3B,IAAIA,CAACmB,KAAK,EAAEV,IAAI,EAAE;IAChB,IAAI,IAAI,CAACrD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACQ,SAAS,EAAE;MACxC,IAAI,CAACR,MAAM,CAAC4C,IAAI,CAACmB,KAAK,EAAEV,IAAI,CAAC;IAC/B,CAAC,MAAM;MACL5C,OAAO,CAACiE,IAAI,CAAC,uCAAuC,CAAC;IACvD;EACF;;EAEA;AACF;AACA;AACA;EACEC,QAAQA,CAACC,IAAI,EAAE;IACb,IAAI,IAAI,CAAC5E,MAAM,IAAI,IAAI,CAACA,MAAM,CAACQ,SAAS,EAAE;MACxC,IAAI,CAACR,MAAM,CAAC4C,IAAI,CAAC,mBAAmB,EAAEgC,IAAI,CAAC;MAC3CnE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkE,IAAI,CAAC;IAC9C;EACF;;EAEA;AACF;AACA;AACA;EACEC,SAASA,CAACD,IAAI,EAAE;IACd,IAAI,IAAI,CAAC5E,MAAM,IAAI,IAAI,CAACA,MAAM,CAACQ,SAAS,EAAE;MACxC,IAAI,CAACR,MAAM,CAAC4C,IAAI,CAAC,oBAAoB,EAAEgC,IAAI,CAAC;MAC5CnE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEkE,IAAI,CAAC;IAC5C;EACF;;EAEA;AACF;AACA;AACA;EACEE,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7E,WAAW,IAAI,IAAI,CAACD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACQ,SAAS;EACjE;;EAEA;AACF;AACA;AACA;EACEuE,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/E,MAAM;EACpB;AACF;;AAEA;AACA,MAAMgF,aAAa,GAAG,IAAIlF,aAAa,CAAC,CAAC;AAEzC,eAAekF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}