{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^(\\d+)(th|st|nd|rd)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(ម\\.)?គស/i,\n  abbreviated: /^(មុន)?គ\\.ស/i,\n  wide: /^(មុន|នៃ)គ្រិស្តសករាជ/i\n};\nconst parseEraPatterns = {\n  any: [/^(ម|មុន)គ\\.?ស/i, /^(នៃ)?គ\\.?ស/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^(ត្រីមាស)(ទី)?\\s?[1234]/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^(ម\\.ក|ក\\.ម|មិ|ម\\.ស|ឧ\\.ស|ម\\.ថ|ក\\.ដ|សី|កញ|តុ|វិ|ធ)/i,\n  abbreviated: /^(មករា|កុម្ភៈ|មីនា|មេសា|ឧសភា|មិថុនា|កក្កដា|សីហា|កញ្ញា|តុលា|វិច្ឆិកា|ធ្នូ)/i,\n  wide: /^(មករា|កុម្ភៈ|មីនា|មេសា|ឧសភា|មិថុនា|កក្កដា|សីហា|កញ្ញា|តុលា|វិច្ឆិកា|ធ្នូ)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^ម\\.ក/i, /^ក\\.ម/i, /^មិ/i, /^ម\\.ស/i, /^ឧ\\.ស/i, /^ម\\.ថ/i, /^ក\\.ដ/i, /^សី/i, /^កញ/i, /^តុ/i, /^វិ/i, /^ធ/i],\n  any: [/^មក/i, /^កុ/i, /^មីន/i, /^មេ/i, /^ឧស/i, /^មិថ/i, /^កក/i, /^សី/i, /^កញ/i, /^តុ/i, /^វិច/i, /^ធ/i]\n};\nconst matchDayPatterns = {\n  narrow: /^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,\n  short: /^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,\n  abbreviated: /^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,\n  wide: /^(អាទិត្យ|ចន្ទ|អង្គារ|ពុធ|ព្រហស្បតិ៍|សុក្រ|សៅរ៍)/i\n};\nconst parseDayPatterns = {\n  narrow: [/^អា/i, /^ច/i, /^អ/i, /^ព/i, /^ព្រ/i, /^សុ/i, /^ស/i],\n  any: [/^អា/i, /^ច/i, /^អ/i, /^ព/i, /^ព្រ/i, /^សុ/i, /^សៅ/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(ព្រឹក|ល្ងាច|ពេលព្រឹក|ពេលថ្ងៃត្រង់|ពេលល្ងាច|ពេលរសៀល|ពេលយប់|ពេលកណ្ដាលអធ្រាត្រ)/i,\n  any: /^(ព្រឹក|ល្ងាច|ពេលព្រឹក|ពេលថ្ងៃត្រង់|ពេលល្ងាច|ពេលរសៀល|ពេលយប់|ពេលកណ្ដាលអធ្រាត្រ)/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ព្រឹក/i,\n    pm: /^ល្ងាច/i,\n    midnight: /^ពេលកណ្ដាលអធ្រាត្រ/i,\n    noon: /^ពេលថ្ងៃត្រង់/i,\n    morning: /ពេលព្រឹក/i,\n    afternoon: /ពេលរសៀល/i,\n    evening: /ពេលល្ងាច/i,\n    night: /ពេលយប់/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}