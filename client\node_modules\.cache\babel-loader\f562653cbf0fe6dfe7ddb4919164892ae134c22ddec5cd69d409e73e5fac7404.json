{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'o' eeee 'pasado á' LT\",\n  yesterday: \"'onte á' p\",\n  today: \"'hoxe á' p\",\n  tomorrow: \"'mañá á' p\",\n  nextWeek: \"eeee 'á' p\",\n  other: \"P\"\n};\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'o' eeee 'pasado ás' p\",\n  yesterday: \"'onte ás' p\",\n  today: \"'hoxe ás' p\",\n  tomorrow: \"'mañá ás' p\",\n  nextWeek: \"eeee 'ás' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}