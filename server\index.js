/**
 * Server Entry Point
 * Initializes the application
 */

// Load environment variables
require('dotenv').config();

const http = require('http');
const https = require('https');
const app = require('./app');
const { connectDB } = require('./config/db');
const logger = require('./utils/logger');
const fs = require('fs');
const path = require('path');
const { initializeSocket } = require('./socket/socketHandler');
const appointmentReminderService = require('./services/appointment-reminder.service');

// Get port from environment variable or use default
const PORT = process.env.PORT || 5000;

// SSL options for HTTPS
const sslOptions = {
  key: fs.readFileSync(path.join(__dirname, '../ssl/server-key.pem')),
  cert: fs.readFileSync(path.join(__dirname, '../ssl/server.pem'))
};

// Create HTTPS server
const server = https.createServer(sslOptions, app);

// Initialize Socket.IO
const io = initializeSocket(server);

// Make io available to routes
app.set('io', io);

// Make io globally available for services
global.io = io;

// Start server
const startServer = async () => {
  try {
    console.log('Starting server...');
    
    // Ensure logs directory exists
    const logsDir = path.join(__dirname, './logs');
    if (!fs.existsSync(logsDir)) {
      console.log('Creating logs directory...');
      fs.mkdirSync(logsDir);
    }
    
    // Connect to database
    console.log('Connecting to database...');
    await connectDB();
    console.log('Database connection successful');
    
    // Start HTTPS server
    server.listen(PORT, '0.0.0.0', () => {
      console.log(`🔒 HTTPS Server running on https://*************:${PORT}`);
      console.log(`🔒 Also accessible via https://localhost:${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`HTTPS Server running on https://*************:${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);

      // Start appointment reminder service
      try {
        appointmentReminderService.start();
        logger.info('Appointment reminder service started successfully');
      } catch (reminderError) {
        logger.error('Failed to start appointment reminder service:', reminderError);
        // Don't crash the server if reminder service fails
      }
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Rejection:', err);
  logger.error('Unhandled Rejection:', err);
  // Close server and exit
  server.close(() => process.exit(1));
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

// Start server
console.log('Initializing server...');
startServer();
