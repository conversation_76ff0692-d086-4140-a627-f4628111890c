{"ast": null, "code": "/**\n * Socket URL Utility\n * Dinamik olarak doğru socket URL'sini belirler\n */\n\n/**\n * Doğru socket URL'sini belirler\n * @returns {string} Socket URL\n */\nexport const getSocketUrl = () => {\n  // Environment'tan URL'yi al\n  const envUrl = process.env.REACT_APP_API_URL || process.env.REACT_APP_SOCKET_URL;\n  if (envUrl) {\n    return envUrl;\n  }\n\n  // Fallback: Mevcut hostname'e göre belirle\n  const hostname = window.location.hostname;\n  const protocol = window.location.protocol === 'https:' ? 'https:' : 'https:';\n  const port = '5000';\n\n  // Localhost veya 127.0.0.1 ise localhost kullan\n  if (hostname === 'localhost' || hostname === '127.0.0.1') {\n    return `${protocol}//localhost:${port}`;\n  }\n\n  // IP adresi ise o IP'yi kullan\n  if (hostname.match(/^\\d+\\.\\d+\\.\\d+\\.\\d+$/)) {\n    return `${protocol}//${hostname}:${port}`;\n  }\n\n  // Fallback IP\n  const fallbackIp = process.env.REACT_APP_FALLBACK_IP || '*************';\n  return `${protocol}//${fallbackIp}:${port}`;\n};\n\n/**\n * Socket.IO için optimize edilmiş konfigürasyon\n * @param {string} token - JWT token\n * @returns {object} Socket.IO konfigürasyonu\n */\nexport const getSocketConfig = token => {\n  return {\n    auth: {\n      token\n    },\n    transports: ['websocket', 'polling'],\n    reconnection: true,\n    reconnectionDelay: 1000,\n    reconnectionAttempts: 5,\n    timeout: 20000,\n    rejectUnauthorized: false,\n    // SSL sertifika kontrolünü bypass et\n    secure: true,\n    forceNew: false,\n    upgrade: true,\n    rememberUpgrade: false\n  };\n};", "map": {"version": 3, "names": ["getSocketUrl", "envUrl", "process", "env", "REACT_APP_API_URL", "REACT_APP_SOCKET_URL", "hostname", "window", "location", "protocol", "port", "match", "fallbackIp", "REACT_APP_FALLBACK_IP", "getSocketConfig", "token", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "rejectUnauthorized", "secure", "forceNew", "upgrade", "rememberUpgrade"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/utils/socketUrl.js"], "sourcesContent": ["/**\n * Socket URL Utility\n * Dinamik olarak doğru socket URL'sini belirler\n */\n\n/**\n * Doğru socket URL'sini belirler\n * @returns {string} Socket URL\n */\nexport const getSocketUrl = () => {\n  // Environment'tan URL'yi al\n  const envUrl = process.env.REACT_APP_API_URL || process.env.REACT_APP_SOCKET_URL;\n  \n  if (envUrl) {\n    return envUrl;\n  }\n  \n  // Fallback: Mevcut hostname'e göre belirle\n  const hostname = window.location.hostname;\n  const protocol = window.location.protocol === 'https:' ? 'https:' : 'https:';\n  const port = '5000';\n  \n  // Localhost veya 127.0.0.1 ise localhost kullan\n  if (hostname === 'localhost' || hostname === '127.0.0.1') {\n    return `${protocol}//localhost:${port}`;\n  }\n  \n  // IP adresi ise o IP'yi kullan\n  if (hostname.match(/^\\d+\\.\\d+\\.\\d+\\.\\d+$/)) {\n    return `${protocol}//${hostname}:${port}`;\n  }\n  \n  // Fallback IP\n  const fallbackIp = process.env.REACT_APP_FALLBACK_IP || '*************';\n  return `${protocol}//${fallbackIp}:${port}`;\n};\n\n/**\n * Socket.IO için optimize edilmiş konfigürasyon\n * @param {string} token - JWT token\n * @returns {object} Socket.IO konfigürasyonu\n */\nexport const getSocketConfig = (token) => {\n  return {\n    auth: { token },\n    transports: ['websocket', 'polling'],\n    reconnection: true,\n    reconnectionDelay: 1000,\n    reconnectionAttempts: 5,\n    timeout: 20000,\n    rejectUnauthorized: false, // SSL sertifika kontrolünü bypass et\n    secure: true,\n    forceNew: false,\n    upgrade: true,\n    rememberUpgrade: false\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,GAAGA,CAAA,KAAM;EAChC;EACA,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAIF,OAAO,CAACC,GAAG,CAACE,oBAAoB;EAEhF,IAAIJ,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;;EAEA;EACA,MAAMK,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACF,QAAQ;EACzC,MAAMG,QAAQ,GAAGF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ;EAC5E,MAAMC,IAAI,GAAG,MAAM;;EAEnB;EACA,IAAIJ,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,WAAW,EAAE;IACxD,OAAO,GAAGG,QAAQ,eAAeC,IAAI,EAAE;EACzC;;EAEA;EACA,IAAIJ,QAAQ,CAACK,KAAK,CAAC,sBAAsB,CAAC,EAAE;IAC1C,OAAO,GAAGF,QAAQ,KAAKH,QAAQ,IAAII,IAAI,EAAE;EAC3C;;EAEA;EACA,MAAME,UAAU,GAAGV,OAAO,CAACC,GAAG,CAACU,qBAAqB,IAAI,eAAe;EACvE,OAAO,GAAGJ,QAAQ,KAAKG,UAAU,IAAIF,IAAI,EAAE;AAC7C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,eAAe,GAAIC,KAAK,IAAK;EACxC,OAAO;IACLC,IAAI,EAAE;MAAED;IAAM,CAAC;IACfE,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;IACpCC,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAE,IAAI;IACvBC,oBAAoB,EAAE,CAAC;IACvBC,OAAO,EAAE,KAAK;IACdC,kBAAkB,EAAE,KAAK;IAAE;IAC3BC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,IAAI;IACbC,eAAe,EAAE;EACnB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}