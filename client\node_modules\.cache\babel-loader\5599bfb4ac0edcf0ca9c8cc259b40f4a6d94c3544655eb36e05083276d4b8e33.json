{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"前\", \"公元\"],\n  abbreviated: [\"前\", \"公元\"],\n  wide: [\"公元前\", \"公元\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"第一季\", \"第二季\", \"第三季\", \"第四季\"],\n  wide: [\"第一季度\", \"第二季度\", \"第三季度\", \"第四季度\"]\n};\nconst monthValues = {\n  narrow: [\"一\", \"二\", \"三\", \"四\", \"五\", \"六\", \"七\", \"八\", \"九\", \"十\", \"十一\", \"十二\"],\n  abbreviated: [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\", \"6月\", \"7月\", \"8月\", \"9月\", \"10月\", \"11月\", \"12月\"],\n  wide: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"]\n};\nconst dayValues = {\n  narrow: [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"],\n  short: [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"],\n  abbreviated: [\"週日\", \"週一\", \"週二\", \"週三\", \"週四\", \"週五\", \"週六\"],\n  wide: [\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"上\",\n    pm: \"下\",\n    midnight: \"午夜\",\n    noon: \"晌\",\n    morning: \"早\",\n    afternoon: \"午\",\n    evening: \"晚\",\n    night: \"夜\"\n  },\n  abbreviated: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"午夜\",\n    noon: \"中午\",\n    morning: \"上午\",\n    afternoon: \"下午\",\n    evening: \"晚上\",\n    night: \"夜晚\"\n  },\n  wide: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"午夜\",\n    noon: \"中午\",\n    morning: \"上午\",\n    afternoon: \"下午\",\n    evening: \"晚上\",\n    night: \"夜晚\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"上\",\n    pm: \"下\",\n    midnight: \"午夜\",\n    noon: \"晌\",\n    morning: \"早\",\n    afternoon: \"午\",\n    evening: \"晚\",\n    night: \"夜\"\n  },\n  abbreviated: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"午夜\",\n    noon: \"中午\",\n    morning: \"上午\",\n    afternoon: \"下午\",\n    evening: \"晚上\",\n    night: \"夜晚\"\n  },\n  wide: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"午夜\",\n    noon: \"中午\",\n    morning: \"上午\",\n    afternoon: \"下午\",\n    evening: \"晚上\",\n    night: \"夜晚\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  switch (options?.unit) {\n    case \"date\":\n      return number + \"日\";\n    case \"hour\":\n      return number + \"時\";\n    case \"minute\":\n      return number + \"分\";\n    case \"second\":\n      return number + \"秒\";\n    default:\n      return \"第 \" + number;\n  }\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}