{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"до н.е.\", \"н.е.\"],\n  abbreviated: [\"до н. е.\", \"н. е.\"],\n  wide: [\"до нашої ери\", \"нашої ери\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-й кв.\", \"2-й кв.\", \"3-й кв.\", \"4-й кв.\"],\n  wide: [\"1-й квартал\", \"2-й квартал\", \"3-й квартал\", \"4-й квартал\"]\n};\nconst monthValues = {\n  // ДСТУ 3582:2013\n  narrow: [\"С\", \"Л\", \"Б\", \"К\", \"Т\", \"Ч\", \"Л\", \"С\", \"В\", \"Ж\", \"Л\", \"Г\"],\n  abbreviated: [\"січ.\", \"лют.\", \"берез.\", \"квіт.\", \"трав.\", \"черв.\", \"лип.\", \"серп.\", \"верес.\", \"жовт.\", \"листоп.\", \"груд.\"],\n  wide: [\"січень\", \"лютий\", \"березень\", \"квітень\", \"травень\", \"червень\", \"липень\", \"серпень\", \"вересень\", \"жовтень\", \"листопад\", \"грудень\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"С\", \"Л\", \"Б\", \"К\", \"Т\", \"Ч\", \"Л\", \"С\", \"В\", \"Ж\", \"Л\", \"Г\"],\n  abbreviated: [\"січ.\", \"лют.\", \"берез.\", \"квіт.\", \"трав.\", \"черв.\", \"лип.\", \"серп.\", \"верес.\", \"жовт.\", \"листоп.\", \"груд.\"],\n  wide: [\"січня\", \"лютого\", \"березня\", \"квітня\", \"травня\", \"червня\", \"липня\", \"серпня\", \"вересня\", \"жовтня\", \"листопада\", \"грудня\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"нед\", \"пон\", \"вів\", \"сер\", \"чтв\", \"птн\", \"суб\"],\n  wide: [\"неділя\", \"понеділок\", \"вівторок\", \"середа\", \"четвер\", \"п’ятниця\", \"субота\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ніч\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ніч\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"північ\",\n    noon: \"полудень\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"вечір\",\n    night: \"ніч\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"північ\",\n    noon: \"полудень\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const unit = String(options?.unit);\n  const number = Number(dirtyNumber);\n  let suffix;\n  if (unit === \"date\") {\n    if (number === 3 || number === 23) {\n      suffix = \"-є\";\n    } else {\n      suffix = \"-е\";\n    }\n  } else if (unit === \"minute\" || unit === \"second\" || unit === \"hour\") {\n    suffix = \"-а\";\n  } else {\n    suffix = \"-й\";\n  }\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}