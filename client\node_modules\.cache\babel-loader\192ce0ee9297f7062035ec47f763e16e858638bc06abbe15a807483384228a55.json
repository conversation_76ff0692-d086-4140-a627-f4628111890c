{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"ավելի քիչ քան 1 վայրկյան\",\n    other: \"ավելի քիչ քան {{count}} վայրկյան\"\n  },\n  xSeconds: {\n    one: \"1 վայրկյան\",\n    other: \"{{count}} վայրկյան\"\n  },\n  halfAMinute: \"կես րոպե\",\n  lessThanXMinutes: {\n    one: \"ավելի քիչ քան 1 րոպե\",\n    other: \"ավելի քիչ քան {{count}} րոպե\"\n  },\n  xMinutes: {\n    one: \"1 րոպե\",\n    other: \"{{count}} րոպե\"\n  },\n  aboutXHours: {\n    one: \"մոտ 1 ժամ\",\n    other: \"մոտ {{count}} ժամ\"\n  },\n  xHours: {\n    one: \"1 ժամ\",\n    other: \"{{count}} ժամ\"\n  },\n  xDays: {\n    one: \"1 օր\",\n    other: \"{{count}} օր\"\n  },\n  aboutXWeeks: {\n    one: \"մոտ 1 շաբաթ\",\n    other: \"մոտ {{count}} շաբաթ\"\n  },\n  xWeeks: {\n    one: \"1 շաբաթ\",\n    other: \"{{count}} շաբաթ\"\n  },\n  aboutXMonths: {\n    one: \"մոտ 1 ամիս\",\n    other: \"մոտ {{count}} ամիս\"\n  },\n  xMonths: {\n    one: \"1 ամիս\",\n    other: \"{{count}} ամիս\"\n  },\n  aboutXYears: {\n    one: \"մոտ 1 տարի\",\n    other: \"մոտ {{count}} տարի\"\n  },\n  xYears: {\n    one: \"1 տարի\",\n    other: \"{{count}} տարի\"\n  },\n  overXYears: {\n    one: \"ավելի քան 1 տարի\",\n    other: \"ավելի քան {{count}} տարի\"\n  },\n  almostXYears: {\n    one: \"համարյա 1 տարի\",\n    other: \"համարյա {{count}} տարի\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" հետո\";\n    } else {\n      return result + \" առաջ\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}