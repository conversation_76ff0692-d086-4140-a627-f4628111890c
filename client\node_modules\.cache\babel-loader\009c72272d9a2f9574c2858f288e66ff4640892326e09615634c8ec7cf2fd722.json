{"ast": null, "code": "function declensionGroup(scheme, count) {\n  if (count === 1 && scheme.one) {\n    return scheme.one;\n  }\n  if (count >= 2 && count <= 4 && scheme.twoFour) {\n    return scheme.twoFour;\n  }\n\n  // if count === null || count === 0 || count >= 5\n  return scheme.other;\n}\nfunction declension(scheme, count, time) {\n  const group = declensionGroup(scheme, count);\n  const finalText = group[time];\n  return finalText.replace(\"{{count}}\", String(count));\n}\nfunction extractPreposition(token) {\n  const result = [\"lessThan\", \"about\", \"over\", \"almost\"].filter(function (preposition) {\n    return !!token.match(new RegExp(\"^\" + preposition));\n  });\n  return result[0];\n}\nfunction prefixPreposition(preposition) {\n  let translation = \"\";\n  if (preposition === \"almost\") {\n    translation = \"takmer\";\n  }\n  if (preposition === \"about\") {\n    translation = \"približne\";\n  }\n  return translation.length > 0 ? translation + \" \" : \"\";\n}\nfunction suffixPreposition(preposition) {\n  let translation = \"\";\n  if (preposition === \"lessThan\") {\n    translation = \"menej než\";\n  }\n  if (preposition === \"over\") {\n    translation = \"viac než\";\n  }\n  return translation.length > 0 ? translation + \" \" : \"\";\n}\nfunction lowercaseFirstLetter(string) {\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}\nconst formatDistanceLocale = {\n  xSeconds: {\n    one: {\n      present: \"sekunda\",\n      past: \"sekundou\",\n      future: \"sekundu\"\n    },\n    twoFour: {\n      present: \"{{count}} sekundy\",\n      past: \"{{count}} sekundami\",\n      future: \"{{count}} sekundy\"\n    },\n    other: {\n      present: \"{{count}} sekúnd\",\n      past: \"{{count}} sekundami\",\n      future: \"{{count}} sekúnd\"\n    }\n  },\n  halfAMinute: {\n    other: {\n      present: \"pol minúty\",\n      past: \"pol minútou\",\n      future: \"pol minúty\"\n    }\n  },\n  xMinutes: {\n    one: {\n      present: \"minúta\",\n      past: \"minútou\",\n      future: \"minútu\"\n    },\n    twoFour: {\n      present: \"{{count}} minúty\",\n      past: \"{{count}} minútami\",\n      future: \"{{count}} minúty\"\n    },\n    other: {\n      present: \"{{count}} minút\",\n      past: \"{{count}} minútami\",\n      future: \"{{count}} minút\"\n    }\n  },\n  xHours: {\n    one: {\n      present: \"hodina\",\n      past: \"hodinou\",\n      future: \"hodinu\"\n    },\n    twoFour: {\n      present: \"{{count}} hodiny\",\n      past: \"{{count}} hodinami\",\n      future: \"{{count}} hodiny\"\n    },\n    other: {\n      present: \"{{count}} hodín\",\n      past: \"{{count}} hodinami\",\n      future: \"{{count}} hodín\"\n    }\n  },\n  xDays: {\n    one: {\n      present: \"deň\",\n      past: \"dňom\",\n      future: \"deň\"\n    },\n    twoFour: {\n      present: \"{{count}} dni\",\n      past: \"{{count}} dňami\",\n      future: \"{{count}} dni\"\n    },\n    other: {\n      present: \"{{count}} dní\",\n      past: \"{{count}} dňami\",\n      future: \"{{count}} dní\"\n    }\n  },\n  xWeeks: {\n    one: {\n      present: \"týždeň\",\n      past: \"týždňom\",\n      future: \"týždeň\"\n    },\n    twoFour: {\n      present: \"{{count}} týždne\",\n      past: \"{{count}} týždňami\",\n      future: \"{{count}} týždne\"\n    },\n    other: {\n      present: \"{{count}} týždňov\",\n      past: \"{{count}} týždňami\",\n      future: \"{{count}} týždňov\"\n    }\n  },\n  xMonths: {\n    one: {\n      present: \"mesiac\",\n      past: \"mesiacom\",\n      future: \"mesiac\"\n    },\n    twoFour: {\n      present: \"{{count}} mesiace\",\n      past: \"{{count}} mesiacmi\",\n      future: \"{{count}} mesiace\"\n    },\n    other: {\n      present: \"{{count}} mesiacov\",\n      past: \"{{count}} mesiacmi\",\n      future: \"{{count}} mesiacov\"\n    }\n  },\n  xYears: {\n    one: {\n      present: \"rok\",\n      past: \"rokom\",\n      future: \"rok\"\n    },\n    twoFour: {\n      present: \"{{count}} roky\",\n      past: \"{{count}} rokmi\",\n      future: \"{{count}} roky\"\n    },\n    other: {\n      present: \"{{count}} rokov\",\n      past: \"{{count}} rokmi\",\n      future: \"{{count}} rokov\"\n    }\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const preposition = extractPreposition(token) || \"\";\n  const key = lowercaseFirstLetter(token.substring(preposition.length));\n  const scheme = formatDistanceLocale[key];\n  if (!options?.addSuffix) {\n    return prefixPreposition(preposition) + suffixPreposition(preposition) + declension(scheme, count, \"present\");\n  }\n  if (options.comparison && options.comparison > 0) {\n    return prefixPreposition(preposition) + \"o \" + suffixPreposition(preposition) + declension(scheme, count, \"future\");\n  } else {\n    return prefixPreposition(preposition) + \"pred \" + suffixPreposition(preposition) + declension(scheme, count, \"past\");\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}