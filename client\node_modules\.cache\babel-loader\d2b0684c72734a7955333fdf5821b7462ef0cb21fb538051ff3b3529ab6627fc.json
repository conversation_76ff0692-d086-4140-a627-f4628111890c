{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst weekdays = [\"недела\", \"понеделник\", \"вторник\", \"среда\", \"четврток\", \"петок\", \"сабота\"];\nfunction lastWeek(day) {\n  const weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'минатата \" + weekday + \" во' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'минатиот \" + weekday + \" во' p\";\n  }\n}\nfunction thisWeek(day) {\n  const weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'ова \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'овој \" + weekday + \" вo' p\";\n  }\n}\nfunction nextWeek(day) {\n  const weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следната \" + weekday + \" вo' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следниот \" + weekday + \" вo' p\";\n  }\n}\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера во' p\",\n  today: \"'денес во' p\",\n  tomorrow: \"'утре во' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}