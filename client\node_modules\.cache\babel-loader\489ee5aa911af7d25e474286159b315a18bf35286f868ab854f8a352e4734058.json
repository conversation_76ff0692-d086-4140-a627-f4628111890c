{"ast": null, "code": "const accusativeWeekdays = [\"vas<PERSON>rna<PERSON>\", \"hétfőn\", \"kedden\", \"szerdán\", \"csü<PERSON><PERSON>rtökön\", \"pénteken\", \"szombaton\"];\nfunction week(isFuture) {\n  return date => {\n    const weekday = accusativeWeekdays[date.getDay()];\n    const prefix = isFuture ? \"\" : \"'múlt' \";\n    return `${prefix}'${weekday}' p'-kor'`;\n  };\n}\nconst formatRelativeLocale = {\n  lastWeek: week(false),\n  yesterday: \"'tegnap' p'-kor'\",\n  today: \"'ma' p'-kor'\",\n  tomorrow: \"'holnap' p'-kor'\",\n  nextWeek: week(true),\n  other: \"P\"\n};\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}