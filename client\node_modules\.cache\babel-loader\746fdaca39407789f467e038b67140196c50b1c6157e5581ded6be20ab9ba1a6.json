{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nimport { dayPeriodEnumToHours } from \"../utils.js\";\nexport class AMPMParser extends Parser {\n  priority = 80;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n      case \"aaa\":\n        return match.dayPeriod(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"aaaaa\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"aaaa\":\n      default:\n        return match.dayPeriod(dateString, {\n          width: \"wide\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n    }\n  }\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"b\", \"B\", \"H\", \"k\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}