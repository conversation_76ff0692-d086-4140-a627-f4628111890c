{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^\\d+\\./i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(e\\.m\\.a|m\\.a\\.j|eKr|pKr)/i,\n  abbreviated: /^(e\\.m\\.a|m\\.a\\.j|eKr|pKr)/i,\n  wide: /^(enne meie ajaarvamist|meie ajaarvamise järgi|enne <PERSON>|päras<PERSON> Kristust)/i\n};\nconst parseEraPatterns = {\n  any: [/^e/i, /^(m|p)/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^K[1234]/i,\n  wide: /^[1234](\\.)? kvartal/i\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^[jvmasond]/i,\n  abbreviated: /^(jaan|veebr|märts|apr|mai|juuni|juuli|aug|sept|okt|nov|dets)/i,\n  wide: /^(jaanuar|veebruar|märts|aprill|mai|juuni|juuli|august|september|oktoober|november|detsember)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^j/i, /^v/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^v/i, /^mär/i, /^ap/i, /^mai/i, /^juun/i, /^juul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[petknrl]/i,\n  short: /^[petknrl]/i,\n  abbreviated: /^(püh?|esm?|tei?|kolm?|nel?|ree?|laup?)\\.?/i,\n  wide: /^(pühapäev|esmaspäev|teisipäev|kolmapäev|neljapäev|reede|laupäev)/i\n};\nconst parseDayPatterns = {\n  any: [/^p/i, /^e/i, /^t/i, /^k/i, /^n/i, /^r/i, /^l/i]\n};\nconst matchDayPeriodPatterns = {\n  any: /^(am|pm|keskööl?|keskpäev(al)?|hommik(ul)?|pärastlõunal?|õhtul?|öö(sel)?)/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^keskö/i,\n    noon: /^keskp/i,\n    morning: /hommik/i,\n    afternoon: /pärastlõuna/i,\n    evening: /õhtu/i,\n    night: /öö/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}