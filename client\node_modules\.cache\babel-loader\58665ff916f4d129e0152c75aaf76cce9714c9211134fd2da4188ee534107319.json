{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"пр.н.е.\", \"н.е.\"],\n  abbreviated: [\"преди н. е.\", \"н. е.\"],\n  wide: [\"преди новата ера\", \"новата ера\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-во тримес.\", \"2-ро тримес.\", \"3-то тримес.\", \"4-то тримес.\"],\n  wide: [\"1-во тримесечие\", \"2-ро тримесечие\", \"3-то тримесечие\", \"4-то тримесечие\"]\n};\nconst monthValues = {\n  abbreviated: [\"яну\", \"фев\", \"мар\", \"апр\", \"май\", \"юни\", \"юли\", \"авг\", \"сеп\", \"окт\", \"ное\", \"дек\"],\n  wide: [\"януари\", \"февруари\", \"март\", \"април\", \"май\", \"юни\", \"юли\", \"август\", \"септември\", \"октомври\", \"ноември\", \"декември\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"нед\", \"пон\", \"вто\", \"сря\", \"чет\", \"пет\", \"съб\"],\n  wide: [\"неделя\", \"понеделник\", \"вторник\", \"сряда\", \"четвъртък\", \"петък\", \"събота\"]\n};\nconst dayPeriodValues = {\n  wide: {\n    am: \"преди обяд\",\n    pm: \"след обяд\",\n    midnight: \"в полунощ\",\n    noon: \"на обяд\",\n    morning: \"сутринта\",\n    afternoon: \"следобед\",\n    evening: \"вечерта\",\n    night: \"през нощта\"\n  }\n};\nfunction isFeminine(unit) {\n  return unit === \"year\" || unit === \"week\" || unit === \"minute\" || unit === \"second\";\n}\nfunction isNeuter(unit) {\n  return unit === \"quarter\";\n}\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n  const suffix = isNeuter(unit) ? neuter : isFeminine(unit) ? feminine : masculine;\n  return number + \"-\" + suffix;\n}\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  if (number === 0) {\n    return numberWithSuffix(0, unit, \"ев\", \"ева\", \"ево\");\n  } else if (number % 1000 === 0) {\n    return numberWithSuffix(number, unit, \"ен\", \"на\", \"но\");\n  } else if (number % 100 === 0) {\n    return numberWithSuffix(number, unit, \"тен\", \"тна\", \"тно\");\n  }\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return numberWithSuffix(number, unit, \"ви\", \"ва\", \"во\");\n      case 2:\n        return numberWithSuffix(number, unit, \"ри\", \"ра\", \"ро\");\n      case 7:\n      case 8:\n        return numberWithSuffix(number, unit, \"ми\", \"ма\", \"мо\");\n    }\n  }\n  return numberWithSuffix(number, unit, \"ти\", \"та\", \"то\");\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}