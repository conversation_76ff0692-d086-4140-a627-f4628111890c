{"ast": null, "code": "function isPluralType(val) {\n  return val.one !== undefined;\n}\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: \"ஒரு வினாடிக்கு குறைவாக\",\n      in: \"ஒரு வினாடிக்குள்\",\n      ago: \"ஒரு வினாடிக்கு முன்பு\"\n    },\n    other: {\n      default: \"{{count}} வினாடிகளுக்கு குறைவாக\",\n      in: \"{{count}} வினாடிகளுக்குள்\",\n      ago: \"{{count}} வினாடிகளுக்கு முன்பு\"\n    }\n  },\n  xSeconds: {\n    one: {\n      default: \"1 வினாடி\",\n      in: \"1 வினாடியில்\",\n      ago: \"1 வினாடி முன்பு\"\n    },\n    other: {\n      default: \"{{count}} விநாடிகள்\",\n      in: \"{{count}} வினாடிகளில்\",\n      ago: \"{{count}} விநாடிகளுக்கு முன்பு\"\n    }\n  },\n  halfAMinute: {\n    default: \"அரை நிமிடம்\",\n    in: \"அரை நிமிடத்தில்\",\n    ago: \"அரை நிமிடம் முன்பு\"\n  },\n  lessThanXMinutes: {\n    one: {\n      default: \"ஒரு நிமிடத்திற்கும் குறைவாக\",\n      in: \"ஒரு நிமிடத்திற்குள்\",\n      ago: \"ஒரு நிமிடத்திற்கு முன்பு\"\n    },\n    other: {\n      default: \"{{count}} நிமிடங்களுக்கும் குறைவாக\",\n      in: \"{{count}} நிமிடங்களுக்குள்\",\n      ago: \"{{count}} நிமிடங்களுக்கு முன்பு\"\n    }\n  },\n  xMinutes: {\n    one: {\n      default: \"1 நிமிடம்\",\n      in: \"1 நிமிடத்தில்\",\n      ago: \"1 நிமிடம் முன்பு\"\n    },\n    other: {\n      default: \"{{count}} நிமிடங்கள்\",\n      in: \"{{count}} நிமிடங்களில்\",\n      ago: \"{{count}} நிமிடங்களுக்கு முன்பு\"\n    }\n  },\n  aboutXHours: {\n    one: {\n      default: \"சுமார் 1 மணி நேரம்\",\n      in: \"சுமார் 1 மணி நேரத்தில்\",\n      ago: \"சுமார் 1 மணி நேரத்திற்கு முன்பு\"\n    },\n    other: {\n      default: \"சுமார் {{count}} மணி நேரம்\",\n      in: \"சுமார் {{count}} மணி நேரத்திற்கு முன்பு\",\n      ago: \"சுமார் {{count}} மணி நேரத்தில்\"\n    }\n  },\n  xHours: {\n    one: {\n      default: \"1 மணி நேரம்\",\n      in: \"1 மணி நேரத்தில்\",\n      ago: \"1 மணி நேரத்திற்கு முன்பு\"\n    },\n    other: {\n      default: \"{{count}} மணி நேரம்\",\n      in: \"{{count}} மணி நேரத்தில்\",\n      ago: \"{{count}} மணி நேரத்திற்கு முன்பு\"\n    }\n  },\n  xDays: {\n    one: {\n      default: \"1 நாள்\",\n      in: \"1 நாளில்\",\n      ago: \"1 நாள் முன்பு\"\n    },\n    other: {\n      default: \"{{count}} நாட்கள்\",\n      in: \"{{count}} நாட்களில்\",\n      ago: \"{{count}} நாட்களுக்கு முன்பு\"\n    }\n  },\n  aboutXWeeks: {\n    one: {\n      default: \"சுமார் 1 வாரம்\",\n      in: \"சுமார் 1 வாரத்தில்\",\n      ago: \"சுமார் 1 வாரம் முன்பு\"\n    },\n    other: {\n      default: \"சுமார் {{count}} வாரங்கள்\",\n      in: \"சுமார் {{count}} வாரங்களில்\",\n      ago: \"சுமார் {{count}} வாரங்களுக்கு முன்பு\"\n    }\n  },\n  xWeeks: {\n    one: {\n      default: \"1 வாரம்\",\n      in: \"1 வாரத்தில்\",\n      ago: \"1 வாரம் முன்பு\"\n    },\n    other: {\n      default: \"{{count}} வாரங்கள்\",\n      in: \"{{count}} வாரங்களில்\",\n      ago: \"{{count}} வாரங்களுக்கு முன்பு\"\n    }\n  },\n  aboutXMonths: {\n    one: {\n      default: \"சுமார் 1 மாதம்\",\n      in: \"சுமார் 1 மாதத்தில்\",\n      ago: \"சுமார் 1 மாதத்திற்கு முன்பு\"\n    },\n    other: {\n      default: \"சுமார் {{count}} மாதங்கள்\",\n      in: \"சுமார் {{count}} மாதங்களில்\",\n      ago: \"சுமார் {{count}} மாதங்களுக்கு முன்பு\"\n    }\n  },\n  xMonths: {\n    one: {\n      default: \"1 மாதம்\",\n      in: \"1 மாதத்தில்\",\n      ago: \"1 மாதம் முன்பு\"\n    },\n    other: {\n      default: \"{{count}} மாதங்கள்\",\n      in: \"{{count}} மாதங்களில்\",\n      ago: \"{{count}} மாதங்களுக்கு முன்பு\"\n    }\n  },\n  aboutXYears: {\n    one: {\n      default: \"சுமார் 1 வருடம்\",\n      in: \"சுமார் 1 ஆண்டில்\",\n      ago: \"சுமார் 1 வருடம் முன்பு\"\n    },\n    other: {\n      default: \"சுமார் {{count}} ஆண்டுகள்\",\n      in: \"சுமார் {{count}} ஆண்டுகளில்\",\n      ago: \"சுமார் {{count}} ஆண்டுகளுக்கு முன்பு\"\n    }\n  },\n  xYears: {\n    one: {\n      default: \"1 வருடம்\",\n      in: \"1 ஆண்டில்\",\n      ago: \"1 வருடம் முன்பு\"\n    },\n    other: {\n      default: \"{{count}} ஆண்டுகள்\",\n      in: \"{{count}} ஆண்டுகளில்\",\n      ago: \"{{count}} ஆண்டுகளுக்கு முன்பு\"\n    }\n  },\n  overXYears: {\n    one: {\n      default: \"1 வருடத்திற்கு மேல்\",\n      in: \"1 வருடத்திற்கும் மேலாக\",\n      ago: \"1 வருடம் முன்பு\"\n    },\n    other: {\n      default: \"{{count}} ஆண்டுகளுக்கும் மேலாக\",\n      in: \"{{count}} ஆண்டுகளில்\",\n      ago: \"{{count}} ஆண்டுகளுக்கு முன்பு\"\n    }\n  },\n  almostXYears: {\n    one: {\n      default: \"கிட்டத்தட்ட 1 வருடம்\",\n      in: \"கிட்டத்தட்ட 1 ஆண்டில்\",\n      ago: \"கிட்டத்தட்ட 1 வருடம் முன்பு\"\n    },\n    other: {\n      default: \"கிட்டத்தட்ட {{count}} ஆண்டுகள்\",\n      in: \"கிட்டத்தட்ட {{count}} ஆண்டுகளில்\",\n      ago: \"கிட்டத்தட்ட {{count}} ஆண்டுகளுக்கு முன்பு\"\n    }\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const tense = options?.addSuffix ? options.comparison && options.comparison > 0 ? \"in\" : \"ago\" : \"default\";\n  const tokenValue = formatDistanceLocale[token];\n  if (!isPluralType(tokenValue)) return tokenValue[tense];\n  if (count === 1) {\n    return tokenValue.one[tense];\n  } else {\n    return tokenValue.other[tense].replace(\"{{count}}\", String(count));\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}