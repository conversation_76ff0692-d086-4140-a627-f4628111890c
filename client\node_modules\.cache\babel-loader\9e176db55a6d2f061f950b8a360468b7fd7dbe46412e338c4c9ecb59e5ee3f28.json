{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    past: \"{{count}} წამზე ნაკლები ხნის წინ\",\n    present: \"{{count}} წამზე ნაკლები\",\n    future: \"{{count}} წამზე ნაკლებში\"\n  },\n  xSeconds: {\n    past: \"{{count}} წამის წინ\",\n    present: \"{{count}} წამი\",\n    future: \"{{count}} წამში\"\n  },\n  halfAMinute: {\n    past: \"ნახევარი წუთის წინ\",\n    present: \"ნახევარი წუთი\",\n    future: \"ნახევარი წუთში\"\n  },\n  lessThanXMinutes: {\n    past: \"{{count}} წუთზე ნაკლები ხნის წინ\",\n    present: \"{{count}} წუთზე ნაკლები\",\n    future: \"{{count}} წუთზე ნაკლებში\"\n  },\n  xMinutes: {\n    past: \"{{count}} წუთის წინ\",\n    present: \"{{count}} წუთი\",\n    future: \"{{count}} წუთში\"\n  },\n  aboutXHours: {\n    past: \"დაახლოებით {{count}} საათის წინ\",\n    present: \"დაახლოებით {{count}} საათი\",\n    future: \"დაახლოებით {{count}} საათში\"\n  },\n  xHours: {\n    past: \"{{count}} საათის წინ\",\n    present: \"{{count}} საათი\",\n    future: \"{{count}} საათში\"\n  },\n  xDays: {\n    past: \"{{count}} დღის წინ\",\n    present: \"{{count}} დღე\",\n    future: \"{{count}} დღეში\"\n  },\n  aboutXWeeks: {\n    past: \"დაახლოებით {{count}} კვირას წინ\",\n    present: \"დაახლოებით {{count}} კვირა\",\n    future: \"დაახლოებით {{count}} კვირაში\"\n  },\n  xWeeks: {\n    past: \"{{count}} კვირას კვირა\",\n    present: \"{{count}} კვირა\",\n    future: \"{{count}} კვირაში\"\n  },\n  aboutXMonths: {\n    past: \"დაახლოებით {{count}} თვის წინ\",\n    present: \"დაახლოებით {{count}} თვე\",\n    future: \"დაახლოებით {{count}} თვეში\"\n  },\n  xMonths: {\n    past: \"{{count}} თვის წინ\",\n    present: \"{{count}} თვე\",\n    future: \"{{count}} თვეში\"\n  },\n  aboutXYears: {\n    past: \"დაახლოებით {{count}} წლის წინ\",\n    present: \"დაახლოებით {{count}} წელი\",\n    future: \"დაახლოებით {{count}} წელში\"\n  },\n  xYears: {\n    past: \"{{count}} წლის წინ\",\n    present: \"{{count}} წელი\",\n    future: \"{{count}} წელში\"\n  },\n  overXYears: {\n    past: \"{{count}} წელზე მეტი ხნის წინ\",\n    present: \"{{count}} წელზე მეტი\",\n    future: \"{{count}} წელზე მეტი ხნის შემდეგ\"\n  },\n  almostXYears: {\n    past: \"თითქმის {{count}} წლის წინ\",\n    present: \"თითქმის {{count}} წელი\",\n    future: \"თითქმის {{count}} წელში\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (options?.addSuffix && options.comparison && options.comparison > 0) {\n    result = tokenValue.future.replace(\"{{count}}\", String(count));\n  } else if (options?.addSuffix) {\n    result = tokenValue.past.replace(\"{{count}}\", String(count));\n  } else {\n    result = tokenValue.present.replace(\"{{count}}\", String(count));\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}