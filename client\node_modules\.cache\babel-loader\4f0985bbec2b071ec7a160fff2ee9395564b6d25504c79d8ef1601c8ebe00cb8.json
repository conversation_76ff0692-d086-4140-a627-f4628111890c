{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"f.Kr.\", \"e.Kr.\"],\n  abbreviated: [\"f.Kr.\", \"e.Kr.\"],\n  wide: [\"fyrir <PERSON>\", \"eftir <PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1F\", \"2F\", \"3F\", \"4F\"],\n  wide: [\"1. fjórðungur\", \"2. fjórðungur\", \"3. fjórðungur\", \"4. fjórðungur\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"Á\", \"S\", \"Ó\", \"N\", \"D\"],\n  abbreviated: [\"jan.\", \"feb.\", \"mars\", \"apríl\", \"maí\", \"jún<PERSON>\", \"júl<PERSON>\", \"ágúst\", \"sept.\", \"okt.\", \"nóv.\", \"des.\"],\n  wide: [\"jan<PERSON>ar\", \"febrúar\", \"mars\", \"apríl\", \"maí\", \"j<PERSON><PERSON>\", \"júlí\", \"ágúst\", \"september\", \"október\", \"nóvember\", \"desember\"]\n};\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"Þ\", \"M\", \"F\", \"F\", \"L\"],\n  short: [\"Su\", \"Má\", \"Þr\", \"Mi\", \"Fi\", \"Fö\", \"La\"],\n  abbreviated: [\"sun.\", \"mán.\", \"þri.\", \"mið.\", \"fim.\", \"fös.\", \"lau.\"],\n  wide: [\"sunnudagur\", \"mánudagur\", \"þriðjudagur\", \"miðvikudagur\", \"fimmtudagur\", \"föstudagur\", \"laugardagur\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"f\",\n    pm: \"e\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\"\n  },\n  abbreviated: {\n    am: \"f.h.\",\n    pm: \"e.h.\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\"\n  },\n  wide: {\n    am: \"fyrir hádegi\",\n    pm: \"eftir hádegi\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"f\",\n    pm: \"e\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\"\n  },\n  abbreviated: {\n    am: \"f.h.\",\n    pm: \"e.h.\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\"\n  },\n  wide: {\n    am: \"fyrir hádegi\",\n    pm: \"eftir hádegi\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}