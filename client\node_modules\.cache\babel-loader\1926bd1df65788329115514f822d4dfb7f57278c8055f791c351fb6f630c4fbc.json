{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"πΧ\", \"μΧ\"],\n  abbreviated: [\"π.Χ.\", \"μ.Χ.\"],\n  wide: [\"προ Χριστού\", \"μετά Χριστόν\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Τ1\", \"Τ2\", \"Τ3\", \"Τ4\"],\n  wide: [\"1ο τρίμηνο\", \"2ο τρίμηνο\", \"3ο τρίμηνο\", \"4ο τρίμηνο\"]\n};\nconst monthValues = {\n  narrow: [\"Ι\", \"Φ\", \"Μ\", \"Α\", \"Μ\", \"Ι\", \"Ι\", \"Α\", \"Σ\", \"Ο\", \"Ν\", \"Δ\"],\n  abbreviated: [\"Ιαν\", \"Φεβ\", \"Μάρ\", \"Απρ\", \"Μά<PERSON>\", \"Ιού<PERSON>\", \"<PERSON>ού<PERSON>\", \"Αύγ\", \"Σε<PERSON>\", \"Οκτ\", \"Νοέ\", \"Δεκ\"],\n  wide: [\"Ιανουάριος\", \"Φεβρουάριος\", \"Μάρτιος\", \"Απρίλιος\", \"Μάιος\", \"Ιούνιος\", \"Ιούλιος\", \"Αύγουστος\", \"Σεπτέμβριος\", \"Οκτώβριος\", \"Νοέμβριος\", \"Δεκέμβριος\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"Ι\", \"Φ\", \"Μ\", \"Α\", \"Μ\", \"Ι\", \"Ι\", \"Α\", \"Σ\", \"Ο\", \"Ν\", \"Δ\"],\n  abbreviated: [\"Ιαν\", \"Φεβ\", \"Μαρ\", \"Απρ\", \"Μαΐ\", \"Ιουν\", \"Ιουλ\", \"Αυγ\", \"Σεπ\", \"Οκτ\", \"Νοε\", \"Δεκ\"],\n  wide: [\"Ιανουαρίου\", \"Φεβρουαρίου\", \"Μαρτίου\", \"Απριλίου\", \"Μαΐου\", \"Ιουνίου\", \"Ιουλίου\", \"Αυγούστου\", \"Σεπτεμβρίου\", \"Οκτωβρίου\", \"Νοεμβρίου\", \"Δεκεμβρίου\"]\n};\nconst dayValues = {\n  narrow: [\"Κ\", \"Δ\", \"T\", \"Τ\", \"Π\", \"Π\", \"Σ\"],\n  short: [\"Κυ\", \"Δε\", \"Τρ\", \"Τε\", \"Πέ\", \"Πα\", \"Σά\"],\n  abbreviated: [\"Κυρ\", \"Δευ\", \"Τρί\", \"Τετ\", \"Πέμ\", \"Παρ\", \"Σάβ\"],\n  wide: [\"Κυριακή\", \"Δευτέρα\", \"Τρίτη\", \"Τετάρτη\", \"Πέμπτη\", \"Παρασκευή\", \"Σάββατο\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"πμ\",\n    pm: \"μμ\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\"\n  },\n  abbreviated: {\n    am: \"π.μ.\",\n    pm: \"μ.μ.\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\"\n  },\n  wide: {\n    am: \"π.μ.\",\n    pm: \"μ.μ.\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  let suffix;\n  if (unit === \"year\" || unit === \"month\") {\n    suffix = \"ος\";\n  } else if (unit === \"week\" || unit === \"dayOfYear\" || unit === \"day\" || unit === \"hour\" || unit === \"date\") {\n    suffix = \"η\";\n  } else {\n    suffix = \"ο\";\n  }\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}