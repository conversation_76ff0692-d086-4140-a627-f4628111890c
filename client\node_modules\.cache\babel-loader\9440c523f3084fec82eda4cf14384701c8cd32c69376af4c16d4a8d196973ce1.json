{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      standalone: \"мање од 1 секунде\",\n      withPrepositionAgo: \"мање од 1 секунде\",\n      withPrepositionIn: \"мање од 1 секунду\"\n    },\n    dual: \"мање од {{count}} секунде\",\n    other: \"мање од {{count}} секунди\"\n  },\n  xSeconds: {\n    one: {\n      standalone: \"1 секунда\",\n      withPrepositionAgo: \"1 секунде\",\n      withPrepositionIn: \"1 секунду\"\n    },\n    dual: \"{{count}} секунде\",\n    other: \"{{count}} секунди\"\n  },\n  halfAMinute: \"пола минуте\",\n  lessThanXMinutes: {\n    one: {\n      standalone: \"мање од 1 минуте\",\n      withPrepositionAgo: \"мање од 1 минуте\",\n      withPrepositionIn: \"мање од 1 минуту\"\n    },\n    dual: \"мање од {{count}} минуте\",\n    other: \"мање од {{count}} минута\"\n  },\n  xMinutes: {\n    one: {\n      standalone: \"1 минута\",\n      withPrepositionAgo: \"1 минуте\",\n      withPrepositionIn: \"1 минуту\"\n    },\n    dual: \"{{count}} минуте\",\n    other: \"{{count}} минута\"\n  },\n  aboutXHours: {\n    one: {\n      standalone: \"око 1 сат\",\n      withPrepositionAgo: \"око 1 сат\",\n      withPrepositionIn: \"око 1 сат\"\n    },\n    dual: \"око {{count}} сата\",\n    other: \"око {{count}} сати\"\n  },\n  xHours: {\n    one: {\n      standalone: \"1 сат\",\n      withPrepositionAgo: \"1 сат\",\n      withPrepositionIn: \"1 сат\"\n    },\n    dual: \"{{count}} сата\",\n    other: \"{{count}} сати\"\n  },\n  xDays: {\n    one: {\n      standalone: \"1 дан\",\n      withPrepositionAgo: \"1 дан\",\n      withPrepositionIn: \"1 дан\"\n    },\n    dual: \"{{count}} дана\",\n    other: \"{{count}} дана\"\n  },\n  aboutXWeeks: {\n    one: {\n      standalone: \"око 1 недељу\",\n      withPrepositionAgo: \"око 1 недељу\",\n      withPrepositionIn: \"око 1 недељу\"\n    },\n    dual: \"око {{count}} недеље\",\n    other: \"око {{count}} недеље\"\n  },\n  xWeeks: {\n    one: {\n      standalone: \"1 недељу\",\n      withPrepositionAgo: \"1 недељу\",\n      withPrepositionIn: \"1 недељу\"\n    },\n    dual: \"{{count}} недеље\",\n    other: \"{{count}} недеље\"\n  },\n  aboutXMonths: {\n    one: {\n      standalone: \"око 1 месец\",\n      withPrepositionAgo: \"око 1 месец\",\n      withPrepositionIn: \"око 1 месец\"\n    },\n    dual: \"око {{count}} месеца\",\n    other: \"око {{count}} месеци\"\n  },\n  xMonths: {\n    one: {\n      standalone: \"1 месец\",\n      withPrepositionAgo: \"1 месец\",\n      withPrepositionIn: \"1 месец\"\n    },\n    dual: \"{{count}} месеца\",\n    other: \"{{count}} месеци\"\n  },\n  aboutXYears: {\n    one: {\n      standalone: \"око 1 годину\",\n      withPrepositionAgo: \"око 1 годину\",\n      withPrepositionIn: \"око 1 годину\"\n    },\n    dual: \"око {{count}} године\",\n    other: \"око {{count}} година\"\n  },\n  xYears: {\n    one: {\n      standalone: \"1 година\",\n      withPrepositionAgo: \"1 године\",\n      withPrepositionIn: \"1 годину\"\n    },\n    dual: \"{{count}} године\",\n    other: \"{{count}} година\"\n  },\n  overXYears: {\n    one: {\n      standalone: \"преко 1 годину\",\n      withPrepositionAgo: \"преко 1 годину\",\n      withPrepositionIn: \"преко 1 годину\"\n    },\n    dual: \"преко {{count}} године\",\n    other: \"преко {{count}} година\"\n  },\n  almostXYears: {\n    one: {\n      standalone: \"готово 1 годину\",\n      withPrepositionAgo: \"готово 1 годину\",\n      withPrepositionIn: \"готово 1 годину\"\n    },\n    dual: \"готово {{count}} године\",\n    other: \"готово {{count}} година\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        result = tokenValue.one.withPrepositionIn;\n      } else {\n        result = tokenValue.one.withPrepositionAgo;\n      }\n    } else {\n      result = tokenValue.one.standalone;\n    }\n  } else if (count % 10 > 1 && count % 10 < 5 &&\n  // if last digit is between 2 and 4\n  String(count).substr(-2, 1) !== \"1\" // unless the 2nd to last digit is \"1\"\n  ) {\n    result = tokenValue.dual.replace(\"{{count}}\", String(count));\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"за \" + result;\n    } else {\n      return \"пре \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}