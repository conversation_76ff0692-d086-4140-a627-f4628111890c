{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"minna en 1 sekúnda\",\n    other: \"minna en {{count}} sekúndur\"\n  },\n  xSeconds: {\n    one: \"1 sekúnda\",\n    other: \"{{count}} sekúndur\"\n  },\n  halfAMinute: \"h<PERSON><PERSON>\",\n  lessThanXMinutes: {\n    one: \"minna en 1 mínúta\",\n    other: \"minna en {{count}} mínútur\"\n  },\n  xMinutes: {\n    one: \"1 mínúta\",\n    other: \"{{count}} mínútur\"\n  },\n  aboutXHours: {\n    one: \"u.þ.b. 1 klukkustund\",\n    other: \"u.þ.b. {{count}} klukkustundir\"\n  },\n  xHours: {\n    one: \"1 klukkustund\",\n    other: \"{{count}} klukkustundir\"\n  },\n  xDays: {\n    one: \"1 dagur\",\n    other: \"{{count}} dagar\"\n  },\n  aboutXWeeks: {\n    one: \"um viku\",\n    other: \"um {{count}} vikur\"\n  },\n  xWeeks: {\n    one: \"1 viku\",\n    other: \"{{count}} vikur\"\n  },\n  aboutXMonths: {\n    one: \"u.þ.b. 1 mánuður\",\n    other: \"u.þ.b. {{count}} mánuðir\"\n  },\n  xMonths: {\n    one: \"1 mánuður\",\n    other: \"{{count}} mánuðir\"\n  },\n  aboutXYears: {\n    one: \"u.þ.b. 1 ár\",\n    other: \"u.þ.b. {{count}} ár\"\n  },\n  xYears: {\n    one: \"1 ár\",\n    other: \"{{count}} ár\"\n  },\n  overXYears: {\n    one: \"meira en 1 ár\",\n    other: \"meira en {{count}} ár\"\n  },\n  almostXYears: {\n    one: \"næstum 1 ár\",\n    other: \"næstum {{count}} ár\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"í \" + result;\n    } else {\n      return result + \" síðan\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}