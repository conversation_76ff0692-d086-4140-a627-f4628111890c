{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"пр.н.е.\", \"н.е.\"],\n  abbreviated: [\"пред н. е.\", \"н. е.\"],\n  wide: [\"пред нашата ера\", \"нашата ера\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ви кв.\", \"2-ри кв.\", \"3-ти кв.\", \"4-ти кв.\"],\n  wide: [\"1-ви квартал\", \"2-ри квартал\", \"3-ти квартал\", \"4-ти квартал\"]\n};\nconst monthValues = {\n  abbreviated: [\"јан\", \"фев\", \"мар\", \"апр\", \"мај\", \"јун\", \"јул\", \"авг\", \"септ\", \"окт\", \"ноем\", \"дек\"],\n  wide: [\"јануари\", \"февруари\", \"март\", \"април\", \"мај\", \"јуни\", \"јули\", \"август\", \"септември\", \"октомври\", \"ноември\", \"декември\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"не\", \"по\", \"вт\", \"ср\", \"че\", \"пе\", \"са\"],\n  abbreviated: [\"нед\", \"пон\", \"вто\", \"сре\", \"чет\", \"пет\", \"саб\"],\n  wide: [\"недела\", \"понеделник\", \"вторник\", \"среда\", \"четврток\", \"петок\", \"сабота\"]\n};\nconst dayPeriodValues = {\n  wide: {\n    am: \"претпладне\",\n    pm: \"попладне\",\n    midnight: \"полноќ\",\n    noon: \"напладне\",\n    morning: \"наутро\",\n    afternoon: \"попладне\",\n    evening: \"навечер\",\n    night: \"ноќе\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + \"-ви\";\n      case 2:\n        return number + \"-ри\";\n      case 7:\n      case 8:\n        return number + \"-ми\";\n    }\n  }\n  return number + \"-ти\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}