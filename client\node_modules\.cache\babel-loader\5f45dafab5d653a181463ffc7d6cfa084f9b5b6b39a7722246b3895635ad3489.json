{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"по-малко от секунда\",\n    other: \"по-малко от {{count}} секунди\"\n  },\n  xSeconds: {\n    one: \"1 секунда\",\n    other: \"{{count}} секунди\"\n  },\n  halfAMinute: \"половин минута\",\n  lessThanXMinutes: {\n    one: \"по-малко от минута\",\n    other: \"по-малко от {{count}} минути\"\n  },\n  xMinutes: {\n    one: \"1 минута\",\n    other: \"{{count}} минути\"\n  },\n  aboutXHours: {\n    one: \"около час\",\n    other: \"около {{count}} часа\"\n  },\n  xHours: {\n    one: \"1 час\",\n    other: \"{{count}} часа\"\n  },\n  xDays: {\n    one: \"1 ден\",\n    other: \"{{count}} дни\"\n  },\n  aboutXWeeks: {\n    one: \"около седмица\",\n    other: \"около {{count}} седмици\"\n  },\n  xWeeks: {\n    one: \"1 седмица\",\n    other: \"{{count}} седмици\"\n  },\n  aboutXMonths: {\n    one: \"около месец\",\n    other: \"около {{count}} месеца\"\n  },\n  xMonths: {\n    one: \"1 месец\",\n    other: \"{{count}} месеца\"\n  },\n  aboutXYears: {\n    one: \"около година\",\n    other: \"около {{count}} години\"\n  },\n  xYears: {\n    one: \"1 година\",\n    other: \"{{count}} години\"\n  },\n  overXYears: {\n    one: \"над година\",\n    other: \"над {{count}} години\"\n  },\n  almostXYears: {\n    one: \"почти година\",\n    other: \"почти {{count}} години\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"след \" + result;\n    } else {\n      return \"преди \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}