{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"př. n. l.\", \"n. l.\"],\n  abbreviated: [\"př. n. l.\", \"n. l.\"],\n  wide: [\"před naším letopo<PERSON>\", \"našeho letopočtu\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1. čtvrtletí\", \"2. čtvrtletí\", \"3. čtvrtletí\", \"4. čtvrtletí\"],\n  wide: [\"1. čtvrtletí\", \"2. čtvrtletí\", \"3. čtvrtletí\", \"4. čtvrtletí\"]\n};\nconst monthValues = {\n  narrow: [\"L\", \"Ú\", \"B\", \"D\", \"K\", \"Č\", \"Č\", \"S\", \"Z\", \"Ř\", \"L\", \"P\"],\n  abbreviated: [\"led\", \"úno\", \"bře\", \"dub\", \"kvě\", \"čvn\", \"čvc\", \"srp\", \"zář\", \"říj\", \"lis\", \"pro\"],\n  wide: [\"leden\", \"únor\", \"březen\", \"duben\", \"květen\", \"červen\", \"červenec\", \"srpen\", \"září\", \"říjen\", \"listopad\", \"prosinec\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"L\", \"Ú\", \"B\", \"D\", \"K\", \"Č\", \"Č\", \"S\", \"Z\", \"Ř\", \"L\", \"P\"],\n  abbreviated: [\"led\", \"úno\", \"bře\", \"dub\", \"kvě\", \"čvn\", \"čvc\", \"srp\", \"zář\", \"říj\", \"lis\", \"pro\"],\n  wide: [\"ledna\", \"února\", \"března\", \"dubna\", \"května\", \"června\", \"července\", \"srpna\", \"září\", \"října\", \"listopadu\", \"prosince\"]\n};\nconst dayValues = {\n  narrow: [\"ne\", \"po\", \"út\", \"st\", \"čt\", \"pá\", \"so\"],\n  short: [\"ne\", \"po\", \"út\", \"st\", \"čt\", \"pá\", \"so\"],\n  abbreviated: [\"ned\", \"pon\", \"úte\", \"stř\", \"čtv\", \"pát\", \"sob\"],\n  wide: [\"neděle\", \"pondělí\", \"úterý\", \"středa\", \"čtvrtek\", \"pátek\", \"sobota\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"dop.\",\n    pm: \"odp.\",\n    midnight: \"půlnoc\",\n    noon: \"poledne\",\n    morning: \"ráno\",\n    afternoon: \"odpoledne\",\n    evening: \"večer\",\n    night: \"noc\"\n  },\n  abbreviated: {\n    am: \"dop.\",\n    pm: \"odp.\",\n    midnight: \"půlnoc\",\n    noon: \"poledne\",\n    morning: \"ráno\",\n    afternoon: \"odpoledne\",\n    evening: \"večer\",\n    night: \"noc\"\n  },\n  wide: {\n    am: \"dopoledne\",\n    pm: \"odpoledne\",\n    midnight: \"půlnoc\",\n    noon: \"poledne\",\n    morning: \"ráno\",\n    afternoon: \"odpoledne\",\n    evening: \"večer\",\n    night: \"noc\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"dop.\",\n    pm: \"odp.\",\n    midnight: \"půlnoc\",\n    noon: \"poledne\",\n    morning: \"ráno\",\n    afternoon: \"odpoledne\",\n    evening: \"večer\",\n    night: \"noc\"\n  },\n  abbreviated: {\n    am: \"dop.\",\n    pm: \"odp.\",\n    midnight: \"půlnoc\",\n    noon: \"poledne\",\n    morning: \"ráno\",\n    afternoon: \"odpoledne\",\n    evening: \"večer\",\n    night: \"noc\"\n  },\n  wide: {\n    am: \"dopoledne\",\n    pm: \"odpoledne\",\n    midnight: \"půlnoc\",\n    noon: \"poledne\",\n    morning: \"ráno\",\n    afternoon: \"odpoledne\",\n    evening: \"večer\",\n    night: \"noc\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}