{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setSeconds} function options.\n */\n\n/**\n * @name setSeconds\n * @category Second Helpers\n * @summary Set the seconds to the given date, with context support.\n *\n * @description\n * Set the seconds to the given date, with an optional context for time zone specification.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param seconds - The seconds of the new date\n * @param options - An object with options\n *\n * @returns The new date with the seconds set\n *\n * @example\n * // Set 45 seconds to 1 September 2014 11:30:40:\n * const result = setSeconds(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:30:45\n */\nexport function setSeconds(date, seconds, options) {\n  const _date = toDate(date, options?.in);\n  _date.setSeconds(seconds);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setSeconds;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}