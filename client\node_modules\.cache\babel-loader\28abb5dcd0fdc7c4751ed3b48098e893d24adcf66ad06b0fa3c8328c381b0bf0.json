{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\n\nconst eraValues = {\n  narrow: [\"ಕ್ರಿ.ಪೂ\", \"ಕ್ರಿ.ಶ\"],\n  abbreviated: [\"ಕ್ರಿ.ಪೂ\", \"ಕ್ರಿ.ಶ\"],\n  // CLDR #1618, #1620\n  wide: [\"ಕ್ರಿಸ್ತ ಪೂರ್ವ\", \"ಕ್ರಿಸ್ತ ಶಕ\"] // CLDR #1614, #1616\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ತ್ರೈ 1\", \"ತ್ರೈ 2\", \"ತ್ರೈ 3\", \"ತ್ರೈ 4\"],\n  // CLDR #1630 - #1638\n  wide: [\"1ನೇ ತ್ರೈಮಾಸಿಕ\", \"2ನೇ ತ್ರೈಮಾಸಿಕ\", \"3ನೇ ತ್ರೈಮಾಸಿಕ\", \"4ನೇ ತ್ರೈಮಾಸಿಕ\"]\n  // CLDR #1622 - #1629\n};\n\n// CLDR #1646 - #1717\nconst monthValues = {\n  narrow: [\"ಜ\", \"ಫೆ\", \"ಮಾ\", \"ಏ\", \"ಮೇ\", \"ಜೂ\", \"ಜು\", \"ಆ\", \"ಸೆ\", \"ಅ\", \"ನ\", \"ಡಿ\"],\n  abbreviated: [\"ಜನ\", \"ಫೆಬ್ರ\", \"ಮಾರ್ಚ್\", \"ಏಪ್ರಿ\", \"ಮೇ\", \"ಜೂನ್\", \"ಜುಲೈ\", \"ಆಗ\", \"ಸೆಪ್ಟೆಂ\", \"ಅಕ್ಟೋ\", \"ನವೆಂ\", \"ಡಿಸೆಂ\"],\n  wide: [\"ಜನವರಿ\", \"ಫೆಬ್ರವರಿ\", \"ಮಾರ್ಚ್\", \"ಏಪ್ರಿಲ್\", \"ಮೇ\", \"ಜೂನ್\", \"ಜುಲೈ\", \"ಆಗಸ್ಟ್\", \"ಸೆಪ್ಟೆಂಬರ್\", \"ಅಕ್ಟೋಬರ್\", \"ನವೆಂಬರ್\", \"ಡಿಸೆಂಬರ್\"]\n};\n\n// CLDR #1718 - #1773\nconst dayValues = {\n  narrow: [\"ಭಾ\", \"ಸೋ\", \"ಮಂ\", \"ಬು\", \"ಗು\", \"ಶು\", \"ಶ\"],\n  short: [\"ಭಾನು\", \"ಸೋಮ\", \"ಮಂಗಳ\", \"ಬುಧ\", \"ಗುರು\", \"ಶುಕ್ರ\", \"ಶನಿ\"],\n  abbreviated: [\"ಭಾನು\", \"ಸೋಮ\", \"ಮಂಗಳ\", \"ಬುಧ\", \"ಗುರು\", \"ಶುಕ್ರ\", \"ಶನಿ\"],\n  wide: [\"ಭಾನುವಾರ\", \"ಸೋಮವಾರ\", \"ಮಂಗಳವಾರ\", \"ಬುಧವಾರ\", \"ಗುರುವಾರ\", \"ಶುಕ್ರವಾರ\", \"ಶನಿವಾರ\"]\n};\n\n// CLDR #1774 - #1815\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾಹ್ನ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾಹ್ನ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  },\n  abbreviated: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  },\n  wide: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ಪೂ\",\n    pm: \"ಅ\",\n    midnight: \"ಮಧ್ಯರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  },\n  abbreviated: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯ ರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  },\n  wide: {\n    am: \"ಪೂರ್ವಾಹ್ನ\",\n    pm: \"ಅಪರಾಹ್ನ\",\n    midnight: \"ಮಧ್ಯ ರಾತ್ರಿ\",\n    noon: \"ಮಧ್ಯಾನ್ಹ\",\n    morning: \"ಬೆಳಗ್ಗೆ\",\n    afternoon: \"ಮಧ್ಯಾನ್ಹ\",\n    evening: \"ಸಂಜೆ\",\n    night: \"ರಾತ್ರಿ\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"ನೇ\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}