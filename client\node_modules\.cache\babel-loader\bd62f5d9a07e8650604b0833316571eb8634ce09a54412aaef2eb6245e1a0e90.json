{"ast": null, "code": "function declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\nfunction buildLocalizeTokenFn(scheme) {\n  return (count, options) => {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return \"за \" + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + \" тому\";\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\nconst halfAtMinute = (_, options) => {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"за півхвилини\";\n    } else {\n      return \"півхвилини тому\";\n    }\n  }\n  return \"півхвилини\";\n};\nconst formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: \"менше секунди\",\n      singularNominative: \"менше {{count}} секунди\",\n      singularGenitive: \"менше {{count}} секунд\",\n      pluralGenitive: \"менше {{count}} секунд\"\n    },\n    future: {\n      one: \"менше, ніж за секунду\",\n      singularNominative: \"менше, ніж за {{count}} секунду\",\n      singularGenitive: \"менше, ніж за {{count}} секунди\",\n      pluralGenitive: \"менше, ніж за {{count}} секунд\"\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} секунда\",\n      singularGenitive: \"{{count}} секунди\",\n      pluralGenitive: \"{{count}} секунд\"\n    },\n    past: {\n      singularNominative: \"{{count}} секунду тому\",\n      singularGenitive: \"{{count}} секунди тому\",\n      pluralGenitive: \"{{count}} секунд тому\"\n    },\n    future: {\n      singularNominative: \"за {{count}} секунду\",\n      singularGenitive: \"за {{count}} секунди\",\n      pluralGenitive: \"за {{count}} секунд\"\n    }\n  }),\n  halfAMinute: halfAtMinute,\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: \"менше хвилини\",\n      singularNominative: \"менше {{count}} хвилини\",\n      singularGenitive: \"менше {{count}} хвилин\",\n      pluralGenitive: \"менше {{count}} хвилин\"\n    },\n    future: {\n      one: \"менше, ніж за хвилину\",\n      singularNominative: \"менше, ніж за {{count}} хвилину\",\n      singularGenitive: \"менше, ніж за {{count}} хвилини\",\n      pluralGenitive: \"менше, ніж за {{count}} хвилин\"\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} хвилина\",\n      singularGenitive: \"{{count}} хвилини\",\n      pluralGenitive: \"{{count}} хвилин\"\n    },\n    past: {\n      singularNominative: \"{{count}} хвилину тому\",\n      singularGenitive: \"{{count}} хвилини тому\",\n      pluralGenitive: \"{{count}} хвилин тому\"\n    },\n    future: {\n      singularNominative: \"за {{count}} хвилину\",\n      singularGenitive: \"за {{count}} хвилини\",\n      pluralGenitive: \"за {{count}} хвилин\"\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} години\",\n      singularGenitive: \"близько {{count}} годин\",\n      pluralGenitive: \"близько {{count}} годин\"\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} годину\",\n      singularGenitive: \"приблизно за {{count}} години\",\n      pluralGenitive: \"приблизно за {{count}} годин\"\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} годину\",\n      singularGenitive: \"{{count}} години\",\n      pluralGenitive: \"{{count}} годин\"\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} день\",\n      singularGenitive: \"{{count}} днi\",\n      pluralGenitive: \"{{count}} днів\"\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} тижня\",\n      singularGenitive: \"близько {{count}} тижнів\",\n      pluralGenitive: \"близько {{count}} тижнів\"\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} тиждень\",\n      singularGenitive: \"приблизно за {{count}} тижні\",\n      pluralGenitive: \"приблизно за {{count}} тижнів\"\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} тиждень\",\n      singularGenitive: \"{{count}} тижні\",\n      pluralGenitive: \"{{count}} тижнів\"\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} місяця\",\n      singularGenitive: \"близько {{count}} місяців\",\n      pluralGenitive: \"близько {{count}} місяців\"\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} місяць\",\n      singularGenitive: \"приблизно за {{count}} місяці\",\n      pluralGenitive: \"приблизно за {{count}} місяців\"\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} місяць\",\n      singularGenitive: \"{{count}} місяці\",\n      pluralGenitive: \"{{count}} місяців\"\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"близько {{count}} року\",\n      singularGenitive: \"близько {{count}} років\",\n      pluralGenitive: \"близько {{count}} років\"\n    },\n    future: {\n      singularNominative: \"приблизно за {{count}} рік\",\n      singularGenitive: \"приблизно за {{count}} роки\",\n      pluralGenitive: \"приблизно за {{count}} років\"\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"{{count}} рік\",\n      singularGenitive: \"{{count}} роки\",\n      pluralGenitive: \"{{count}} років\"\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"більше {{count}} року\",\n      singularGenitive: \"більше {{count}} років\",\n      pluralGenitive: \"більше {{count}} років\"\n    },\n    future: {\n      singularNominative: \"більше, ніж за {{count}} рік\",\n      singularGenitive: \"більше, ніж за {{count}} роки\",\n      pluralGenitive: \"більше, ніж за {{count}} років\"\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: \"майже {{count}} рік\",\n      singularGenitive: \"майже {{count}} роки\",\n      pluralGenitive: \"майже {{count}} років\"\n    },\n    future: {\n      singularNominative: \"майже за {{count}} рік\",\n      singularGenitive: \"майже за {{count}} роки\",\n      pluralGenitive: \"майже за {{count}} років\"\n    }\n  })\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  return formatDistanceLocale[token](count, options);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}