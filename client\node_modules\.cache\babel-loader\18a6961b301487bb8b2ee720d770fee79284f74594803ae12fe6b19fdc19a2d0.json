{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"P\", \"M\"],\n  abbreviated: [\"PK\", \"MK\"],\n  wide: [\"Para Krishtit\", \"<PERSON>bas Krishtit\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"4-mujori I\", \"4-mujori II\", \"4-mujori III\", \"4-mujori IV\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"J\", \"S\", \"M\", \"P\", \"M\", \"Q\", \"K\", \"G\", \"S\", \"T\", \"N\", \"D\"],\n  abbreviated: [\"<PERSON>\", \"Shk\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>r\", \"<PERSON>\", \"<PERSON>ht\", \"Te<PERSON>\", \"Nën\", \"Dhj\"],\n  wide: [\"Janar\", \"Shkurt\", \"Mars\", \"Prill\", \"Maj\", \"Qershor\", \"Korrik\", \"Gusht\", \"Shtator\", \"Tetor\", \"Nëntor\", \"Dhjetor\"]\n};\nconst dayValues = {\n  narrow: [\"D\", \"H\", \"M\", \"M\", \"E\", \"P\", \"S\"],\n  short: [\"Di\", \"Hë\", \"Ma\", \"Më\", \"En\", \"Pr\", \"Sh\"],\n  abbreviated: [\"Die\", \"Hën\", \"Mar\", \"Mër\", \"Enj\", \"Pre\", \"Sht\"],\n  wide: [\"Dielë\", \"Hënë\", \"Martë\", \"Mërkurë\", \"Enjte\", \"Premte\", \"Shtunë\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"p\",\n    pm: \"m\",\n    midnight: \"m\",\n    noon: \"d\",\n    morning: \"mëngjes\",\n    afternoon: \"dite\",\n    evening: \"mbrëmje\",\n    night: \"natë\"\n  },\n  abbreviated: {\n    am: \"PD\",\n    pm: \"MD\",\n    midnight: \"mesnëtë\",\n    noon: \"drek\",\n    morning: \"mëngjes\",\n    afternoon: \"mbasdite\",\n    evening: \"mbrëmje\",\n    night: \"natë\"\n  },\n  wide: {\n    am: \"p.d.\",\n    pm: \"m.d.\",\n    midnight: \"mesnëtë\",\n    noon: \"drek\",\n    morning: \"mëngjes\",\n    afternoon: \"mbasdite\",\n    evening: \"mbrëmje\",\n    night: \"natë\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"p\",\n    pm: \"m\",\n    midnight: \"m\",\n    noon: \"d\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\"\n  },\n  abbreviated: {\n    am: \"PD\",\n    pm: \"MD\",\n    midnight: \"mesnatë\",\n    noon: \"drek\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\"\n  },\n  wide: {\n    am: \"p.d.\",\n    pm: \"m.d.\",\n    midnight: \"mesnatë\",\n    noon: \"drek\",\n    morning: \"në mëngjes\",\n    afternoon: \"në mbasdite\",\n    evening: \"në mbrëmje\",\n    night: \"në mesnatë\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  if (options?.unit === \"hour\") return String(number);\n  if (number === 1) return number + \"-rë\";\n  if (number === 4) return number + \"t\";\n  return number + \"-të\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}