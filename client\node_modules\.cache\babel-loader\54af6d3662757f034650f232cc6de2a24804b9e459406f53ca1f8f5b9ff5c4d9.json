{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"e.m.a\", \"m.a.j\"],\n  abbreviated: [\"e.m.a\", \"m.a.j\"],\n  wide: [\"enne meie ajaarvamist\", \"meie ajaarvamise järgi\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1. kvartal\", \"2. kvartal\", \"3. kvartal\", \"4. kvartal\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"V\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"jaan\", \"veebr\", \"märts\", \"apr\", \"mai\", \"juuni\", \"juuli\", \"aug\", \"sept\", \"okt\", \"nov\", \"dets\"],\n  wide: [\"jaanuar\", \"veebruar\", \"märts\", \"aprill\", \"mai\", \"juuni\", \"juuli\", \"august\", \"september\", \"oktoober\", \"november\", \"detsember\"]\n};\nconst dayValues = {\n  narrow: [\"P\", \"E\", \"T\", \"K\", \"N\", \"R\", \"L\"],\n  short: [\"P\", \"E\", \"T\", \"K\", \"N\", \"R\", \"L\"],\n  abbreviated: [\"pühap.\", \"esmasp.\", \"teisip.\", \"kolmap.\", \"neljap.\", \"reede.\", \"laup.\"],\n  wide: [\"pühapäev\", \"esmaspäev\", \"teisipäev\", \"kolmapäev\", \"neljapäev\", \"reede\", \"laupäev\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"kesköö\",\n    noon: \"keskpäev\",\n    morning: \"hommik\",\n    afternoon: \"pärastlõuna\",\n    evening: \"õhtu\",\n    night: \"öö\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"kesköö\",\n    noon: \"keskpäev\",\n    morning: \"hommik\",\n    afternoon: \"pärastlõuna\",\n    evening: \"õhtu\",\n    night: \"öö\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"kesköö\",\n    noon: \"keskpäev\",\n    morning: \"hommik\",\n    afternoon: \"pärastlõuna\",\n    evening: \"õhtu\",\n    night: \"öö\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"keskööl\",\n    noon: \"keskpäeval\",\n    morning: \"hommikul\",\n    afternoon: \"pärastlõunal\",\n    evening: \"õhtul\",\n    night: \"öösel\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"keskööl\",\n    noon: \"keskpäeval\",\n    morning: \"hommikul\",\n    afternoon: \"pärastlõunal\",\n    evening: \"õhtul\",\n    night: \"öösel\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"keskööl\",\n    noon: \"keskpäeval\",\n    morning: \"hommikul\",\n    afternoon: \"pärastlõunal\",\n    evening: \"õhtul\",\n    night: \"öösel\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: monthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n    formattingValues: dayValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}