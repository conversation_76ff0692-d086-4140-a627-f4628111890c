{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"B\", \"คศ\"],\n  abbreviated: [\"BC\", \"ค.ศ.\"],\n  wide: [\"ปีก่อนคริสตกาล\", \"คริสต์ศักราช\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"ไตรมาสแรก\", \"ไตรมาสที่สอง\", \"ไตรมาสที่สาม\", \"ไตรมาสที่สี่\"]\n};\nconst dayValues = {\n  narrow: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  short: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  abbreviated: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  wide: [\"อาทิตย์\", \"จันทร์\", \"อังคาร\", \"พุธ\", \"พฤหัสบดี\", \"ศุกร์\", \"เสาร์\"]\n};\nconst monthValues = {\n  narrow: [\"ม.ค.\", \"ก.พ.\", \"มี.ค.\", \"เม.ย.\", \"พ.ค.\", \"มิ.ย.\", \"ก.ค.\", \"ส.ค.\", \"ก.ย.\", \"ต.ค.\", \"พ.ย.\", \"ธ.ค.\"],\n  abbreviated: [\"ม.ค.\", \"ก.พ.\", \"มี.ค.\", \"เม.ย.\", \"พ.ค.\", \"มิ.ย.\", \"ก.ค.\", \"ส.ค.\", \"ก.ย.\", \"ต.ค.\", \"พ.ย.\", \"ธ.ค.\"],\n  wide: [\"มกราคม\", \"กุมภาพันธ์\", \"มีนาคม\", \"เมษายน\", \"พฤษภาคม\", \"มิถุนายน\", \"กรกฎาคม\", \"สิงหาคม\", \"กันยายน\", \"ตุลาคม\", \"พฤศจิกายน\", \"ธันวาคม\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\"\n  },\n  abbreviated: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\"\n  },\n  wide: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\"\n  },\n  abbreviated: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\"\n  },\n  wide: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}