{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mindre än en sekund\",\n    other: \"mindre än {{count}} sekunder\"\n  },\n  xSeconds: {\n    one: \"en sekund\",\n    other: \"{{count}} sekunder\"\n  },\n  halfAMinute: \"en halv minut\",\n  lessThanXMinutes: {\n    one: \"mindre än en minut\",\n    other: \"mindre än {{count}} minuter\"\n  },\n  xMinutes: {\n    one: \"en minut\",\n    other: \"{{count}} minuter\"\n  },\n  aboutXHours: {\n    one: \"ungefär en timme\",\n    other: \"ungefär {{count}} timmar\"\n  },\n  xHours: {\n    one: \"en timme\",\n    other: \"{{count}} timmar\"\n  },\n  xDays: {\n    one: \"en dag\",\n    other: \"{{count}} dagar\"\n  },\n  aboutXWeeks: {\n    one: \"ungefär en vecka\",\n    other: \"ungefär {{count}} veckor\"\n  },\n  xWeeks: {\n    one: \"en vecka\",\n    other: \"{{count}} veckor\"\n  },\n  aboutXMonths: {\n    one: \"ungefär en månad\",\n    other: \"ungefär {{count}} månader\"\n  },\n  xMonths: {\n    one: \"en månad\",\n    other: \"{{count}} månader\"\n  },\n  aboutXYears: {\n    one: \"ungefär ett år\",\n    other: \"ungefär {{count}} år\"\n  },\n  xYears: {\n    one: \"ett år\",\n    other: \"{{count}} år\"\n  },\n  overXYears: {\n    one: \"över ett år\",\n    other: \"över {{count}} år\"\n  },\n  almostXYears: {\n    one: \"nästan ett år\",\n    other: \"nästan {{count}} år\"\n  }\n};\nconst wordMapping = [\"noll\", \"en\", \"två\", \"tre\", \"fyra\", \"fem\", \"sex\", \"sju\", \"åtta\", \"nio\", \"tio\", \"elva\", \"tolv\"];\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count < 13 ? wordMapping[count] : String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"om \" + result;\n    } else {\n      return result + \" sedan\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}