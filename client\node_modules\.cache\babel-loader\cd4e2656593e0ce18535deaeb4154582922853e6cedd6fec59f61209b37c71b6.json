{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: date => {\n    switch (date.getDay()) {\n      case 0:\n        return \"'prošle nedelje u' p\";\n      case 3:\n        return \"'prošle srede u' p\";\n      case 6:\n        return \"'prošle subote u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  yesterday: \"'juče u' p\",\n  today: \"'danas u' p\",\n  tomorrow: \"'sutra u' p\",\n  nextWeek: date => {\n    switch (date.getDay()) {\n      case 0:\n        return \"'sledeće nedelje u' p\";\n      case 3:\n        return \"'sledeću sredu u' p\";\n      case 6:\n        return \"'sledeću subotu u' p\";\n      default:\n        return \"'sledeći' EEEE 'u' p\";\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}