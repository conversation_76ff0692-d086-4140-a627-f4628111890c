{"ast": null, "code": "const translations = {\n  about: \"kö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  over: \"több mint\",\n  almost: \"majdnem\",\n  lessthan: \"kevesebb mint\"\n};\nconst withoutSuffixes = {\n  xseconds: \" másodperc\",\n  halfaminute: \"fél perc\",\n  xminutes: \" perc\",\n  xhours: \" óra\",\n  xdays: \" nap\",\n  xweeks: \" hét\",\n  xmonths: \" hónap\",\n  xyears: \" év\"\n};\nconst withSuffixes = {\n  xseconds: {\n    \"-1\": \" másodperccel ezelőtt\",\n    1: \" másodperc múlva\",\n    0: \" másodperce\"\n  },\n  halfaminute: {\n    \"-1\": \"fél perccel ezelőtt\",\n    1: \"fél perc múlva\",\n    0: \"fél perce\"\n  },\n  xminutes: {\n    \"-1\": \" perccel ezelőtt\",\n    1: \" perc múlva\",\n    0: \" perce\"\n  },\n  xhours: {\n    \"-1\": \" ór<PERSON><PERSON> ezel<PERSON>tt\",\n    1: \" óra múlva\",\n    0: \" órája\"\n  },\n  xdays: {\n    \"-1\": \" nappal ezelőtt\",\n    1: \" nap múlva\",\n    0: \" napja\"\n  },\n  xweeks: {\n    \"-1\": \" héttel ezelőtt\",\n    1: \" hét múlva\",\n    0: \" hete\"\n  },\n  xmonths: {\n    \"-1\": \" hónappal ezelőtt\",\n    1: \" hónap múlva\",\n    0: \" hónapja\"\n  },\n  xyears: {\n    \"-1\": \" évvel ezelőtt\",\n    1: \" év múlva\",\n    0: \" éve\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const adverb = token.match(/about|over|almost|lessthan/i);\n  const unit = adverb ? token.replace(adverb[0], \"\") : token;\n  const addSuffix = options?.addSuffix === true;\n  const key = unit.toLowerCase();\n  const comparison = options?.comparison || 0;\n  const translated = addSuffix ? withSuffixes[key][comparison] : withoutSuffixes[key];\n  let result = key === \"halfaminute\" ? translated : count + translated;\n  if (adverb) {\n    const adv = adverb[0].toLowerCase();\n    result = translations[adv] + \" \" + result;\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}