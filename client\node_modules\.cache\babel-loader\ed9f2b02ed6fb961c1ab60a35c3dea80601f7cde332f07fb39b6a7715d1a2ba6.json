{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1秒未満\",\n    other: \"{{count}}秒未満\",\n    oneWithSuffix: \"約1秒\",\n    otherWithSuffix: \"約{{count}}秒\"\n  },\n  xSeconds: {\n    one: \"1秒\",\n    other: \"{{count}}秒\"\n  },\n  halfAMinute: \"30秒\",\n  lessThanXMinutes: {\n    one: \"1分未満\",\n    other: \"{{count}}分未満\",\n    oneWithSuffix: \"約1分\",\n    otherWithSuffix: \"約{{count}}分\"\n  },\n  xMinutes: {\n    one: \"1分\",\n    other: \"{{count}}分\"\n  },\n  aboutXHours: {\n    one: \"約1時間\",\n    other: \"約{{count}}時間\"\n  },\n  xHours: {\n    one: \"1時間\",\n    other: \"{{count}}時間\"\n  },\n  xDays: {\n    one: \"1日\",\n    other: \"{{count}}日\"\n  },\n  aboutXWeeks: {\n    one: \"約1週間\",\n    other: \"約{{count}}週間\"\n  },\n  xWeeks: {\n    one: \"1週間\",\n    other: \"{{count}}週間\"\n  },\n  aboutXMonths: {\n    one: \"約1か月\",\n    other: \"約{{count}}か月\"\n  },\n  xMonths: {\n    one: \"1か月\",\n    other: \"{{count}}か月\"\n  },\n  aboutXYears: {\n    one: \"約1年\",\n    other: \"約{{count}}年\"\n  },\n  xYears: {\n    one: \"1年\",\n    other: \"{{count}}年\"\n  },\n  overXYears: {\n    one: \"1年以上\",\n    other: \"{{count}}年以上\"\n  },\n  almostXYears: {\n    one: \"1年近く\",\n    other: \"{{count}}年近く\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  options = options || {};\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options.addSuffix && tokenValue.oneWithSuffix) {\n      result = tokenValue.oneWithSuffix;\n    } else {\n      result = tokenValue.one;\n    }\n  } else {\n    if (options.addSuffix && tokenValue.otherWithSuffix) {\n      result = tokenValue.otherWithSuffix.replace(\"{{count}}\", String(count));\n    } else {\n      result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"後\";\n    } else {\n      return result + \"前\";\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}