{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nconst weekdays = [\"svētdienā\", \"pirmdienā\", \"otrdien<PERSON>\", \"trešdien<PERSON>\", \"ceturtdienā\", \"piektdienā\", \"sestdienā\"];\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    if (isSameWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    const weekday = weekdays[date.getDay()];\n    return \"'Pagājušā \" + weekday + \" plkst.' p\";\n  },\n  yesterday: \"'<PERSON><PERSON>r plkst.' p\",\n  today: \"'Š<PERSON><PERSON> plkst.' p\",\n  tomorrow: \"'Rīt plkst.' p\",\n  nextWeek: (date, baseDate, options) => {\n    if (isSameWeek(date, baseDate, options)) {\n      return \"eeee 'plkst.' p\";\n    }\n    const weekday = weekdays[date.getDay()];\n    return \"'Nāka<PERSON>jā \" + weekday + \" plkst.' p\";\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}