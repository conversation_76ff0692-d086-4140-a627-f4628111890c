{"ast": null, "code": "// note: no implementation for weeks\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      future: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      past: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\"\n    },\n    other: {\n      default: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      future: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      past: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\"\n    }\n  },\n  xSeconds: {\n    one: {\n      default: \"1 ಸೆಕೆಂಡ್\",\n      future: \"1 ಸೆಕೆಂಡ್‌ನಲ್ಲಿ\",\n      past: \"1 ಸೆಕೆಂಡ್ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ಸೆಕೆಂಡುಗಳು\",\n      future: \"{{count}} ಸೆಕೆಂಡ್‌ಗಳಲ್ಲಿ\",\n      past: \"{{count}} ಸೆಕೆಂಡ್ ಹಿಂದೆ\"\n    }\n  },\n  halfAMinute: {\n    other: {\n      default: \"ಅರ್ಧ ನಿಮಿಷ\",\n      future: \"ಅರ್ಧ ನಿಮಿಷದಲ್ಲಿ\",\n      past: \"ಅರ್ಧ ನಿಮಿಷದ ಹಿಂದೆ\"\n    }\n  },\n  lessThanXMinutes: {\n    one: {\n      default: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      future: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      past: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\"\n    },\n    other: {\n      default: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      future: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      past: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\"\n    }\n  },\n  xMinutes: {\n    one: {\n      default: \"1 ನಿಮಿಷ\",\n      future: \"1 ನಿಮಿಷದಲ್ಲಿ\",\n      past: \"1 ನಿಮಿಷದ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ನಿಮಿಷಗಳು\",\n      future: \"{{count}} ನಿಮಿಷಗಳಲ್ಲಿ\",\n      past: \"{{count}} ನಿಮಿಷಗಳ ಹಿಂದೆ\"\n    }\n  },\n  aboutXHours: {\n    one: {\n      default: \"ಸುಮಾರು 1 ಗಂಟೆ\",\n      future: \"ಸುಮಾರು 1 ಗಂಟೆಯಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ಗಂಟೆ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳ ಹಿಂದೆ\"\n    }\n  },\n  xHours: {\n    one: {\n      default: \"1 ಗಂಟೆ\",\n      future: \"1 ಗಂಟೆಯಲ್ಲಿ\",\n      past: \"1 ಗಂಟೆ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ಗಂಟೆಗಳು\",\n      future: \"{{count}} ಗಂಟೆಗಳಲ್ಲಿ\",\n      past: \"{{count}} ಗಂಟೆಗಳ ಹಿಂದೆ\"\n    }\n  },\n  xDays: {\n    one: {\n      default: \"1 ದಿನ\",\n      future: \"1 ದಿನದಲ್ಲಿ\",\n      past: \"1 ದಿನದ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ದಿನಗಳು\",\n      future: \"{{count}} ದಿನಗಳಲ್ಲಿ\",\n      past: \"{{count}} ದಿನಗಳ ಹಿಂದೆ\"\n    }\n  },\n  // TODO\n  // aboutXWeeks: {},\n\n  // TODO\n  // xWeeks: {},\n\n  aboutXMonths: {\n    one: {\n      default: \"ಸುಮಾರು 1 ತಿಂಗಳು\",\n      future: \"ಸುಮಾರು 1 ತಿಂಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ತಿಂಗಳ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ತಿಂಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ತಿಂಗಳುಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ತಿಂಗಳುಗಳ ಹಿಂದೆ\"\n    }\n  },\n  xMonths: {\n    one: {\n      default: \"1 ತಿಂಗಳು\",\n      future: \"1 ತಿಂಗಳಲ್ಲಿ\",\n      past: \"1 ತಿಂಗಳ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ತಿಂಗಳು\",\n      future: \"{{count}} ತಿಂಗಳುಗಳಲ್ಲಿ\",\n      past: \"{{count}} ತಿಂಗಳುಗಳ ಹಿಂದೆ\"\n    }\n  },\n  aboutXYears: {\n    one: {\n      default: \"ಸುಮಾರು 1 ವರ್ಷ\",\n      future: \"ಸುಮಾರು 1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ವರ್ಷದ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳ ಹಿಂದೆ\"\n    }\n  },\n  xYears: {\n    one: {\n      default: \"1 ವರ್ಷ\",\n      future: \"1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"1 ವರ್ಷದ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ವರ್ಷಗಳು\",\n      future: \"{{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"{{count}} ವರ್ಷಗಳ ಹಿಂದೆ\"\n    }\n  },\n  overXYears: {\n    one: {\n      default: \"1 ವರ್ಷದ ಮೇಲೆ\",\n      future: \"1 ವರ್ಷದ ಮೇಲೆ\",\n      past: \"1 ವರ್ಷದ ಮೇಲೆ\"\n    },\n    other: {\n      default: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n      future: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n      past: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\"\n    }\n  },\n  almostXYears: {\n    one: {\n      default: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n      future: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\"\n    },\n    other: {\n      default: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      future: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\"\n    }\n  }\n};\nfunction getResultByTense(parentToken, options) {\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return parentToken.future;\n    } else {\n      return parentToken.past;\n    }\n  }\n  return parentToken.default;\n}\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (tokenValue.one && count === 1) {\n    result = getResultByTense(tokenValue.one, options);\n  } else {\n    result = getResultByTense(tokenValue.other, options);\n  }\n  return result.replace(\"{{count}}\", String(count));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}