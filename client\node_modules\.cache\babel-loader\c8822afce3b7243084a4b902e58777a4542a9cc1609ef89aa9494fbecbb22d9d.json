{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'passat a' p\",\n  yesterday: \"'ièr a' p\",\n  today: \"'uèi a' p\",\n  tomorrow: \"'deman a' p\",\n  nextWeek: \"eeee 'a' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}