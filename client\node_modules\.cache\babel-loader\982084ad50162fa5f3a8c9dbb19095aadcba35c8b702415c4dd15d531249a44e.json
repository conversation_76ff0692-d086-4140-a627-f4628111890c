{"ast": null, "code": "import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// Stand-alone local day of week\nexport class StandAloneLocalDayParser extends Parser {\n  priority = 90;\n  parse(dateString, token, match, options) {\n    const valueCallback = value => {\n      // We want here floor instead of trunc, so we get -7 for value 0 instead of 0\n      const wholeWeekDays = Math.floor((value - 1) / 7) * 7;\n      return (value + options.weekStartsOn + 6) % 7 + wholeWeekDays;\n    };\n    switch (token) {\n      // 3\n      case \"c\":\n      case \"cc\":\n        // 03\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      // 3rd\n      case \"co\":\n        return mapValue(match.ordinalNumber(dateString, {\n          unit: \"day\"\n        }), valueCallback);\n      // Tue\n      case \"ccc\":\n        return match.day(dateString, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"short\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n\n      // T\n      case \"ccccc\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n      // Tu\n      case \"cccccc\":\n        return match.day(dateString, {\n          width: \"short\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n\n      // Tuesday\n      case \"cccc\":\n      default:\n        return match.day(dateString, {\n          width: \"wide\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"short\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n    }\n  }\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"y\", \"R\", \"u\", \"q\", \"Q\", \"M\", \"L\", \"I\", \"d\", \"D\", \"E\", \"i\", \"e\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}