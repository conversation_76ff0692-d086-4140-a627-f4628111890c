{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"أقل من ثانية\",\n    two: \"أقل من زوز ثواني\",\n    threeToTen: \"أقل من {{count}} ثواني\",\n    other: \"أقل من {{count}} ثانية\"\n  },\n  xSeconds: {\n    one: \"ثانية\",\n    two: \"زوز ثواني\",\n    threeToTen: \"{{count}} ثواني\",\n    other: \"{{count}} ثانية\"\n  },\n  halfAMinute: \"نص دقيقة\",\n  lessThanXMinutes: {\n    one: \"أقل من دقيقة\",\n    two: \"أقل من دقيقتين\",\n    threeToTen: \"أقل من {{count}} دقايق\",\n    other: \"أقل من {{count}} دقيقة\"\n  },\n  xMinutes: {\n    one: \"دقيقة\",\n    two: \"دقيقتين\",\n    threeToTen: \"{{count}} دقايق\",\n    other: \"{{count}} دقيقة\"\n  },\n  aboutXHours: {\n    one: \"ساعة تقريب\",\n    two: \"ساعتين تقريب\",\n    threeToTen: \"{{count}} سوايع تقريب\",\n    other: \"{{count}} ساعة تقريب\"\n  },\n  xHours: {\n    one: \"ساعة\",\n    two: \"ساعتين\",\n    threeToTen: \"{{count}} سوايع\",\n    other: \"{{count}} ساعة\"\n  },\n  xDays: {\n    one: \"نهار\",\n    two: \"نهارين\",\n    threeToTen: \"{{count}} أيام\",\n    other: \"{{count}} يوم\"\n  },\n  aboutXWeeks: {\n    one: \"جمعة تقريب\",\n    two: \"جمعتين تقريب\",\n    threeToTen: \"{{count}} جماع تقريب\",\n    other: \"{{count}} جمعة تقريب\"\n  },\n  xWeeks: {\n    one: \"جمعة\",\n    two: \"جمعتين\",\n    threeToTen: \"{{count}} جماع\",\n    other: \"{{count}} جمعة\"\n  },\n  aboutXMonths: {\n    one: \"شهر تقريب\",\n    two: \"شهرين تقريب\",\n    threeToTen: \"{{count}} أشهرة تقريب\",\n    other: \"{{count}} شهر تقريب\"\n  },\n  xMonths: {\n    one: \"شهر\",\n    two: \"شهرين\",\n    threeToTen: \"{{count}} أشهرة\",\n    other: \"{{count}} شهر\"\n  },\n  aboutXYears: {\n    one: \"عام تقريب\",\n    two: \"عامين تقريب\",\n    threeToTen: \"{{count}} أعوام تقريب\",\n    other: \"{{count}} عام تقريب\"\n  },\n  xYears: {\n    one: \"عام\",\n    two: \"عامين\",\n    threeToTen: \"{{count}} أعوام\",\n    other: \"{{count}} عام\"\n  },\n  overXYears: {\n    one: \"أكثر من عام\",\n    two: \"أكثر من عامين\",\n    threeToTen: \"أكثر من {{count}} أعوام\",\n    other: \"أكثر من {{count}} عام\"\n  },\n  almostXYears: {\n    one: \"عام تقريب\",\n    two: \"عامين تقريب\",\n    threeToTen: \"{{count}} أعوام تقريب\",\n    other: \"{{count}} عام تقريب\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const usageGroup = formatDistanceLocale[token];\n  let result;\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace(\"{{count}}\", String(count));\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"في \" + result;\n    } else {\n      return \"عندو \" + result;\n    }\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}