{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل الميلاد\", \"بعد الميلاد\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ر1\", \"ر2\", \"ر3\", \"ر4\"],\n  wide: [\"الربع الأول\", \"الربع الثاني\", \"الربع الثالث\", \"الربع الرابع\"]\n};\nconst monthValues = {\n  narrow: [\"ج\", \"ف\", \"م\", \"أ\", \"م\", \"ج\", \"ج\", \"أ\", \"س\", \"أ\", \"ن\", \"د\"],\n  abbreviated: [\"جانـ\", \"فيفـ\", \"مارس\", \"أفريل\", \"مايـ\", \"جوانـ\", \"جويـ\", \"أوت\", \"سبتـ\", \"أكتـ\", \"نوفـ\", \"ديسـ\"],\n  wide: [\"جانفي\", \"فيفري\", \"مارس\", \"أفريل\", \"ماي\", \"جوان\", \"جويلية\", \"أوت\", \"سبتمبر\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"]\n};\nconst dayValues = {\n  narrow: [\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"],\n  short: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n  abbreviated: [\"أحد\", \"اثنـ\", \"ثلا\", \"أربـ\", \"خميـ\", \"جمعة\", \"سبت\"],\n  wide: [\"الأحد\", \"الاثنين\", \"الثلاثاء\", \"الأربعاء\", \"الخميس\", \"الجمعة\", \"السبت\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\"\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\"\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"في الصباح\",\n    afternoon: \"بعد الظـهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\"\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"في الصباح\",\n    afternoon: \"بعد الظهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\"\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظـهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\"\n  }\n};\nconst ordinalNumber = dirtyNumber => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => Number(quarter) - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}