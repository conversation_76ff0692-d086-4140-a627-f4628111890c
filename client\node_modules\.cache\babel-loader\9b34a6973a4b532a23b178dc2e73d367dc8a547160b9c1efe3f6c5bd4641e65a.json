{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\nimport { subDays } from \"./subDays.js\";\n\n/**\n * The {@link isYesterday} function options.\n */\n\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is yesterday\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\nexport function isYesterday(date, options) {\n  return isSameDay(constructFrom(options?.in || date, date), subDays(constructNow(options?.in || date), 1));\n}\n\n// Fallback for modularized imports:\nexport default isYesterday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}