{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"Q\", \"W\"],\n  abbreviated: [\"QK\", \"WK\"],\n  wide: [\"qabe<PERSON>\", \"wara <PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1. kwart\", \"2. kwart\", \"3. kwart\", \"4. kwart\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"Ġ\", \"L\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"Jan\", \"Fra\", \"Mar\", \"Apr\", \"Mej\", \"Ġun\", \"Lul\", \"Aww\", \"Set\", \"Ott\", \"Nov\", \"Diċ\"],\n  wide: [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"April\", \"<PERSON>j<PERSON>\", \"Ġunju\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>wwiss<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>em<PERSON><PERSON>\", \"Diċembru\"]\n};\nconst dayValues = {\n  narrow: [\"Ħ\", \"T\", \"T\", \"E\", \"Ħ\", \"Ġ\", \"S\"],\n  short: [\"Ħa\", \"Tn\", \"Tl\", \"Er\", \"Ħa\", \"Ġi\", \"Si\"],\n  abbreviated: [\"Ħad\", \"Tne\", \"Tli\", \"Erb\", \"Ħam\", \"Ġim\", \"Sib\"],\n  wide: [\"Il-Ħadd\", \"It-Tnejn\", \"It-Tlieta\", \"L-Erbgħa\", \"Il-Ħamis\", \"Il-Ġimgħa\", \"Is-Sibt\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"nofsillejl\",\n    noon: \"nofsinhar\",\n    morning: \"għodwa\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"lejl\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"f'nofsillejl\",\n    noon: \"f'nofsinhar\",\n    morning: \"filgħodu\",\n    afternoon: \"wara nofsinhar\",\n    evening: \"filgħaxija\",\n    night: \"billejl\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \"º\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}