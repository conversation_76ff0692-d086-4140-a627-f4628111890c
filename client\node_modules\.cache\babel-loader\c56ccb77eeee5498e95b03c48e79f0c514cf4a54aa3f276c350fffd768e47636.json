{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /\\d+/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^(нтө|нт)/i,\n  abbreviated: /^(нтө|нт)/i,\n  wide: /^(нийтийн тооллын өмнө|нийтийн тооллын)/i\n};\nconst parseEraPatterns = {\n  any: [/^(нтө|нийтийн тооллын өмнө)/i, /^(нт|нийтийн тооллын)/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^(iv|iii|ii|i)/i,\n  abbreviated: /^(iv|iii|ii|i) улирал/i,\n  wide: /^[1-4]-р улирал/i\n};\nconst parseQuarterPatterns = {\n  any: [/^(i(\\s|$)|1)/i, /^(ii(\\s|$)|2)/i, /^(iii(\\s|$)|3)/i, /^(iv(\\s|$)|4)/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^(xii|xi|x|ix|viii|vii|vi|v|iv|iii|ii|i)/i,\n  abbreviated: /^(1-р сар|2-р сар|3-р сар|4-р сар|5-р сар|6-р сар|7-р сар|8-р сар|9-р сар|10-р сар|11-р сар|12-р сар)/i,\n  wide: /^(нэгдүгээр сар|хоёрдугаар сар|гуравдугаар сар|дөрөвдүгээр сар|тавдугаар сар|зургаадугаар сар|долоодугаар сар|наймдугаар сар|есдүгээр сар|аравдугаар сар|арван нэгдүгээр сар|арван хоёрдугаар сар)/i\n};\nconst parseMonthPatterns = {\n  narrow: [/^i$/i, /^ii$/i, /^iii$/i, /^iv$/i, /^v$/i, /^vi$/i, /^vii$/i, /^viii$/i, /^ix$/i, /^x$/i, /^xi$/i, /^xii$/i],\n  any: [/^(1|нэгдүгээр)/i, /^(2|хоёрдугаар)/i, /^(3|гуравдугаар)/i, /^(4|дөрөвдүгээр)/i, /^(5|тавдугаар)/i, /^(6|зургаадугаар)/i, /^(7|долоодугаар)/i, /^(8|наймдугаар)/i, /^(9|есдүгээр)/i, /^(10|аравдугаар)/i, /^(11|арван нэгдүгээр)/i, /^(12|арван хоёрдугаар)/i]\n};\nconst matchDayPatterns = {\n  narrow: /^[ндмлпбб]/i,\n  short: /^(ня|да|мя|лх|пү|ба|бя)/i,\n  abbreviated: /^(ням|дав|мяг|лха|пүр|баа|бям)/i,\n  wide: /^(ням|даваа|мягмар|лхагва|пүрэв|баасан|бямба)/i\n};\nconst parseDayPatterns = {\n  narrow: [/^н/i, /^д/i, /^м/i, /^л/i, /^п/i, /^б/i, /^б/i],\n  any: [/^ня/i, /^да/i, /^мя/i, /^лх/i, /^пү/i, /^ба/i, /^бя/i]\n};\nconst matchDayPeriodPatterns = {\n  narrow: /^(ү\\.ө\\.|ү\\.х\\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i,\n  any: /^(ү\\.ө\\.|ү\\.х\\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ү\\.ө\\./i,\n    pm: /^ү\\.х\\./i,\n    midnight: /^шөнө дунд/i,\n    noon: /^үд дунд/i,\n    morning: /өглөө/i,\n    afternoon: /өдөр/i,\n    evening: /орой/i,\n    night: /шөнө/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}